using Client.Common.Configs;
using Client.Common.Configs.Components;
using Client.Common.Ftue.Infrastructure;
using Client.Common.Network.MetaNet;
using Client.Common.Player.Controllers;
using Client.Common.Player.Timers;
using Common;
using ItemType = Common.ItemType;
using TimerType = Client.Common.Network.MetaNet.TimerType;

namespace Client.Common.Ftue.Helpers
{
    public class FtueConditions
    {
        private readonly ConfigService _configService;
        private readonly FtueProgress _ftueProgress;
        private readonly PlayerManager _playerManager;

        public FtueConditions(ConfigService configService, FtueProgress ftue, PlayerManager playerManager)
        {
            _configService = configService;
            _ftueProgress = ftue;
            _playerManager = playerManager;
        }

        public const string PLAYER_NAME_FOR_TUTORIAL_REGION = "";
        public const string BOT_NAME_FOR_TUTORIAL_BATTLES = "";

        public const string PLAYER_COUNTRY_FOR_TUTORIAL_REGION = "";
        public const string BOT_COUNTRY_FOR_TUTORIAL_BATTLES = "";

        public const int BOT_RANK_FOR_REGION0 = 0;

        public bool IsItemUnlocked(int itemId)
        {
            return _playerManager.Session.Progress.BloodProgress >= _configService.GetByKey<ProgressStepData>(itemId.ToString()).Step;
        }

        public bool IsFirstShurikenUnlocked => IsItemUnlocked(PlayerInventory.Items.FIRST_CRAFTED_SHURIKEN);

        public bool IsFirstAmuletUnlocked => IsItemUnlocked(PlayerInventory.Items.FIRST_AMULET_ID);

        public bool IsFirstPowerStoneUnlocked => IsItemUnlocked(PlayerInventory.Items.FIRST_POWER_STONE);

        public bool IsFirstShurikenSkinUnlocked => IsItemUnlocked(PlayerInventory.Items.FIRST_SHURIKEN_SKIN_ID);

        public bool IsFirstOutfitUnlocked => IsItemUnlocked(PlayerInventory.Items.FIRST_OUTFIT_ID);

        public bool IsFirstConsumableHookUnlocked => IsItemUnlocked(PlayerInventory.Items.FIRST_CONSUMABLE_HOOK);

        public bool IsFirstConsumableBombUnlocked => IsItemUnlocked(PlayerInventory.Items.FIRST_CONSUMABLE_BOMB);

        public bool IsHealBottleUnlocked => IsItemUnlocked(PlayerInventory.Items.BOTTLE_FOR_HEAL);

        public bool IsFirstShurikenCrafted => _playerManager.Session.Inventory.GetItemCount(PlayerInventory.Items.FIRST_CRAFTED_SHURIKEN) > 0;
        public bool IsFirstAmuletCrafted => _playerManager.Session.Inventory.GetItemCount(PlayerInventory.Items.FIRST_AMULET_ID) > 0;

        public bool CanOpenChests()
        {
            return HasItemsOfType(ItemType.Lockpick) &&
                   (
                       HasItemsOfType(ItemType.OutfitClothChest) ||
                       HasItemsOfType(ItemType.ShurikenSkinPaintChest)
                   );
        }

        public bool CanCompleteFirstShurikenCraft()
        {
            return _playerManager.Session.Timers.GetTimer(PlayerInventory.Items.FIRST_CRAFTED_SHURIKEN, global::Common.TimerType.Craft) != null;
        }

        public bool PlayerHasTrauma()
        {
            return _playerManager.Session.Timers.GetTimers(PlayerTimers.Query.Get().WithTimerType(global::Common.TimerType.Trauma)).Count > 0;
        }

        public bool CanEquipFirstShuriken()
        {
            return _playerManager.Session.Inventory.GetItemCount(PlayerInventory.Items.FIRST_CRAFTED_SHURIKEN) > 0 &&
                   _playerManager.Session.Progress.WeaponId != PlayerInventory.Items.FIRST_CRAFTED_SHURIKEN;
        }


        public bool IsFirstShurikenEquipped()
        {
            return _playerManager.Session.Progress.WeaponId == PlayerInventory.Items.FIRST_CRAFTED_SHURIKEN;
        }

        public bool IsTraumaFeatureEnabled()
        {
            //the feature works as expected, but traumas are only added by the ftue flow.
            return _ftueProgress.IsCompleted(AccountStep.AlchemistCraftTutorialFinished);
        }

        public bool IsTraumaCalculationEnabled()
        {
            //allows trauma calculation and application
            return _ftueProgress.IsCompleted(AccountStep.AlchemistTraumaTutorialFinished);
        }

        public bool IsQteEnabled()
        {
            return _ftueProgress.IsCompleted(AccountStep.QteTutorialFinished);
        }

        public bool IsShieldParryTutorialAvailable()
        {
            return _playerManager.Session.Progress.BloodProgress >= 6;
        }

        public bool IsShieldParryEnabled()
        {
            return _ftueProgress.IsCompleted(AccountStep.ParryTutorialFinished);
        }

        private bool HasItemsOfType(ItemType contentType)
        {
            return _playerManager.Session.Inventory.GetItemsByType(contentType).Count > 0;
        }

        public bool IsPowerStonesEnabled()
        {
            return _ftueProgress.IsCompleted(AccountStep.PowerStonesTutorialFinished);
        }

        public bool IsLoadoutUnlocked()
        {
            return IsFirstPowerStoneUnlocked;
        }
    }
}