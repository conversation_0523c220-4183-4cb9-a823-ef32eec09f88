using Client.Common.Audio;
using Client.Common.Components.Equip;
using Client.Common.Player.Controllers;
using Client.Utils.ECS.LocalWorld;
using Client.Utils.Haptics;
using FMODUnity;

namespace Client.Common.Controllers.EquipHelper
{
    public class ShurikenItemEquipper : IItemEquipper
    {
        private readonly PlayerProgress _playerProgress;
        private readonly LocalEcsWorld _world;
        private readonly HapticWrapper _haptics;
        private readonly EventReference _equipAudioEvent;
        private readonly EquipperHelper _equipperHelper;
        private int _previousEquippedItemId;

        public ShurikenItemEquipper(PlayerProgress playerProgress, LocalEcsWorld world, HapticWrapper haptics, EventReference equipAudioEvent)
        {
            _playerProgress = playerProgress;
            _world = world;
            _equipAudioEvent = equipAudioEvent;
            _haptics = haptics;
            _equipperHelper = new EquipperHelper(_world);
        }

        public void OnEquip(int itemId)
        {
            _previousEquippedItemId = _playerProgress.WeaponId;
            
            AudioSystemCommon.TaskPlayAudioOneshot(_world, _equipAudioEvent);
            _haptics.Play(HapticWrapper.Presets.Middle);
        }

        public void OnEquipped(int itemId)
        {
            _equipperHelper.Equip(itemId, _previousEquippedItemId);
        }
    }
}