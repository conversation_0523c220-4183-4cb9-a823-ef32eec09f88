using System.Collections.Generic;
using System.Threading;
using System.Threading.Tasks;
using Client.Common.Network.MetaNet;
using Client.Common.Player.Controllers;
using Client.Common.UI.InputLockService;
using Client.Utils.GameLogger;
using Client.Utils.ResultTool.Results;
using Common;
using Cysharp.Threading.Tasks;

namespace Client.Common.Controllers.EquipHelper
{
    public class ItemEquipHelper
    {
        private readonly MetaNet _metaNet;
        private readonly CancellationToken _cancellationToken;
        private readonly FullScreenLocker _screenLocker;

        private readonly Dictionary<ItemType, IItemEquipper> _equippersMap;

        public ItemEquipHelper(MetaNet metaNet, FullScreenLocker screenLocker, Dictionary<ItemType,IItemEquipper> equippersMap, CancellationToken cancellationToken)
        {
            _metaNet = metaNet;
            _screenLocker = screenLocker;
            _equippersMap = equippersMap;
            _cancellationToken = cancellationToken;
        }

        public async UniTask<bool> TryEquip(int itemId, ItemType itemType)
        {
            if (!_equippersMap.TryGetValue(itemType, out IItemEquipper equipper))
            {
                GameLogger.LogWarning($"[{nameof(ItemEquipHelper)} You probably want to make a custom equipper for {itemType}.]");
            }
            
            equipper?.OnEquip(itemId);
            if (await TryRequestEquip(itemId))
            {
                equipper?.OnEquipped(itemId);
                return true;
            }

            return false;
        }

        // todo: move to EquipPocketCommand
        public async UniTask<bool> TryEquipPocket(int itemId, uint count, ItemType itemType)
        {
            if (!_equippersMap.TryGetValue(itemType, out IItemEquipper equipper))
            {
                GameLogger.LogWarning($"[{nameof(ItemEquipHelper)} You probably want to make a custom equipper for {itemType}.]");
            }
            
            equipper?.OnEquip(itemId);
            if (await TryRequestEquipPocket(itemId, count))
            {
                equipper?.OnEquipped(itemId);
                return true;
            }

            return false;
        }
        
        private async Task<bool> TryRequestEquip(int itemId)
        {
            _screenLocker.Lock(_cancellationToken);
            Result<ClientItemEquipResponse> equipItem = await _metaNet.EquipItem(itemId, _cancellationToken);
            _screenLocker.Unlock(_cancellationToken);
            return equipItem.IsSuccess;
        }
        
        private async Task<bool> TryRequestEquipPocket(int itemId, uint count)
        {
             _screenLocker.Lock(_cancellationToken);
            Result equipItem = await _metaNet.EquipPocket(new ItemQuantity(itemId, count), _cancellationToken);
            _screenLocker.Unlock(_cancellationToken);
            return equipItem.IsSuccess;
        }
    }
}