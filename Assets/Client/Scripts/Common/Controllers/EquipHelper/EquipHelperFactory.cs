using System.Collections.Generic;
using System.Threading;
using Client.Common.Audio.Banks;
using Client.Common.Network.MetaNet;
using Client.Common.Player.Controllers;
using Client.Common.UI.InputLockService;
using Client.Utils.ECS.LocalWorld;
using Client.Utils.Haptics;
using Common;

namespace Client.Common.Controllers.EquipHelper
{
    public class EquipHelperFactory
    {
        private readonly MetaNet _metaNet;
        private readonly PlayerSession _playerSession;
        private readonly PlayerManager _playerManager;
        private readonly CancellationToken _cancellationToken;
        private readonly FullScreenLocker _screenLocker;
        private readonly LocalEcsWorld _world;
        private readonly BazarAudio _bazarAudio;
        private readonly HapticWrapper _haptics;

        public EquipHelperFactory(MetaNet metaNet, PlayerSession playerSession, FullScreenLocker screenLocker, BazarAudio bazarAudio, LocalEcsWorld world, HapticWrapper haptics,
            CancellationToken cancellationToken)
        {
            _cancellationToken = cancellationToken;
            _metaNet = metaNet;
            _playerSession = playerSession;
            _screenLocker = screenLocker;
            _bazarAudio = bazarAudio;
            _haptics = haptics;
            _world = world;
        }

        public ItemEquipHelper Create()
        {
            Dictionary<ItemType, IItemEquipper> equippersMap = new()
            {
                { ItemType.Amulet, new AmuletItemEquipper(_playerSession.Progress, _world, _haptics, _bazarAudio.EquipAmulet) },
                { ItemType.Shuriken, new ShurikenItemEquipper(_playerSession.Progress, _world, _haptics, _bazarAudio.EquipShuriken) },
                { ItemType.ShurikenSkinPaint, new ShurikenSkinPaintEquipper(_playerSession.Progress, _world, _haptics, _bazarAudio.EquipShuriken) },
                { ItemType.Outfit, new OutfitEquipper(_playerSession.Progress, _world, _haptics, _bazarAudio.EquipShuriken) },
                { ItemType.PowerStone, new ItemEquipperBase(_world, _haptics, _bazarAudio.EquipShuriken) },
                { ItemType.Hook, new ItemEquipperBase(_world, _haptics, _bazarAudio.EquipShuriken) },
                { ItemType.Bomb, new ItemEquipperBase(_world, _haptics, _bazarAudio.EquipShuriken) },
            };
            
            return new ItemEquipHelper(_metaNet, _screenLocker, equippersMap, _cancellationToken);
        }
    }
}