using Client.Utils.ConfigHelper.Abstractions;
using Client.Utils.ConfigHelper.Implementations;
using Client.Utils.GameLogger;
using Common;

namespace Client.Common.Durability.DurabilityStorage.WeaponSkins
{
    public class BrokenWeaponSkinLocalStorage : IBrokenWeaponSkinStorage
    {
        private const string _STORAGE_KEY_ = "weapon.skin.broken.data.{0}";
        
        private readonly ConfigHelper<WeaponSkin> _storage;

        public BrokenWeaponSkinLocalStorage()
        {
            _storage = new PlayerPrefsConfigHelper<WeaponSkin>(_STORAGE_KEY_);
        }
        
        public bool TryGetSkinData(int weaponId, out WeaponSkin weaponSkin)
        {
            return _storage.TryLoad(out weaponSkin, weaponId.ToString());
        }

        public WeaponSkin GetSkinData(int weaponId)
        {
            if (TryGetSkinData(weaponId, out WeaponSkin weaponSkin))
            {
                return weaponSkin;
            }
            
            GameLogger.LogWarning($"No skin found for the weaponId {weaponId}");

            return default;
        }

        public void SetSkinData(WeaponSkin weaponSkin)
        {
            _storage.Save(weaponSkin, weaponSkin.WeaponId.ToString());
        }
    }
}