using Client.Common.Durability.DurabilityStorage;
using Client.Common.Durability.DurabilityStorage.WeaponSkins;
using Client.Common.NpcTab.Data.TabTypes;
using Client.Common.ProfileShurikens.Item;
using Client.Common.ProfileShurikens.Providers;
using Client.Common.SceneLoading;
using Client.Common.SceneLoading.SceneData.Implementations;
using Client.Utils.Fsm;
using Common;
using Leopotam.Localization;

namespace Client.Common.ProfileShurikens.Screen.StateMachine.States
{
    public class GoToBlacksmithState : State
    {
        private readonly ProfileShurikenScreenView _view;
        private readonly AnimatedSceneLoader _sceneLoader;
        private readonly IBrokenWeaponSkinStorage _brokenWeaponSkinStorage;
        private readonly PreferredSkinIdProvider _preferredSkinProvider;
        private readonly CsvLocalization _localization;

        private ShurikenItemPresenter _shuriken;
        private int _preferredSkinId;

        public GoToBlacksmithState(
            ProfileShurikenScreenView view, 
            AnimatedSceneLoader sceneLoader, 
            IBrokenWeaponSkinStorage brokenWeaponSkinStorage,
            PreferredSkinIdProvider preferredSkinProvider,
            CsvLocalization localization,
            string name = "") : base(name)
        {
            _view = view;
            _sceneLoader = sceneLoader;
            _brokenWeaponSkinStorage = brokenWeaponSkinStorage;
            _preferredSkinProvider = preferredSkinProvider;
            _localization = localization;
        }

        public void SetShuriken(ShurikenItemPresenter shuriken)
        {
            _shuriken = shuriken;
        }
        
        protected override void OnEnable()
        {
            if (_brokenWeaponSkinStorage.TryGetSkinData(_shuriken.Model.ItemId, out WeaponSkin lastBrokenSkin))
            {
                if (_preferredSkinProvider.TryGet((int)lastBrokenSkin.Id, out _preferredSkinId))
                {
                    _view.GoToBlacksmithButtonVfx.ShowAnimated();
                }
            }
            
            _view.GoToBlacksmithButton.SetText(_localization.Get("profile.shurikens.goToSmith"));
            _view.SetGoToBlacksmithAvailable(true);

            _view.GoToBlacksmithClicked += GoToBlacksmith;
        }

        protected override void OnDisable()
        {
            _view.GoToBlacksmithButtonVfx.HideAnimated();
            _view.SetGoToBlacksmithAvailable(false);
            _view.GoToBlacksmithClicked -= GoToBlacksmith;
        }

        private void GoToBlacksmith()
        {
            if (_preferredSkinId != -1)
            {
                _sceneLoader.Load(new BlacksmithSceneLoadData(_preferredSkinId));
            }
            else
            {
                _sceneLoader.Load(new BlacksmithSceneLoadData(BlacksmithTabType.Skin));
            }            
        }
    }
}