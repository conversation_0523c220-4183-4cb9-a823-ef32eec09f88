using System.Collections.Generic;
using System.Linq;
using System.Threading;
using Client.Common.Abilities.Data;
using Client.Common.Abilities.Infrastructure.Data;
using Client.Common.Abilities.Infrastructure.Loaders;
using Client.Common.Audio;
using Client.Common.Audio.Banks;
using Client.Common.Configs;
using Client.Common.Configs.Components;
using Client.Common.Configs.Components.Abilities;
using Client.Common.Configs.Components.Items;
using Client.Common.Configs.Components.Items.TypeMarkers;
using Client.Common.Controllers.EquipHelper;
using Client.Common.CSV;
using Client.Common.Durability;
using Client.Common.Ftue.Infrastructure;
using Client.Common.ItemParts;
using Client.Common.Items;
using Client.Common.Items.Icons;
using Client.Common.Network.MetaNet;
using Client.Common.NpcTab.Controllers;
using Client.Common.Palette.Item;
using Client.Common.Palette.Panel;
using Client.Common.Player.Controllers;
using Client.Common.Profile.Ability.Circle;
using Client.Common.Profile.Ability.Circle.Presenter;
using Client.Common.Profile.Ability.GradePanel;
using Client.Common.Profile.Ability.Grading;
using Client.Common.Profile.Ability.Popup;
using Client.Common.Profile.Ability.Popup.Presenter;
using Client.Common.Profile.Placeholder;
using Client.Common.ProfileShurikens.DurabilityBlock;
using Client.Common.ProfileShurikens.Item;
using Client.Common.ProfileShurikens.Paint;
using Client.Common.ProfileShurikens.ParamPopup;
using Client.Common.ProfileShurikens.Providers;
using Client.Common.ProfileShurikens.Screen;
using Client.Common.ProfileShurikens.Screen.StateMachine;
using Client.Common.ProfileShurikens.Scroll;
using Client.Common.ProfileShurikens.Visual;
using Client.Common.SceneLoading;
using Client.Common.SceneLoading.SceneData.Implementations;
using Client.Common.UI.InputLockService;
using Client.Common.UI.ScrollController.Controllers;
using Client.Utils.CustomTweens.VFXService.Abstractions;
using Client.Utils.ECS.LocalWorld;
using Client.Utils.ResourceLoading;
using Client.Utils.ViewService;
using Common;
using Cysharp.Threading.Tasks;
using Leopotam.Ecs;
using Leopotam.Localization;
using UnityEngine;

namespace Client.Common.ProfileShurikens
{
    public class ProfileShurikenScreenFactory
    {
        public AbilityPopupPresenter AbilityPopupPresenter { get; private set; }
        
        private readonly LocalEcsWorld _world;
        private readonly ConfigService _configService;
        private readonly ViewService _viewService;
        private readonly PopupViewService _popupViewService;
        private readonly ResourceLoadingService _resourceLoadingService;
        private readonly PlayerManager _playerManager;
        private readonly ItemData _itemData;
        private readonly CsvLocalization _localization;
        private readonly ProfileShurikensSceneLoadData _sceneData;
        private readonly MetaNet _metaNet;
        private readonly FullScreenLocker _fullScreenLocker;
        private readonly BazarAudio _bazarAudio;
        private readonly AnimatedSceneLoader _sceneLoader;
        private readonly ItemEquipHelper _equipHelper;
        private readonly AbilityGradeService _abilityGradeService;
        private readonly FtueProgress _ftueProgress;
        private readonly QuantifiedIconLoadingService _quantifiedIconLoadingService;
        private readonly GoogleDocsData _googleDocsData;
        private readonly AbilityByItemProvider _abilityByItemProvider;
        private readonly DurabilityService _durabilityService;

        private readonly ITweenManager _tweenManager;
        private readonly Transform _screenRoot;
        private readonly Transform _palettePanelRoot;
        private readonly Transform _abilityPopupRoot;
        private readonly Transform _paramsPopupRoot;
        private readonly ParamsVisual _paramsVisual;
        
        private EcsFilter<ItemIdData, ConfigComponent, ResourceConfigsData, ShurikenItemMarker> _itemConfigs;

        private ItemPresenterFactory _itemPresenterFactory;
        private AbilityGradePanelFactory _gradePanelFactory;
        private ShurikenItemPresenterFactory _shurikenItemPresenterFactory;
        private PalettePanelFactory _palettePanelFactory;

        private CancellationToken _cancellationToken;
        private ProfileShurikenScreenPresenter _screenPresenter;

        private PlayerInventory PlayerInventory => _playerManager.Session.Inventory;
        private PlayerProgress PlayerProgress => _playerManager.Session.Progress;

        public ProfileShurikenScreenFactory(
            LocalEcsWorld world,
            ConfigService configService,
            ViewService viewService,
            PopupViewService popupViewService,
            ResourceLoadingService resourceLoadingService,
            PlayerManager playerManager,
            ItemData itemData,
            CsvLocalization localization,
            ProfileShurikensSceneLoadData sceneData,
            MetaNet metaNet,
            FullScreenLocker fullScreenLocker,
            BazarAudio bazarAudio,
            AnimatedSceneLoader sceneLoader,
            ItemEquipHelper equipHelper,
            AbilityGradeService abilityGradeService,
            FtueProgress ftueProgress,
            QuantifiedIconLoadingService quantifiedIconLoadingService,
            GoogleDocsData googleDocsData,
            AbilityByItemProvider abilityByItemProvider,
            DurabilityService durabilityService,
            ITweenManager tweenManager,
            Transform screenRoot,
            Transform palettePanelRoot,
            Transform abilityPopupRoot,
            Transform paramsPopupRoot,
            ParamsVisual paramsVisual)
        {
            _world = world;
            _configService = configService;
            _viewService = viewService;
            _popupViewService = popupViewService;
            _resourceLoadingService = resourceLoadingService;
            _playerManager = playerManager;
            _itemData = itemData;
            _localization = localization;
            _sceneData = sceneData;
            _metaNet = metaNet;
            _fullScreenLocker = fullScreenLocker;
            _bazarAudio = bazarAudio;
            _sceneLoader = sceneLoader;
            _equipHelper = equipHelper;
            _abilityGradeService = abilityGradeService;
            _ftueProgress = ftueProgress;
            _quantifiedIconLoadingService = quantifiedIconLoadingService;
            _googleDocsData = googleDocsData;
            _abilityByItemProvider = abilityByItemProvider;
            _durabilityService = durabilityService;
            _tweenManager = tweenManager;
            _screenRoot = screenRoot;
            _palettePanelRoot = palettePanelRoot;
            _abilityPopupRoot = abilityPopupRoot;
            _paramsPopupRoot = paramsPopupRoot;
            _paramsVisual = paramsVisual;
        }

        public async UniTask<ProfileShurikenScreenPresenter> Create(CancellationToken cancellationToken)
        {
            _cancellationToken = cancellationToken;
            _itemConfigs = _configService.GetFilter<EcsFilter<ItemIdData, ConfigComponent, ResourceConfigsData, ShurikenItemMarker>>();

            _shurikenItemPresenterFactory = new ShurikenItemPresenterFactory(_world, _configService, _viewService, _resourceLoadingService,
                                                                             PlayerProgress.WeaponStorage, PlayerProgress.BrokenWeaponSkinStorage,
                                                                             _durabilityService, _bazarAudio, _playerManager, _quantifiedIconLoadingService);
            _itemPresenterFactory = new ItemPresenterFactory(_configService, _viewService, _resourceLoadingService);
            _gradePanelFactory = new AbilityGradePanelFactory(_resourceLoadingService, _tweenManager, _viewService);

            ShurikenProfilePaletteItemFactory profilePaletteItemFactory = new(_configService, _viewService, _playerManager, _resourceLoadingService, _localization, _googleDocsData);
            _palettePanelFactory = new PalettePanelFactory(_playerManager, _configService, _viewService, _localization, profilePaletteItemFactory);
            _palettePanelFactory.Init(_palettePanelRoot);

            return await CreateScreen(_cancellationToken);
        }

        private async UniTask<ProfileShurikenScreenPresenter> CreateScreen(CancellationToken cancellationToken)
        {
            ResourceConfig screenResourceConfig = new()
            {
                Path = "UI/Profile/Shurikens/ProfileShurikenScreen",
                LoadFrom = LoadFromType.Ressources,
            };

            ProfileShurikenScrollFactory scrollFactory = await CreateScrollFactory(cancellationToken);
            DurabilityBlockController durabilityBlockController = CreateDurabilityBlockController();
            PaintController paintController = CreatePaintController();
            PaintWithPaletteFlowController paintWithPaletteController = CreatePaintWithPaletteFlowController(cancellationToken);
            ParamsPlatePresenter paramsPlatePresenter = CreateParamsPlatePresenter();
            AbilityCirclePresenter abilityCirclePresenter = await CreateAbility(cancellationToken);
            ProfileShurikenScreenFsm screenFsm = CreateScreenFsm(cancellationToken);

            _screenPresenter = await CreateScreenPresenter(cancellationToken);
            await _viewService.Open<ProfileShurikenScreenPresenter, ProfileShurikenScreenView, ProfileShurikenScreenModel>(
                _screenPresenter, screenResourceConfig, _screenRoot, cancellationToken: cancellationToken);
            paintWithPaletteController.SetScreenView(_screenPresenter.View);

            if (_sceneData.AbilityIdToOpen != AbilityId.None)
            {
                await AbilityPopupPresenter.ShowAt(_sceneData.AbilityIdToOpen, _cancellationToken);
            }
            
            return _screenPresenter;

            async UniTask<ProfileShurikenScrollFactory> CreateScrollFactory(CancellationToken token)
            {
                ScrollGenerator scrollGenerator = await new ScrollGenerator().InitAsync(_resourceLoadingService, token);
                return new ProfileShurikenScrollFactory(scrollGenerator, token);
            }

            DurabilityBlockController CreateDurabilityBlockController()
            {
                return new DurabilityBlockController(_configService, PlayerProgress, _resourceLoadingService, _localization, _durabilityService);
            }

            PaintController CreatePaintController()
            {
                PaintRequestController paintRequestController = new(_metaNet);
                return new PaintController(_viewService, _localization, paintRequestController, _fullScreenLocker, _durabilityService, durabilityBlockController);
            }

            PaintWithPaletteFlowController CreatePaintWithPaletteFlowController(CancellationToken token)
            {
                return new PaintWithPaletteFlowController(_viewService, paintController, _palettePanelFactory, _fullScreenLocker, token);
            }

            ParamsPlatePresenter CreateParamsPlatePresenter()
            {
                return new ParamsPlatePresenter(_paramsVisual);
            }

            async UniTask<AbilityCirclePresenter> CreateAbility(CancellationToken token)
            {
                (string[] abilityConfigKeys, string[] itemConfigKeys) = GetAbilityConfigKeys();
                AbilityUpgradeAvailabilityProvider upgradeAvailabilityProvider = new(_configService, _playerManager.Session.Inventory, _abilityGradeService);
                if (abilityConfigKeys is not {Length: > 0} || !_ftueProgress.IsCompleted(AccountStep.RpTutorialFinished) && !upgradeAvailabilityProvider.IsAnyAbilityUpgradeAvailable())
                {
                    return null;
                }

                AbilityCircleFactory abilityCircleFactory = new(_configService, _viewService, _resourceLoadingService, _tweenManager, _abilityGradeService);
                AbilityCirclePresenter circlePresenter = await abilityCircleFactory.Create(abilityConfigKeys, _screenRoot, token);

                BankTransferController bankTransferController = new(_sceneLoader, _popupViewService, _localization, _playerManager.Session.Inventory);
                AbilityUpgradeController upgradeController = new(_metaNet, _world, _playerManager.Session.Inventory, bankTransferController, _fullScreenLocker, _localization,
                                                                 _popupViewService, _abilityGradeService);
                
                AbilityPopupFactory abilityPopupFactory = new(_configService, _popupViewService, _resourceLoadingService, _localization, _playerManager.Session,
                                                              _itemPresenterFactory, _gradePanelFactory, upgradeController, _abilityGradeService, 
                                                              _abilityByItemProvider, new UISoundHelper(_world));
                
                AbilityPopupPresenter = await abilityPopupFactory.Create(abilityConfigKeys, itemConfigKeys, _abilityPopupRoot, token);
                AbilityPopupPresenter.PopupClosed += OnAbilityPopupPresenterClosed;
                AbilityPopupPresenter.AbilityScrolled += OnAbilityPopupScrolled;

                return (circlePresenter);
            }

            ProfileShurikenScreenFsm CreateScreenFsm(CancellationToken token)
            {
                ItemPartsChecker itemPartsChecker = new(_configService, PlayerInventory);
                PreferredSkinIdProvider preferredSkinProvider = new(_configService, itemPartsChecker);

                return new ProfileShurikenScreenFsm(
                    PlayerProgress, PlayerInventory, _localization,
                    _equipHelper, _sceneLoader, _sceneData,
                    preferredSkinProvider, paintController,
                    paintWithPaletteController, _ftueProgress, token);
            }

            async UniTask<ProfileShurikenScreenPresenter> CreateScreenPresenter(CancellationToken token)
            {
                List<ShurikenItemPresenter> itemPresenters = (await CreateShurikenItemPresenters(token)).ToList();
                ItemPlaceholderPresenter placeholderPresenter = await _shurikenItemPresenterFactory.CreatePlaceholder(null, token);

                ProfileShurikenScreenModel screenModel = new(_sceneData.ItemIdToOpen, itemPresenters, placeholderPresenter, abilityCirclePresenter);
                AbilityDescriptionFormatter descriptionFormatter = new(_configService, _localization, _abilityGradeService);
                ProfileShurikenScreenPresenter presenter = new(
                    screenModel, _playerManager, screenFsm, _localization, scrollFactory, durabilityBlockController, paintController, paintWithPaletteController, 
                    _viewService, _tweenManager, paramsPlatePresenter, _fullScreenLocker, _paramsPopupRoot, token, descriptionFormatter, new UISoundHelper(_world));
                
                presenter.CloseClicked += () => _sceneLoader.BackInHistory();
                presenter.AbilityCircleClicked += OnAbilityCircleClicked;
                return presenter;
            }
        }

        private void OnAbilityCircleClicked(AbilityId abilityId)
        {
            AbilityPopupPresenter.ShowAt(abilityId, _cancellationToken).Forget();
        }

        private void OnAbilityPopupScrolled(AbilityId abilityId)
        {
            _sceneData.AbilityIdToOpen = abilityId;
        }

        private void OnAbilityPopupPresenterClosed(AbilityId abilityId)
        {
            _sceneData.AbilityIdToOpen = AbilityId.None;
            _screenPresenter?.JumpTo(abilityId);
        }

        private (string[] abilityKeys, string[] itemKeys) GetAbilityConfigKeys()
        {
            List<string> abilityConfigKeys = new();
            List<string> itemConfigKeys = new();

            foreach (int index in _itemConfigs)
            {
                int itemId = _itemConfigs.Get1(index).Id;

                if (!PlayerInventory.Contains(itemId)) //todo: Improve PlayerInventory, so it can provide configKey.
                {
                    continue;
                }

                EcsEntity itemEntity = _itemConfigs.GetEntity(index);
                if (!itemEntity.Has<AbilityKeys>())
                {
                    continue;
                }

                AbilityKeys abilityKeys = itemEntity.Get<AbilityKeys>();
                abilityConfigKeys.AddRange(abilityKeys.Keys);

                itemConfigKeys.Add(_itemConfigs.Get2(index).Key);
            }

            return (abilityConfigKeys.ToArray(), itemConfigKeys.ToArray());
        }

        private UniTask<ShurikenItemPresenter[]> CreateShurikenItemPresenters(CancellationToken cancellationToken)
        {
            List<UniTask<ShurikenItemPresenter>> createTasks = new();

            foreach (int i in _itemConfigs)
            {
                int itemId = _itemConfigs.Get1(i).Id;

                if (PlayerInventory.Contains(itemId)) //todo: Improve PlayerInventory, so it can provide configKey.
                {
                    string configKey = _itemConfigs.Get2(i).Key;
                    UniTask<ShurikenItemPresenter> presenterPrepareTask = _shurikenItemPresenterFactory.CreateItem(configKey, null, cancellationToken);

                    createTasks.Add(presenterPrepareTask);
                }
            }

            return UniTask.WhenAll(createTasks);
        }
    }
}