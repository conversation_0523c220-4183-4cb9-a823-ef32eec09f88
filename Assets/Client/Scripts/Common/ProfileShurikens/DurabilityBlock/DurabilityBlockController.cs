using System;
using System.Linq;
using System.Threading;
using Client.Common.Configs;
using Client.Common.Configs.Components;
using Client.Common.Configs.Components.Items;
using Client.Common.Configs.Data;
using Client.Common.Configs.Extensions;
using Client.Common.Durability;
using Client.Common.Durability.DurabilityBlock;
using Client.Common.Network.MetaNet;
using Client.Common.Player.Controllers;
using Client.Common.Player.Data;
using Client.Common.ProfileShurikens.Data;
using Client.Common.ProfileShurikens.Item;
using Client.Utils.ResourceLoading;
using Cysharp.Threading.Tasks;
using Leopotam.Localization;
using UnityEngine;
using WeaponSkin = Common.WeaponSkin;

namespace Client.Common.ProfileShurikens.DurabilityBlock
{
    public class DurabilityBlockController
    {
        private readonly ConfigService _configService;
        private readonly PlayerProgress _playerProgress;
        private readonly ResourceLoadingService _resourceLoadingService;
        private readonly CsvLocalization _localization;
        private readonly DurabilityService _durabilityService;
        private DurabilityBlockView _view;

        public event Action Clicked;

        public DurabilityBlockController(
            ConfigService configService,
            PlayerProgress playerProgress,
            ResourceLoadingService resourceLoadingService,
            CsvLocalization localization,
            DurabilityService durabilityService)
        {
            _configService = configService;
            _playerProgress = playerProgress;
            _resourceLoadingService = resourceLoadingService;
            _localization = localization;
            _durabilityService = durabilityService;
        }

        public void Init(DurabilityBlockView view)
        {
            _view = view;
            _view.PaintClicked += () => Clicked?.Invoke();
        }

        public async UniTask Update(ShurikenItemModel shurikenItemModel)
        {
            if (TryGetWeaponWithSkin(shurikenItemModel, out Weapon weapon))
            {
                await UpdatePaintState(weapon);
            }
            else
            {
                UpdateLabelState();
            }
        }

        public async UniTask UpdateItemAnimated(ShurikenItemModel shurikenItemModel, CancellationToken cancellationToken)
        {
            if (TryGetWeaponWithSkin(shurikenItemModel, out Weapon weapon))
            {
                await UpdatePaintStateAnimated(weapon, cancellationToken);
            }
            else
            {
                UpdateLabelState();
            }
        }

        private bool TryGetWeaponWithSkin(ShurikenItemModel shurikenItemModel, out Weapon weapon)
        {
            bool isWeaponGet = _playerProgress.WeaponStorage.TryGet(shurikenItemModel.ItemId, out weapon);
            if( _playerProgress.BrokenWeaponSkinStorage.TryGetSkinData(shurikenItemModel.ItemId, out WeaponSkin brokenSkin) &&
                !_playerProgress.WeaponStorage.IsWeaponHasSkin(weapon))
            {
                weapon.Skin.Id = brokenSkin.Id;
            }

            return isWeaponGet && _playerProgress.WeaponStorage.IsWeaponHasSkin(weapon);
        }

        private async UniTask UpdatePaintState(Weapon weapon)
        {
            SetPaintText(weapon);

            RectTransform componentAsset = await GetComponentAsset(weapon);
            _view.SetStateItem(componentAsset);
        }

        private async UniTask UpdatePaintStateAnimated(Weapon weapon, CancellationToken cancellationToken)
        {
            SetPaintText(weapon);

            RectTransform componentAsset = await GetComponentAsset(weapon);
            await _view.ChangeItemTo(componentAsset, cancellationToken);
        }

        private async UniTask<RectTransform> GetComponentAsset(Weapon weapon)
        {
            ItemPartsData itemPartsData = _configService.GetByItemId<ItemPartsData>((int)weapon.Skin.Id);
            string componentKey = itemPartsData.ConfigKeyToDataMap.ElementAt(0).Key;

            ResourceConfig componentResource = _configService.GetByKey<ResourceConfigsData>(componentKey).Get(PathType2d.ItemIcon);
            RectTransform componentAsset = await _resourceLoadingService.Get(componentResource).LoadUniAsync<RectTransform>();

            return componentAsset;
        }

        private void SetPaintText(Weapon weapon)
        {
            string text = _localization.Get(LocalIdents.Localization.DURABILITY);
            uint durabilityPercent = _durabilityService.GetWeaponSkinDurabilityPercent(weapon.Id, (int)weapon.Skin.Id);
            _view.SetText(text, (int)durabilityPercent);
        }

        private void UpdateLabelState()
        {
            _view.SetStateLabel();

            string text = _localization.Get(LocalIdents.Localization.DURABILITY_ABSENT);
            _view.SetText(text);
        }
    }
}