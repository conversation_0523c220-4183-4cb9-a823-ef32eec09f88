using System;
using System.Threading;
using System.Collections.Generic;
using System.Linq;
using Client.Common.Abilities.Infrastructure.Data;
using Client.Common.Audio.Banks;
using Client.Common.Configs;
using Client.Common.Configs.Components;
using Client.Common.Configs.Components.Abilities;
using Client.Common.Configs.Components.Items;
using Client.Common.Configs.Components.Items.TypeMarkers;
using Client.Common.Configs.Data;
using Client.Common.Durability;
using Client.Common.Durability.DurabilityStorage.WeaponSkins;
using Client.Common.Items.Icons;
using Client.Common.Network.MetaNet;
using Client.Common.Player.Controllers;
using Client.Common.Player.Data;
using Client.Common.Player.ProgressItems;
using Client.Common.Profile.Placeholder;
using Client.Common.ProfileShurikens.Data;
using Client.Common.UI.GraphicTools;
using Client.Utils.ECS.LocalWorld;
using Client.Utils.ResourceLoading;
using Client.Utils.ViewService.Core;
using Cysharp.Threading.Tasks;
using Leopotam.Ecs;
using UnityEngine;
using Object = UnityEngine.Object;
using WeaponSkin = Common.WeaponSkin;

namespace Client.Common.ProfileShurikens.Item
{
    public class ShurikenItemPresenterFactory
    {
        private readonly LocalEcsWorld _world;
        private readonly ConfigService _configService;
        private readonly PlayerManager _playerManager;
        private readonly ViewServiceBase _viewService;
        private readonly ResourceLoadingService _resourceLoadingService;
        private readonly PlayerWeaponStorage _weaponStorage;
        private readonly IBrokenWeaponSkinStorage _brokenWeaponSkinStorage;
        private readonly DurabilityService _durabilityService;
        private readonly BazarAudio _bazarAudio;
        private readonly QuantifiedIconLoadingService _quantifiedIconLoadingService;

        public ShurikenItemPresenterFactory(
            LocalEcsWorld world,
            ConfigService configService,
            ViewServiceBase viewService,
            ResourceLoadingService resourceLoadingService,
            PlayerWeaponStorage weaponStorage,
            IBrokenWeaponSkinStorage brokenWeaponSkinStorage,
            DurabilityService durabilityService,
            BazarAudio bazarAudio,
            PlayerManager playerManager,
            QuantifiedIconLoadingService quantifiedIconLoadingService)
        {
            _world = world;
            _configService = configService;
            _viewService = viewService;
            _resourceLoadingService = resourceLoadingService;
            _weaponStorage = weaponStorage;
            _brokenWeaponSkinStorage = brokenWeaponSkinStorage;
            _durabilityService = durabilityService;
            _bazarAudio = bazarAudio;
            _playerManager = playerManager;
            _quantifiedIconLoadingService = quantifiedIconLoadingService;
        }

        public async UniTask<ShurikenItemPresenter> CreateItem(string configKey, Transform root, CancellationToken cancellationToken = default)
        {
            ResourceConfig resourceConfig = new()
            {
                Path = "UI/Profile/Shurikens/ShurikenItem",
                LoadFrom = LoadFromType.Ressources
            };

            Dictionary<int, ResourceConfig> skinIdToSkinResourceMap = GetSkinResourceMap();

            ShurikenItemModel itemModel = await CreateItemModel(configKey, cancellationToken);
            ShurikenItemPresenter itemPresenter = new(itemModel, _world, _resourceLoadingService, skinIdToSkinResourceMap, cancellationToken);

            await _viewService.Prepare<ShurikenItemPresenter, ShurikenItemView, ShurikenItemModel>(itemPresenter, resourceConfig, root, cancellationToken: cancellationToken);

            return itemPresenter;
        }

        public async UniTask<ShurikenItemPresenter> CreateLoadoutItem(string configKey, int currentSkinId, Transform root, CancellationToken cancellationToken = default)
        {
            ResourceConfig resourceConfig = new()
            {
                Path = "UI/Profile/Shurikens/ShurikenItem",
                LoadFrom = LoadFromType.Ressources
            };

            Dictionary<int, ResourceConfig> skinIdToSkinResourceMap = GetSkinResourceMap();

            ShurikenItemModel itemModel = await CreateItemModel(configKey, cancellationToken, currentSkinId);
            ShurikenItemPresenter itemPresenter = new(itemModel, _world, _resourceLoadingService, skinIdToSkinResourceMap, cancellationToken);

            await _viewService.Prepare<ShurikenItemPresenter, ShurikenItemView, ShurikenItemModel>(itemPresenter, resourceConfig, root, cancellationToken: cancellationToken);

            return itemPresenter;
        }

        public async UniTask<ItemPlaceholderPresenter> CreatePlaceholder(Transform root, CancellationToken cancellationToken = default)
        {
            ResourceConfig resourceConfig = new()
            {
                Path = "UI/Profile/Shurikens/ShurikenPlaceholder",
                LoadFrom = LoadFromType.Ressources
            };

            ChildGraphicsHolder placeholderAsset = await _resourceLoadingService.Get(resourceConfig).LoadUniAsync<ChildGraphicsHolder>(cancellationToken);

            ItemPlaceholderModel model = new(LocalIdents.Localization.PLACEHOLDER_NAME, LocalIdents.Localization.PLACEHOLDER_DESC);
            RectTransform view = Object.Instantiate(placeholderAsset, root).GetComponent<RectTransform>();
            ItemPlaceholderPresenter presenter = new(model, view);

            return presenter;
        }

        private async UniTask<ShurikenItemModel> CreateItemModel(string configKey, CancellationToken cancellationToken, int currentSkinId = -1)
        {
            ResourceConfigsData resourceConfigsData = _configService.GetByKey<ResourceConfigsData>(configKey);

            ChildGraphicsHolder iconAsset = await LoadIconAsset(resourceConfigsData, cancellationToken);

            int itemId = _configService.GetByKey<ItemIdData>(configKey).Id;
            string nameKey = _configService.GetByKey<NameKey>(configKey).Key;
            string descriptionKey = _configService.GetByKey<DescriptionKey>(configKey).Key;
            int bloodToStep = GetBloodToStep(configKey);

            float damage = _configService.GetByKey<DamageData>(configKey).Damage;
            float staminaConsumption = _configService.GetByKey<StaminaConsumptionData>(configKey).StaminaConsumption;
            float staminaRecovery = _configService.GetByKey<StaminaRecoveryData>(configKey).WeakRecoveryTime;

            (int skinId, int skinDurabilityPercent) = GetSkinData(itemId);
            int availableCount = _playerManager.Session.Inventory.GetItemCount(currentSkinId);
            ProgressItems.Item item = _playerManager.Session.ProgressItems.GetForCurrentRegion().FirstOrDefault(el => el.ItemId == currentSkinId);

            GameObject asset = null;
            string skinNameKey = String.Empty;
            if (item.PieceId > 0)
            {
                skinNameKey = _configService.GetByKey<NameKey>(currentSkinId.ToString()).Key;
                asset = await _quantifiedIconLoadingService.FromResources(item.PieceId).LoadUniAsync<GameObject>(cancellationToken);
            }

            PieceData pieceData = new()
            {
                ComponentIcon = asset,
                MaxCount = item.PiecesTotal,
                CurrentCount = item.PiecesCurrent,
                PartId = item.PieceId
            };
            
            AbilityId abilityId = GetAbilityId(configKey);

            ShurikenItemModel itemModel = new(
                itemId,
                nameKey,
                descriptionKey,
                iconAsset,
                currentSkinId == -1 ? skinId : currentSkinId,
                currentSkinId == -1 ? skinDurabilityPercent : 100,
                bloodToStep,
                damage,
                staminaConsumption,
                staminaRecovery,
                abilityId,
                _bazarAudio.PaintShuriken,
                pieceData,
                availableCount,
                skinNameKey);

            return itemModel;
        }

        private UniTask<ChildGraphicsHolder> LoadIconAsset(ResourceConfigsData resourceConfigsData, CancellationToken cancellationToken)
        {
            ResourceConfig iconResourceConfig = resourceConfigsData.Get(PathType2d.ItemIcon);

            return _resourceLoadingService.Get(iconResourceConfig).LoadUniAsync<ChildGraphicsHolder>(cancellationToken);
        }

        private Dictionary<int, ResourceConfig> GetSkinResourceMap()
        {
            var shurikenSkinConfigs = _configService.GetFilter<EcsFilter<ItemIdData, ConfigComponent, ResourceConfigsData, ShurikenSkinItemMarker>>();
            Dictionary<int, ResourceConfig> result = new();

            foreach (int index in shurikenSkinConfigs)
            {
                int itemId = shurikenSkinConfigs.Get1(index).Id;
                ResourceConfig resourceConfig = shurikenSkinConfigs.Get3(index).Get(PathType2d.SkinTexture);

                result.Add(itemId, resourceConfig);
            }

            return result;
        }

        private int GetBloodToStep(string configKey)
        {
            if (!_configService.TryGetByKey(configKey, out ProgressStepData stepData))
            {
                return 0;
            }
            
            return stepData.BloodToStep;
        }

        private AbilityId GetAbilityId(string configKey)
        {
            if (!_configService.TryGetByKey(configKey, out AbilityKeys abilityKeys))
            {
                return AbilityId.None;
            }
            
            string firstAbilityKey = abilityKeys.Keys[0];

            return _configService.GetByKey<AbilityIdData>(firstAbilityKey).Id;
        }

        private (int skinId, int skinDurabilityPercent) GetSkinData(int itemId)
        {
            _weaponStorage.TryGet(itemId, out Weapon weapon);
            int skinId = _weaponStorage.GetSkinId(itemId);
            int skinDurabilityPercent = (int)_durabilityService.GetWeaponSkinDurabilityPercent(itemId, skinId);

            if (_brokenWeaponSkinStorage.TryGetSkinData(itemId, out WeaponSkin brokenSkin) &&
                !_weaponStorage.IsWeaponHasSkin(weapon))
            {
                skinId = (int)brokenSkin.Id;
                skinDurabilityPercent = 0;
            }

            return (skinId, skinDurabilityPercent);
        }
    }
}