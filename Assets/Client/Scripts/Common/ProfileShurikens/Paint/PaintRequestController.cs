using System.Threading;
using Client.Common.Items;
using Client.Common.Network.MetaNet;
using Client.Common.Player.Controllers;
using Client.Utils.GameLogger;
using Client.Utils.ResultTool.Results;
using Cysharp.Threading.Tasks;
using Google.Protobuf.WellKnownTypes;

namespace Client.Common.ProfileShurikens.Paint
{
    public class PaintRequestController
    {
        private readonly MetaNet _metaNet;

        public PaintRequestController(MetaNet metaNet)
        {
            _metaNet = metaNet;
        }

        public async UniTask<Result> EquipPaint(int itemId, int skinId, CancellationToken cancellationToken)
        {
            Result equipWeaponSkinResult = await _metaNet.EquipWeaponSkin(itemId, skinId, cancellationToken);

            if (equipWeaponSkinResult.IsSuccess)
            {
                return Result.Success();
            }
            
            GameLogger.LogError($"Failed to apply paint {skinId} to shuriken {itemId}");
            return Result.Failure(equipWeaponSkinResult.Error);
        }
    }
}