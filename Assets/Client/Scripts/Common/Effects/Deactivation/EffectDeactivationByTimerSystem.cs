using System.Collections.Generic;
using Client.Common.Components.Common;
using Client.Common.Configs;
using Client.Common.Configs.Components;
using Client.Common.Configs.Components.Effects;
using Client.Common.Configs.Extensions;
using Client.Common.Effects.Lifecycle.Components;
using Client.Common.Effects.Lifecycle.Controller;
using Client.Common.Network.MetaNet;
using Client.Common.NpcTab.Timer;
using Leopotam.Ecs;
using TimerType = Common.TimerType;

namespace Client.Common.Effects.Deactivation
{
    public class EffectDeactivationByTimerSystem : IEcsRunSystem
    {
        private readonly EffectsController _effectsController = default;
        private readonly ConfigService _configService = default;

        private readonly EcsFilter<TimerEndEvent> _timerEnds = default;
        private readonly EcsFilter<EffectIdData, TargetEntityHolder, ActiveMarker, TimerEffectMarker> _activeEffects;

        public void Run()
        {
            ProcessServerTimerEnd();
        }

        private void ProcessServerTimerEnd()
        {
            foreach (int index in _timerEnds)
            {
                TimerEndEvent timerEndEvent = _timerEnds.Get1(index);
                if (timerEndEvent.Type == TimerType.Boost)
                {
                    List<string> effectKeys = _configService.GetByItemId<EffectKeysData>(timerEndEvent.ItemId).Keys;

                    TryDeactivateEffects(effectKeys);
                }
            }
        }

        private void TryDeactivateEffects(ICollection<string> effectKeys)
        {
            foreach (int index in _activeEffects)
            {
                EffectIdData effectIdData = _activeEffects.Get1(index);
                
                if (effectKeys.Contains(effectIdData.ToConfigKey()))
                {
                    EcsEntity effectEntity = _activeEffects.GetEntity(index);
                    _effectsController.Deactivate(effectEntity);
                }
            }
        }
    }
}