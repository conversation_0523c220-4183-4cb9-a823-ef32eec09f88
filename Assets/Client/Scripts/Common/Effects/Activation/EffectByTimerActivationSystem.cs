using System.Collections.Generic;
using Client.Common.Components.Unit;
using Client.Common.Components.Unit;
using Client.Common.Configs;
using Client.Common.Configs.Components;
using Client.Common.Configs.Components.Effects;
using Client.Common.Configs.Extensions;
using Client.Common.Effects.Lifecycle.Components;
using Client.Common.Effects.Lifecycle.Controller;
using Client.Common.Network.MetaNet;
using Client.Common.NpcTab.Timer;
using Client.Common.Player.Controllers;
using Client.Common.Player.Timers;
using Leopotam.Ecs;
using TimerType = Common.TimerType;

namespace Client.Common.Effects.Activation
{
    public class EffectByTimerActivationSystem<TScopeMarker> : IEcsRunSystem, IEcsInitSystem
        where TScopeMarker : struct
    {
        private readonly EffectsController _effectsController = default;
        private readonly ConfigService _configService = default;
        private readonly PlayerManager _playerManager = default;

        private readonly EcsFilter<PlayerFlag, Unit> _players = default;
        private readonly EcsFilter<TimerStartEvent> _timerStarts = default;

        public void Init()
        {
            List<PlayerTimer> boostTimers = _playerManager.Session.Timers.GetTimers(PlayerTimers.Query.Get().WithTimerType(global::Common.TimerType.Boost));

            foreach (PlayerTimer timer in boostTimers)
            {
                List<string> effectKeys = _configService.GetByItemId<EffectKeysData>(timer.ItemId).Keys;
                ActivateEffects(effectKeys, timer);
            }
        }

        public void Run()
        {
            foreach (int index in _timerStarts)
            {
                TimerStartEvent timerStartEvent = _timerStarts.Get1(index);
                if (timerStartEvent.Type == TimerType.Boost)
                {
                    PlayerTimer timer = _playerManager.Session.Timers.GetTimer(timerStartEvent.ItemId, timerStartEvent.Type);
                    List<string> effectKeys = _configService.GetByItemId<EffectKeysData>(timerStartEvent.ItemId).Keys;
                    ActivateEffects(effectKeys, timer);
                }
            }
        }

        private void ActivateEffects(List<string> effectKeys, PlayerTimer timer)
        {
            var effectsFilter = _configService.GetFilter<EcsFilter<EffectIdData, ConfigComponent, TScopeMarker>>();
            foreach (int index in effectsFilter)
            {
                string configKey = effectsFilter.Get2(index).Key;
                if (effectKeys.Contains(configKey))
                {
                    EffectIdData effectIdData = effectsFilter.Get1(index);
                    EcsEntity effectEntity = _effectsController.Activate(effectIdData, GetPlayer());

                    effectEntity.Get<TimerEffectMarker>();
                    effectEntity.Get<EffectDurationData>().Construct(timer.Duration);
                    effectEntity.Get<EffectCurrentDuration>().Seconds = timer.Duration - timer.GetRemainingUtcTimeInSeconds();
                }
            }
        }

        private EcsEntity GetPlayer()
        {
            return _players.GetEntity(0);
        }
    }
}