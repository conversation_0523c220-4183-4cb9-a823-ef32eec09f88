using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading;
using Client.Common.Items;
using Client.Common.Network.MetaNet;
using Client.Utils.Extensions;
using Client.Utils.ResultTool.Results;
using Common;
using Cysharp.Threading.Tasks;

namespace Client.Common.CraftReadyItems
{
    public class CraftReadyItemsService
    {
        public HashSet<int> ReadyItems { get; private set; } //right now we can only have one ready item of each id at a time 
        
        private readonly MetaNet _metaNet;
        private readonly ItemData _itemData;
        
        private readonly HashSet<ItemType> _autoTakeTypes = new();

        public event Action<int> ItemTaken;

        public CraftReadyItemsService(MetaNet metaNet, ItemData itemData)
        {
            _metaNet = metaNet;
            _itemData = itemData;
            SetAutoTake(ItemType.Bottle);
        }

        public void SetAutoTake(ItemType itemType)
        {
            _autoTakeTypes.Add(itemType);
        }

        public async UniTask<Result> Update(CancellationToken cancellationToken = default)
        {
            Result<ClientCraftedItemsResponse> getCraftedItems = await _metaNet.GetCraftedItems(cancellationToken);
            if (getCraftedItems.IsFailure)
            {
                ReadyItems = new HashSet<int>();
                return Result.Failure(getCraftedItems.Error);
            }

            ReadyItems = getCraftedItems.Value.Items.Select(item => item.Id).ToHashSet();
            return Result.Success();
        }

        public async UniTask<Result> AutoTakeItems(CancellationToken cancellationToken)
        {
            var takeProcesses = new List<UniTask<Result>>();

            HashSet<int> itemsToTake = new HashSet<int>();
            foreach (int itemId in ReadyItems)
            {
                ClientItemData item = _itemData.GetItemDataById(itemId);
                if (_autoTakeTypes.Contains((ItemType) item.Type))
                {
                    itemsToTake.Add(itemId);
                    takeProcesses.Add(TakeCraftedItem(itemId, cancellationToken));
                }
            }

            if (takeProcesses.Count <= 0)
            {
                return Result.Success();
            }

            ReadyItems.ExceptWith(itemsToTake);
            
            Result[] takeResults = await UniTask.WhenAll(takeProcesses);

            return ResultX.IsAnyFailed(takeResults, out Result failedResult) ? failedResult : Result.Success();
        }

        public async UniTask<Result> TakeCraftedItem(int itemId, CancellationToken cancellationToken = default)
        {
            Result<ClientTakeCraftedItemResponse> takeResult = await _metaNet.TakeCraftedItem(itemId, cancellationToken);
            if (!takeResult.IsSuccess)
            {
                return Result.Failure(takeResult.Error);
            }
            ItemTaken?.Invoke(itemId);
            return takeResult.IsSuccess ? Result.Success() : Result.Failure(takeResult.Error);
        }
    }
}