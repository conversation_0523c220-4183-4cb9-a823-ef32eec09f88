using System.Threading;
using Client.Utils.ECS.ECSAsyncInitSystem;
using Cysharp.Threading.Tasks;
using Leopotam.Ecs;

namespace Client.Common.CraftReadyItems
{
    public class CraftReadyItemsUpdateSystem : IEcsAsyncInitSystem, IEcsRunSystem
    {
        private readonly CraftReadyItemsService _craftReadyItemsService;

        private bool _autoTakeComplete;
        private CancellationToken _cancellationToken;
        
        public UniTask InitAsync(CancellationToken cancellationToken)
        {
            _cancellationToken = cancellationToken;
            return _craftReadyItemsService.Update(cancellationToken);
        }

        public void Run()
        {
            if (_autoTakeComplete)
            {
                return;
            }
            _craftReadyItemsService.AutoTakeItems(_cancellationToken).Forget();
            _autoTakeComplete = true;
        }
    }
}