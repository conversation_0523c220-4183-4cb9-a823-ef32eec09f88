using System.Collections.Generic;
using Client.Common.Boosters.Data;
using Client.Common.Configs.Components;
using Client.Common.Configs.Components.Items;
using Client.Common.Items;
using Client.Common.Items.Icons;
using Client.Common.Network.MetaNet;
using Client.Common.Network.Utils;
using Client.Common.Player.Controllers;
using Client.Common.Player.Timers;
using Client.Utils.ECS.LocalWorld;
using Common;
using Leopotam.Ecs;
using UnityEngine;
using ItemQuantity = Client.Common.Network.MetaNet.ItemQuantity;
using ItemType = Common.ItemType;
using TimerType = Client.Common.Network.MetaNet.TimerType;

namespace Client.Common.UI.Rewards.RewardsObsolete.Generators
{
    public class RewardConfigItemGenerator : AbstractRewardConfigGenerator
    {
        private readonly PlayerSession _playerSession;
        private readonly QuantifiedIconLoadingService _quantifiedIconLoadingService;

        public RewardConfigItemGenerator(
            LocalEcsWorld world, 
            ItemData itemData, 
            PlayerSession playerSession, 
            QuantifiedIconLoadingService quantifiedIconLoadingService) : base(world)
        {
            ItemData = itemData;
            _playerSession = playerSession;
            _quantifiedIconLoadingService = quantifiedIconLoadingService;
        }

        protected override bool IsValid(RewardRequest request)
        {
            return request.ConnectionType switch
            {
                ConnectionType.Online  => IsValidOnline(request),
                ConnectionType.Offline => IsValidOffline(request),
            };
        }

        private bool IsValidOnline(RewardRequest request)
        {
            int inventoryItemId = ItemData.GetItemById(request.Item.Id);

            if (inventoryItemId == -1)
            {
#if DEBUG
                if (IsEmptyBloodReward(request.Item.Id, request.BloodType))
                {
                    return false;
                }
                
                Debug.LogWarning($"cant find {request.Item.Id} in inventory");
#endif
                return false;
            }

            return true;
        }

        private bool IsValidOffline(RewardRequest request)
        {
            bool isValid = false;

            _quantifiedIconLoadingService.FromResources(request.Item.Id, request.Item.Count).Load<GameObject>(
                asset =>
                {
                    if (asset == null)
                    {
#if DEBUG
                        if (IsEmptyBloodReward(request.Item.Id, request.BloodType))
                        {
                            isValid = false;
                            return;
                        }

                        Debug.LogWarning($"cant find {request.Item.Id} in assets");
#endif
                        isValid = false;
                        return;
                    }

                    isValid = true;
                });

            return isValid;
        }

        private bool IsEmptyBloodReward(int itemId, BloodType bloodType)
        {
            return itemId <= 0 && bloodType == BloodType.Blood;
        }

        protected override void CreateConfig(RewardRequest request)
        {
            if (request.ConnectionType == ConnectionType.Online)
            {
                GenerateOnline(request);
            }
            else if (request.ConnectionType == ConnectionType.Offline)
            {
                GenerateOffline(request);
            }
        }

        private void GenerateOnline(RewardRequest request)
        {
            int inventoryItemId = ItemData.GetItemById(request.Item.Id);

            ClientItemData itemInfo = ItemData.GetData(inventoryItemId);
            ItemType itemType = (ItemType) itemInfo.Type;

            EcsEntity config = World.NewEntity();
            config.Get<InventoryItemMarker>();
            config.Get<ItemIdData>().Id = request.Item.Id;
            config.Get<ItemTypeData>().ItemType = itemType;
            config.Get<QuantityData>().Quantity = request.Item.Count;
            SetRewardData(ref config, request.BloodType, request.ViewType);
            TryAddBoostersData(ref config, request.BoostableType, new ItemQuantity {Id = (ushort) request.Item.Id, Count = request.Item.Count});
            config.Get<ConfigComponent>();
        }

        private void GenerateOffline(RewardRequest request)
        {
            EcsEntity config = World.NewEntity();
            config.Get<InventoryItemMarker>();
            config.Get<ItemIdData>().Id = request.Item.Id;
            config.Get<ItemTypeData>().ItemType = request.Item.ItemType;
            config.Get<QuantityData>().Quantity = request.Item.Count;
            SetRewardData(ref config, request.BloodType, request.ViewType);
            config.Get<ConfigComponent>();
        }

        private void TryAddBoostersData(ref EcsEntity config, BoostableType boostableType, ItemQuantity reward)
        {
            if (boostableType == BoostableType.NotBoostable)
            {
                return;
            }

            if (reward.IsVirtualMoneyCurrency())
            {
                config.Get<BoostersData>().Boosters = GetRewardBoosters(reward.Id);
            }
        }

        // todo: Create boosters system and move it there
        private IEnumerable<RewardBoostInfo> GetRewardBoosters(int rewardId)
        {
            var boosters = new List<RewardBoostInfo>();

            foreach (int boosterItemId in RewardBoosters.GetAvailableBoostersForItem(rewardId))
            {
                RewardBoostInfo boosterInfo = new RewardBoostInfo();
                boosters.Add(boosterInfo);  // add booster even it doesn't already exist to save info about ALL boosters 

                PlayerTimer effectTimer = _playerSession.Timers.GetTimer(boosterItemId, global::Common.TimerType.Boost);

                if (effectTimer == null)
                {
                    continue;
                }
                
                boosterInfo.SetTimer(effectTimer);
                boosterInfo.SetBoostValue(RewardBoosters.GetBoostValue(boosterItemId));
            }

            return boosters;
        }
    }
}