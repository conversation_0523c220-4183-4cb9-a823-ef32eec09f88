#if !UNITY_EDITOR
using Client.Common.Analytics.Infrastructure.Services.Google;
#endif
using System;
using System.Threading;
using Client.Common.Analytics.Systems;
using Client.Common.GameSettings;
using Client.Common.Network;
using Client.Common.Network.Connection;
using Client.Common.Network.MetaNet;
using Client.Common.NpcTab.Timer;
using Client.Common.Quests;
using Client.Common.Quests.Goals.Systems;
using Client.Common.Referrals;
using Client.Common.ResourcesTopPanel.Service;
using Client.Common.Storages.Systems;
using Client.Common.VContainer;
using Client.Utils.ECS.LocalWorld;
using Client.Utils.Extensions;
using Client.Utils.Extensions.EcsExtensions.SystemExtensions;
using Client.Utils.Haptics;
using Client.Utils.ServiceTool;
using Cysharp.Threading.Tasks;
using Leopotam.Ecs;
using UnityEngine;
using VContainer.Unity;
#if DEBUG
using Client.Utils.Profiling;
#endif
#if UNITY_EDITOR
using Leopotam.Ecs.UnityIntegration;
#endif

namespace Client.Common.ECS
{
    public class GlobalWorld : MonoBehaviour, IGlobalWorld
    {
        public EcsWorld World { get; private set; }
        public EcsSystems Systems { get; private set; }

        public event Action Tick;
        public event Action Cleanup;
        public event Action Executed;
        public event Action<LocalEcsWorld, EcsSystems> LocalWorldStarted;

#if DEBUG
        private ProfilerService _profilerService;
#endif
        
        private ILocalWorld _localWorld;

        private GlobalScopeService _globalScopeService;
        
        private Transform _dontDestroyViewRoot;
        private CancellationTokenSource _cts;

        //todo: Pass services in Service container / service bundle 
        public GlobalWorld Construct(EcsWorld globalEcsWorld, Transform dontDestroyViewRoot, GlobalScopeService globalScopeService)
        {
            _globalScopeService = globalScopeService;
            World = globalEcsWorld;
            _dontDestroyViewRoot = dontDestroyViewRoot;

            return this;
        }

        public async UniTask Init()
        {
            _cts = new CancellationTokenSource();
            GameSettingsGiver gameSettingsGiver = Service<GameSettingsGiver>.Get();
            MetaNet metaNet = Service<MetaNet>.Get();
            NetworkInfo networkInfo = Service<NetworkInfo>.Get();
            NetworkController networkController = Service<NetworkController>.Get();
            QuestsData questsData = Service<QuestsData>.Get();
            HapticWrapper haptics = Service<HapticWrapper>.Get();
            haptics.SetVibrationsActive(gameSettingsGiver.VibrationsToggleState);
            
#if DEBUG
            _profilerService = Service<ProfilerService>.Get();
#endif

#if !UNITY_EDITOR
            GoogleAnalyticsManager googleAnalyticsManager = Service<GoogleAnalyticsManager>.Get ();
            LocalWorldStarted += (_, _) => googleAnalyticsManager.TrackScreen();
#endif

            Systems = new EcsSystems(World, "GLOBAL");
#if UNITY_EDITOR
            EcsSystemsObserver.Create(Systems);
#endif

            await Systems
                  .Add(new InventoryUpdateSystem())
                  .Add(new EquipmentUpdateSystem())
                  .Add(new BloodProgressUpdateSystem())
                  .Add(new TimersUpdateSystem())
                  
                  .Add(new CraftCompleteDetectSystem())
                  
                  .AddFeature(new NetworkFeature(networkInfo, networkController, metaNet, _dontDestroyViewRoot))
                  .AddFeature(new QuestFeature())
                  .Add(new TopPanelServiceInitSystem())
                  .Add(new GlobalAnalyticsSystem())
                  .Add(new AnalyticsLogSystem())
                  .Add(new TelegramDeeplinkSystem(_dontDestroyViewRoot))
                  .InjectFromContainer(_globalScopeService.Scope.Container)
                  .InitAsync(_cts.Token);
        }

        public void Dispose()
        {
            Systems?.Destroy();
            Systems = null;
         
            World?.Dispose();
#if DEBUG   
            _profilerService?.Dispose();
#endif
            _cts?.CancelAndDispose();
        }

        public void StartRegisterLocalWorld(ILocalWorld localWorld)
        {
            _localWorld = localWorld;
        }

        public void UnregisterLocalWorld(ILocalWorld localWorld)
        {
            if (_localWorld == localWorld)
            {
                _localWorld = null;
            }
        }

        public bool TryGetCurrentLocalWorld(out ILocalWorld localWorld)
        {
            localWorld = null;
            if (_localWorld == null)
            {
                return false;
            }

            localWorld = _localWorld;
            return true;
        }
        
        public void CompleteRegisterLocalWorld(LocalEcsWorld localWorld, EcsSystems localSystems)
        {
            LocalWorldStarted?.Invoke(localWorld, localSystems);
        }
        
        private void Update()
        {
#if DEBUG
            _profilerService?.Update();
#endif
            
            Systems?.Run();
            Tick?.Invoke();

            Systems?.Cleanup();
            Cleanup?.Invoke();
            
            Executed?.Invoke();
        }

        private void OnApplicationPause(bool pauseStatus)
        {
            Systems?.AppPause();
        }

        private void OnApplicationQuit()
        {
            Systems?.AppQuit();
            Dispose();
        }

        private void OnDestroy()
        {
            Dispose();
            World.Destroy();
            World = null;
        }
    }
}