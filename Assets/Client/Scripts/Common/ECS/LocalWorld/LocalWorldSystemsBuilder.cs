using Client.Common.Analytics.Systems;
using Client.Common.Analytics.Trackers.Currency;
using Client.Common.Destruct.Systems;
using Client.Common.NpcTab.Timer;
using Client.Common.Purchases.Systems;
using Client.Common.Quests.Goals.Systems;
using Client.Common.VContainer;
using Client.Utils.ECS.LocalWorld;
using Client.Utils.Extensions.EcsExtensions.SystemExtensions;
using Leopotam.Ecs;

namespace Client.Common.ECS.LocalWorld
{
    public class LocalWorldSystemsBuilder
    {
        private readonly EcsSystems _systems;
        private readonly LocalEcsWorld _world;
        private readonly GlobalScopeService _globalScopeService;

        public LocalWorldSystemsBuilder(LocalEcsWorld world, EcsSystems systems, GlobalScopeService globalScopeService)
        {
            _systems = systems;
            _globalScopeService = globalScopeService;
            _world = world;
        }

        public void AddCommonPostSystems<TScope>() where TScope : struct
        {
            _systems
                .Add(new CraftItemQuestProgressSystem())
                
                .Add(new ItemPurchaseEventSystem())
                .Add(new NotEnoughCurrencyAnalyticsTrackSystem())
                .Add(new LocalCommonAnalyticsSystem())
                
                .AddFeature(new ProcessDestructedFeature())
                .Add(new EntityCleaningSystem<TScope>())
                .InjectFromContainer(_globalScopeService.Scope.Container)
                .Inject(_world);
        }
    }
}