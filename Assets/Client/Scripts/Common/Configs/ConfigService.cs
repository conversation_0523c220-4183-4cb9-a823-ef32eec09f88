using System;
using System.Collections.Generic;
using System.Threading;
using Client.Common.Abilities.Infrastructure.Data;
using Client.Common.Configs.Components;
using Client.Common.Configs.Components.Abilities;
using Client.Common.Configs.Components.Items;
using Client.Common.Configs.Core;
using Client.Common.Configs.Generators;
using Client.Common.Configs.Loaders;
using Client.Common.Configs.Loaders.Factories;
using Cysharp.Threading.Tasks;
using Leopotam.Ecs;

#if UNITY_EDITOR
using Client.Common.Configs.Editor;
using Leopotam.Ecs.UnityIntegration;
#endif

namespace Client.Common.Configs
{
    public class ConfigService
    {
        public event Action ConfigsLoaded;
        
        private EcsWorld _configWorld;
        private ConfigServiceCore _core;

        public void Init(ConfigLoadersFactory loadersFactory)
        {
            CreateEcs();
            
#if UNITY_EDITOR
            ConfigInspectorOpener.SetConfigWorld(_configWorld);
#endif

            Dictionary<Type,IConfigLoader> loadersMap = loadersFactory.Create();

            _core = new ConfigServiceCore(_configWorld, loadersMap);
        }

        public void Dispose()
        {
            _configWorld?.Destroy();
        }

        public ConfigService AddGenerator(IConfigGenerator configGenerator)
        {
            configGenerator.Generate(_core);
            
            return this;
        }

        public ConfigService AddGenerator(List<IConfigGenerator> configGenerators)
        {
            foreach (IConfigGenerator configGenerator in configGenerators)
            {
                configGenerator.Generate(_core);
            }
            
            return this;
        }

        public T GetFilter<T>(bool createIfNotExists = true) where T : EcsFilter
        {
            return _core.GetFilter<T>(createIfNotExists);
        }

        public T GetByKey<T>(string id) where T: struct
        {
            return _core.GetByKey<T>(id);
        }

        public bool TryGetByKey<T>(string key, out T data) where T : struct
        {
            return _core.TryGetByKey(key, out data);
        }

        public ICollection<T> GetByKeys<T>(ICollection<string> keys) where T : struct
        {
            return _core.GetByKeys<T>(keys);
        }

        public EcsEntity GetEntityByKey<T>(string id) where T : struct
        {
            return _core.GetEntityByKey<T>(id);
        }
        
        public EcsEntity GetItem(int itemId) => _core.GetEntityByKey<ItemMarker>(itemId.ToString());

        public EcsEntity GetAbility(AbilityId ability) => _core.GetEntityByKey<AbilityMarker>(((int) ability).ToString());

        public EcsEntity GetRank(uint index) => _core.GetByOrderNumber<RankMarker>((int)index);

        public EcsEntity GetLeague(uint index) => _core.GetByOrderNumber<LeagueMarker>((int)index);
        
        public EcsEntity GetBloodReward(uint step) => _core.GetByOrderNumber<BloodRewardMarker>((int)step);

        public IEnumerable<EcsEntity> GetAllConfigs()
        {
            return _core.GetAllConfigs();
        }

        public async UniTask Generate(CancellationToken cancellationToken = default)
        {
            await _core.Generate(cancellationToken);
            if (cancellationToken.IsCancellationRequested)
            {
                return;
            }

            ConfigsLoaded?.Invoke();
        }

#if UNITY_EDITOR
        public void AddDebugListener(IEcsWorldDebugListener listener)
        {
            _configWorld.AddDebugListener(listener);
        }
#endif

        private void CreateEcs()
        {
            _configWorld = new EcsWorld(
                new EcsWorldConfig
                {
                    WorldComponentPoolsCacheSize = 512
                });

#if UNITY_EDITOR
            EcsWorldObserver.Create(_configWorld, "ConfigWorld");
#endif
        }
    }
}