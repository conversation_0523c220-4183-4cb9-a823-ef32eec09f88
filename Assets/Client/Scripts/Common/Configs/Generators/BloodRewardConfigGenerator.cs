using System.Collections.Generic;
using System.Linq;
using Client.Common.Configs.Components;
using Client.Common.Configs.Components.Items;
using Client.Common.Configs.Core;
using Client.Common.Configs.Data;
using Client.Common.Configs.DirectLoaders;
using Client.Common.Configs.Loaders;
using Client.Common.Items;
using Common;
using Leopotam.Ecs;
using BloodReward = Common.BloodReward;

namespace Client.Common.Configs.Generators
{
    public struct BloodRewardMarker: IEcsIgnoreInFilter {}
    
    public struct BloodRewardUnlockMarker: IEcsIgnoreInFilter {}
    
    public struct BloodRewardDictionaryMarker: IEcsIgnoreInFilter {}

    public class BloodRewardConfigGenerator: IConfigGenerator
    {
        private readonly BloodRewardDataProvider _bloodRewardDataProvider;
        
        private readonly IDirectConfigLoader<BloodReward>[] _loaders;

        public BloodRewardConfigGenerator(BloodRewardDataProvider bloodRewardDataProvider, ItemData itemData)
        {
            _bloodRewardDataProvider = bloodRewardDataProvider;

            _loaders = new IDirectConfigLoader<BloodReward>[]
            {
                new BloodRewardNameDescriptionLoader(itemData),
                new BloodRewardPathsLoader(itemData),
            };
        }
        public void Generate(ConfigServiceCore configCore)
        {
            int totalBlood = 0;
            foreach (BloodReward bloodReward in _bloodRewardDataProvider.Rewards)
            {
                EcsEntity config = configCore.World.NewEntity();
                totalBlood += (int)bloodReward.Price;

                config.Get<BloodRewardMarker>();
                LoadRewardInfo(config, bloodReward, totalBlood);
                LoadRewardItems(config, bloodReward.Item, configCore);
            }
            
            foreach (BloodReward rewardUnlock in _bloodRewardDataProvider.Diff)
            {
                EcsEntity config = configCore.World.NewEntity();
                
                config.Get<BloodRewardUnlockMarker>();
                LoadRewardInfo(config, rewardUnlock);
                config.Get<ItemConfigKeysList>().Keys = rewardUnlock.Item.Select(x => x.ToString()).ToArray();
            }
        }

        private void LoadRewardInfo(EcsEntity config, BloodReward bloodReward, int totalBlood = 0)
        {
            config.Get<ProgressStepData>().Construct((int)bloodReward.Id, totalBlood);
            config.Get<ConfigComponent>().Key = bloodReward.Description;
            config.Get<PriceData>().Construct((uint)bloodReward.Price, CurrencyType.Blood);
            config.Get<RegionData>().RegionId = bloodReward.RegionId;
            config.Get<PartNumber>().Number = bloodReward.Parts;
            config.Get<OrderNumber>().Number = (int) bloodReward.Id;
            
            foreach (IDirectConfigLoader<BloodReward> loader in _loaders)
            {
                loader.Load(config, bloodReward);
            }
        }

        private void LoadRewardItems(EcsEntity config, IEnumerable<long> items, ConfigServiceCore configServiceCore)
        {
            string[] itemKeys = items.Select(x => x.ToString()).ToArray();
            config.Get<ItemConfigKeysList>().Keys = itemKeys;
            
            ProgressStepData progressStepData = config.Get<ProgressStepData>();
            foreach (string itemKey in itemKeys)
            {
                EcsEntity itemConfig = configServiceCore.GetEntityByKey<ItemIdData>(itemKey);
                if (itemConfig.IsNull())
                {
                    continue;
                }
                itemConfig.Get<ProgressStepData>() = progressStepData;
            }
        }
    }
}