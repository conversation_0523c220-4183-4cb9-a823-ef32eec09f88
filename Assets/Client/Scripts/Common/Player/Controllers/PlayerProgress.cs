using System.Collections.Generic;
using System.Linq;
using Client.Common.Durability.DurabilityStorage.WeaponSkins;
using Client.Common.Network.MetaNet;
using Client.Common.Player.PocketFeature.Pockets;
using Common;
using External;
using SkinDurability = Common.SkinDurability;

namespace Client.Common.Player.Controllers
{
    public sealed class PlayerProgress
    {
        public readonly PocketsStorage Pockets;
        public readonly PlayerWeaponStorage WeaponStorage;
        public readonly IBrokenWeaponSkinStorage BrokenWeaponSkinStorage;

        public int Rank { get; private set; }
        public int RankPoints { get; private set; }
        public int OutfitId { get; private set; }
        public int WeaponId { get; private set; }
        public int WeaponSkinId { get; private set; }
        public int AmuletId { get; private set; }
        public int BloodProgress { get; private set; }
        public int Kills { get; private set; }
        public int Defeats { get; private set; }
        public SkinDurability[] OutfitDurabilities { get; private set; }
        public HashSet<AccountStep> FtueSteps { get; private set; }

        /// <summary>
        /// Last allowed (included to selection) region id.
        /// </summary>
        public readonly int LastOpenedRegionId = 1;
        public int TotalBattles() => Kills + Defeats;

        public PlayerProgress()
        {
            WeaponStorage = new PlayerWeaponStorage();
            BrokenWeaponSkinStorage = new BrokenWeaponSkinLocalStorage();
            Pockets = new PocketsStorage();
        }

        public void SetProgress(in ExternalGetProgressResponse progressResponse)
        {
            Progress progressData = progressResponse.Progress;

            Rank = (int) progressData.Rank;
            WeaponId = (int) progressData.WeaponId;
            WeaponStorage.SetDataMap(progressData.WeaponSkins);
            WeaponSkinId = WeaponStorage.GetSkinId((int) progressData.WeaponId);
            OutfitId = (int) progressData.OutfitId;
            AmuletId = (int) progressData.AmuletId;
            Pockets.Set(progressData.Pockets);
            BloodProgress = (int) progressData.BloodStep;

            OutfitDurabilities = progressData.SkinDurabilities.ToArray();
            FtueSteps = new HashSet<AccountStep>(progressData.Steps.Select(x => (AccountStep)x));
        }

        public void SetBattleProgress(in BattleProgressResponse progressData)
        {
            RankPoints = progressData.Points;
            Defeats = progressData.Lose;
            Kills = progressData.Win;
        }

        public void UpdateBattleProgress(
            int rank,
            int points,
            int kills,
            int defeats)
        {
            Rank = rank;
            RankPoints = points;
            Kills = kills;
            Defeats = defeats;
        }

        public void SetRank(int rank)
        {
            Rank = rank;
        }

        public void UpdateEquipment(in ChangeEquipmentInfo info)
        {
            if (info.WeaponId != 0)
            {
                WeaponId = (int) info.WeaponId;
            }

            if (info.OutfitId != 0)
            {
                OutfitId = (int) info.OutfitId;
            }

            if (info.AmuletId != 0)
            {
                AmuletId = (int) info.AmuletId;
            }

            if (info.WeaponSkin.Any())
            {
                WeaponStorage.SetDataMap(info.WeaponSkin);
                WeaponSkinId = WeaponStorage.GetSkinId(WeaponId);
            }

            if (info.SkinDurability.Any())
            {
                OutfitDurabilities = info.SkinDurability.ToArray();
            }
        }

        public void UpdateBloodStep(int step)
        {
            if (step != 0)
            {
                BloodProgress = step;
            }
        }

        public void UpdatePockets(in ChangePocketsInfo info)
        {
            Pockets.Set(info.Pocket);
        }

        public uint GetOutfitDurability(int itemId)
        {
            SkinDurability item = OutfitDurabilities.FirstOrDefault(item => item.Id == itemId);

            if (item is null)
            {
                return 0;
            }

            return (uint) item.Durability;
        }
    }
}