using System.Collections.Generic;
using System.Linq;
using Client.Common.Player.Data;
using Common;

namespace Client.Common.Player.Controllers
{
    public class PlayerWeaponStorage
    {
        private Dictionary<int, Weapon> _weaponIdToWeaponMap;

        public PlayerWeaponStorage()
        {
            _weaponIdToWeaponMap = new Dictionary<int, Weapon>();
        }

        public void SetDataMap(ICollection<WeaponSkin> weaponSkins)
        {
            _weaponIdToWeaponMap?.Clear();
            _weaponIdToWeaponMap = weaponSkins.ToDictionary(
                weaponSkin => (int)weaponSkin.WeaponId,
                weaponSkin => ConvertWeaponSkinToWeapon(weaponSkin));
        }
        
        public bool TryGet(int weaponId, out Weapon weapon)
        {
            return _weaponIdToWeaponMap.TryGetValue(weaponId, out weapon);
        }

        public bool IsWeaponHasSkin(Weapon weapon)
        {
            return weapon.Skin.Id != PlayerInventory.Items.DEFAULT_SHURIKEN_SKIN_ID; 
        }
        
        public int GetSkinId(int weaponId)
        {
            int skinId = PlayerInventory.Items.DEFAULT_SHURIKEN_SKIN_ID;
            if (TryGet(weaponId, out Weapon weapon))
            {
                skinId = (int)weapon.Skin.Id;
            }

            return skinId;
        }
        
        public uint GetSkinDurability(int weaponId)
        {
            if (!TryGet(weaponId, out Weapon weapon))
            {
                return 0;
            }

            return (uint)weapon.Skin.Durability;
        }

        private Weapon ConvertWeaponSkinToWeapon(WeaponSkin weaponSkin)
        {
            return new Weapon
            {
                Id = (int)weaponSkin.WeaponId,
                Skin = weaponSkin
            };
        }
    }
}