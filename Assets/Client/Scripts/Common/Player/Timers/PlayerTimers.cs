using System;
using System.Collections.Generic;
using Client.Common.Network.MetaNet;
using Client.Utils.ObjectPool.Implementations;
using Common;
using UnityEngine;
using TimerType = Common.TimerType;

namespace Client.Common.Player.Timers
{
    public class PlayerTimers
    {
        private readonly HashSet<PlayerTimer> _timers = new();

        public event Action<PlayerTimer> TimerStarted;
        public event Action<PlayerTimer> TimerEnded; 

        /// <summary>
        /// Only for initial loading
        /// </summary>
        public void Update(in ClientTimerResponse timerResponse)
        {
            timerResponse.Active.Sort((a, b) => a.ItemId - b.ItemId);
            timerResponse.Ended.Sort((a, b) => a.ItemId - b.ItemId);
            _timers.Clear();
            foreach (ClientTimerItem item in timerResponse.Active)
            {
                _timers.Add(new PlayerTimer(item));
            }
        }

        public ClientTimerItem ConvertTimer(Timer timer)
        {
            return new ClientTimerItem
            {
                TimerType = (Network.MetaNet.TimerType) timer.Type,
                ItemId = (ushort) timer.ItemId,
                StartTime = timer.StartTime,
                DeadTime = timer.DeadTime
            };
        }

        public void ChangeTimer(Timer timer)
        {
            ChangeTimer(ConvertTimer(timer));
        }

        public void ChangeTimer(ClientTimerItem timer)
        {
            if (timer.TimerType == 0 || timer.ItemId == 0)
            {
                Debug.LogError("[TIMERS] ERROR: trying to add timer with 0");
                return;
            }

            PlayerTimer playerTimer = GetTimer(timer.ItemId, (TimerType)timer.TimerType);

            if (playerTimer is null)
            {
                PlayerTimer newTimer = new(timer);
                _timers.Add(newTimer);
                TimerStarted?.Invoke(newTimer);
                return;
            }

            _timers.Remove(playerTimer);
            long remainingUtcTimeInSeconds = playerTimer.GetRemainingUtcTimeInSeconds();
            if (remainingUtcTimeInSeconds <= 0)
            {
                TimerEnded?.Invoke(playerTimer);
            }
            else
            {
                _timers.Add(new PlayerTimer(timer));
            }
        }

        public List<PlayerTimer> GetTimers(Query query)
        {
            var timers = new List<PlayerTimer>();

            foreach (var timer in _timers)
            {
                if (IsQueryConditionMet(query.QueryActual, timer))
                {
                    timers.Add(timer);
                }
            }

            query.Release();
            return timers;
        }

        private static bool IsQueryConditionMet(Query.InnerQuery query, PlayerTimer timer)
        {
            return (!query.ItemId.HasValue || query.ItemId.Value == timer.ItemId) &&
                   (!query.TimerType.HasValue || query.TimerType.Value == timer.TimerType);
        }
        
        public PlayerTimer GetTimer(int itemId, TimerType timerType)
        {
            Query query = Query.Get().WithItemId(itemId).WithTimerType(timerType);
            foreach (var timer in _timers)
            {
                if (IsQueryConditionMet(query.QueryActual, timer))
                {
                    query.Release();
                    return timer;
                }
            }

            query.Release();
            return null;
        }

        public bool TryGetTimer(int itemId, TimerType timerType, out PlayerTimer timer)
        {
            timer = GetTimer(itemId, timerType);
            return timer != null && timer.GetRemainingUtcTimeInSeconds() > 0;
        }

        public void Reset()
        {
            _timers.Clear();
        }
        
        //TODO: would be great to pass a custom Func<bool> condition to get all timers belonging to several itemIds or several timerTypes.
        public class Query
        {
            private static readonly ObjectPool<Query> Pool = new ObjectPool<Query>(() => new Query());

            private InnerQuery _query;

            private Query()
            {
            }

            public InnerQuery QueryActual => _query;

            private void Reset()
            {
                _query.Reset();
            }

            public static Query Get()
            {
                return Pool.Get();
            }

            public void Release()
            {
                Reset();
                Pool.Release(this);
            }

            public Query WithItemId(int itemId)
            {
                _query.ItemId = itemId;
                return this;
            }

            public Query WithTimerType(TimerType timerType)
            {
                _query.TimerType = timerType;
                return this;
            }

            public struct InnerQuery
            {
                public int? ItemId;
                public TimerType? TimerType;

                public void Reset()
                {
                    ItemId = null;
                    TimerType = null;
                }
            }
        }
    }
}