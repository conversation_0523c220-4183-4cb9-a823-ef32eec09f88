using System;
using System.Collections.Generic;
using Client.Common.Ftue.Infrastructure;
using Client.Common.Ftue.Infrastructure.Data;
using Client.Common.Network.MetaNet;
using Client.Common.Player.Controllers;
using Client.Common.Player.Timers;
using Leopotam.Localization;
using NotificationSamples;
using TimerType = Common.TimerType;

namespace Client.Common.LocalNotifications.Providers
{
    public class TraumaEndNotificationProvider : LocalNotificationProviderBase
    {
        private readonly PlayerManager _playerManager;

        public TraumaEndNotificationProvider(GameNotificationsManager executor, CsvLocalization localization, PlayerManager playerManager, FtueProgress ftueProgress) : base(executor, localization, ftueProgress)
        {
            _playerManager = playerManager;
        }

        public override bool DisplayInSettings() => false;

        protected override IEnumerable<IGameNotification> GetNotificationsInternal()
        {
            if (!IsRegionTutorialCompleted)
            {
                yield break;
            }
            
            List<PlayerTimer> timers = _playerManager.Session.Timers
                                                     .GetTimers(PlayerTimers.Query.Get().WithTimerType(TimerType.Trauma));

            DateTime timeSchedule = Now.AddHours(LocalNotificationsIdents.NotificationTime.STANDARD_NOTIFICATION_TIME);
            if (timers.Count > 0 && LocalNotificationsHelper.IsTimerEndInNotificationPeriod(timers, timeSchedule))
            {
                yield return CreateNotification(
                    LocalNotificationDataContainer.TraumaFinished,
                    Localization.Get("push_trauma_end_1_1_title"),
                    Localization.Get("push_trauma_end_1_1_body"),
                    ConsiderDoNotDisturb(timeSchedule));
            }
        }
    }
}