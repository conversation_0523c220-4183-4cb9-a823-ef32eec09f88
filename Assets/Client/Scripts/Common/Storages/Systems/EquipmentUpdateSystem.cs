using Client.Common.Network.ExternalEvents.EventReceivers;
using Client.Common.Player.Controllers;
using External;
using Leopotam.Ecs;

namespace Client.Common.Storages.Systems
{
    public class EquipmentUpdateSystem: IEcsInitSystem, IEcsDestroySystem
    {
        private readonly PlayerManager _playerManager = default;
        private readonly IExternalEventReceiver<ChangeEquipmentInfo> _changeEquipmentInfo;
        private readonly IExternalEventReceiver<ChangePocketsInfo> _changePocketsInfo;
        
        public void Init()
        {
            _changeEquipmentInfo.Received += UpdateEquipment;
            _changePocketsInfo.Received += UpdatePockets;
        }

        private void UpdatePockets(ChangePocketsInfo info)
        {
            _playerManager.Session.Progress.UpdatePockets(in info);
        }

        private void UpdateEquipment(ChangeEquipmentInfo info)
        {
            _playerManager.Session.Progress.UpdateEquipment(in info);
        }

        public void Destroy()
        {
            _changeEquipmentInfo.Received -= UpdateEquipment;
            _changePocketsInfo.Received -= UpdatePockets;
        }
    }
}