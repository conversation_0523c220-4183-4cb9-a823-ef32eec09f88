using System.Collections.Generic;
using Client.Common.Network.ExternalEvents.EventReceivers;
using Client.Common.NpcTab.Timer;
using Client.Common.Player.Controllers;
using Client.Common.Player.Timers;
using Client.Utils.GameLogger;
using External;
using Leopotam.Ecs;

namespace Client.Common.Storages.Systems
{
    public class TimersUpdateSystem : IEcsInitSystem, IEcsRunSystem, IEcsDestroySystem, IEcsCleanupSystem
    {
        private readonly PlayerManager _playerManager = default;
        private readonly IExternalEventReceiver<ChangeTimerInfo> _changeInfo = default;
        private readonly EcsWorld _world = default;

        private readonly EcsFilter<TimerEndEvent> _endedTimers = default;
        private readonly EcsFilter<TimerStartEvent> _startedTimers = default;

        private readonly List<PlayerTimer> _timersStartedThisFrame = new();
        private readonly List<PlayerTimer> _timersEndedThisFrame = new();

        public void Init()
        {
            _changeInfo.Received += UpdateTimers;
            _playerManager.Session.Timers.TimerEnded += RecordTimerEnd;
            _playerManager.Session.Timers.TimerStarted += RecordTimerStart;
        }

        private void UpdateTimers(ChangeTimerInfo info)
        {
            PlayerTimers timers = _playerManager.Session.Timers;
            timers.ChangeTimer(timers.ConvertTimer(info.Timer));
        }

        private void RecordTimerEnd(PlayerTimer timer)
        {
            _timersEndedThisFrame.Add(timer);
        }

        private void RecordTimerStart(PlayerTimer timer)
        {
            _timersStartedThisFrame.Add(timer);
        }

        public void Run()
        {
            if (_timersEndedThisFrame.Count == 0 && _timersStartedThisFrame.Count == 0)
            {
                return;
            }

            foreach (PlayerTimer playerTimer in _timersEndedThisFrame)
            {
                NotifyTimerEnd(playerTimer);
            }

            foreach (PlayerTimer playerTimer in _timersStartedThisFrame)
            {
                NotifyTimerStart(playerTimer);
            }

            _timersEndedThisFrame.Clear();
            _timersStartedThisFrame.Clear();
        }

        private void NotifyTimerEnd(PlayerTimer timer)
        {
            ref TimerEndEvent timerEndEvent = ref _world.NewEntity().Get<TimerEndEvent>();
            timerEndEvent.ItemId = timer.ItemId;
            timerEndEvent.Type = timer.TimerType;
            GameLogger.Log($"Timer ended: {timer.TimerType} {timer.ItemId}");
        }

        private void NotifyTimerStart(PlayerTimer timer)
        {
            ref var timerStartEvent = ref _world.NewEntity().Get<TimerStartEvent>();
            timerStartEvent.ItemId = timer.ItemId;
            timerStartEvent.Type = timer.TimerType;
            GameLogger.Log($"Timer started: {timer.TimerType} {timer.ItemId}");
        }

        public void Destroy()
        {
            _changeInfo.Received -= UpdateTimers;
            _playerManager.Session.Timers.TimerEnded -= RecordTimerEnd;
            _playerManager.Session.Timers.TimerStarted -= RecordTimerStart;
        }

        public void Cleanup()
        {
            foreach (int index in _endedTimers)
            {
                _endedTimers.GetEntity(index).Destroy();
            }

            foreach (int index in _startedTimers)
            {
                _startedTimers.GetEntity(index).Destroy();
            }
        }
    }
}