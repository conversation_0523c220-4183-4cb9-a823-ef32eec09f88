using Client.Common.Network.ExternalEvents.EventReceivers;
using Client.Common.Player.Controllers;
using External;
using Leopotam.Ecs;

namespace Client.Common.Storages.Systems
{
    public class BloodProgressUpdateSystem: IEcsInitSystem, IEcsDestroySystem
    {
        private readonly PlayerManager _playerManager = default;
        private readonly IExternalEventReceiver<ChangeBloodInfo> _changeInfo;
        
        public void Init()
        {
            _changeInfo.Received += UpdateBloodStep;
        }

        private void UpdateBloodStep(ChangeBloodInfo info)
        {
            _playerManager.Session.Progress.UpdateBloodStep((int) info.BloodStep);
        }

        public void Destroy()
        {
            _changeInfo.Received -= UpdateBloodStep;
        }
    }
}