using System.Collections.Generic;
using Client.Common.Analytics.Infrastructure.Events;
using Client.Common.Analytics.Trackers.Abstract;
using Client.Common.Items;
using Client.Common.NpcTab.Timer;
using Client.Utils.ECS.LocalWorld;
using Common;
using Leopotam.Ecs;

namespace Client.Common.NpcTab.Analytics
{
    public class ItemEndCraftAnalyticTracker : IAnalyticTracker
    {
        private readonly LocalEcsWorld _world;
        private readonly ItemData _itemData;
        private readonly string _eventId;
        
        private readonly EcsFilter<ItemCraftedEvent>.Exclude<AnalyticsEventApplied<ItemEndCraftAnalyticTracker>> _craftFilter;

        public ItemEndCraftAnalyticTracker(LocalEcsWorld world, ItemData itemData, string eventId)
        {
            _world = world;
            _itemData = itemData;
            _eventId = eventId;
            _craftFilter = (EcsFilter<ItemCraftedEvent>.Exclude<AnalyticsEventApplied<ItemEndCraftAnalyticTracker>>)
                world.GetFilter(typeof(EcsFilter<ItemCraftedEvent>.Exclude<AnalyticsEventApplied<ItemEndCraftAnalyticTracker>>));
        }

        public void Track()
        {
            foreach (int index in _craftFilter)
            {
                FireEvent(_craftFilter.Get1(index).ItemId);

                _craftFilter.GetEntity(index).Get<AnalyticsEventApplied<ItemEndCraftAnalyticTracker>>();
            }
        }

        private void FireEvent(int itemId)
        {
            ItemType type = (ItemType) _itemData.GetItemDataById(itemId).Type;
            string eventId = $"{type.ToString().ToLower()}_{_eventId}";
            
            EcsEntity analyticsEvent = _world.NewEntity();
            analyticsEvent.Get<AnalyticsCustomEvent>().Id = eventId;
            analyticsEvent.Get<AnalyticsParameters>().Parameters = new Dictionary<string, string>()
            {
                ["ItemId"] = $"{itemId}",
            };
        }
    }
}