using Client.Common.Configs.Components;
using Client.Common.Items;
using Client.Common.Network.MetaNet;
using Client.Common.NpcTab.Controllers.Generators;
using Client.Common.Player.Controllers;
using Client.Common.Player.Timers;
using Client.Common.TimeGiver.Abstractions;
using Client.Utils.ECS.EcsTaskTools;
using Client.Utils.ECS.LocalWorld;
using Client.Utils.Extensions;
using Client.Utils.GameLogger;
using Cysharp.Threading.Tasks;
using Leopotam.Ecs;

namespace Client.Common.NpcTab.Data
{
    public class ItemActionsHelper
    {
        private readonly LocalEcsWorld _world;
        private readonly PlayerManager _playerManager;
        private readonly ItemData _itemData;
        private readonly ITimeGiver _timeGiver;

        public ItemActionsHelper(LocalEcsWorld world, PlayerManager playerManager, ItemData itemData, ITimeGiver timeGiver)
        {
            _world = world;
            _playerManager = playerManager;
            _itemData = itemData;
            _timeGiver = timeGiver;
        }
        
        public async UniTask<bool> TryPerformCraftClicked(EcsEntity currentConfig, int itemId, PriceData priceData)
        {
            EcsTask requestTask = RequestHelper.CreateCraftRequest(_world, itemId, priceData.Price, priceData.CurrencyType);
            await requestTask.IsDone();

            if (requestTask.Status == EcsTask.States.Cancelled)
            {
                return false;
            }

            global::Common.TimerType timerType = global::Common.TimerType.Craft;
            if (!TryGetCraftProcess(_playerManager, _timeGiver, itemId, timerType, out PlayerTimer craftProcess))
            {
                GameLogger.LogError($"[Craft] There's no craft timer for {itemId} after craft was clicked.");
                return false;
            }

            ClientItemData itemInfo = _itemData.GetItemDataById(itemId);
            ItemDynamicConfigGenerator.InitCraftConfig(currentConfig, itemInfo, craftProcess);

            return true;
        }
        
        private bool TryGetCraftProcess(PlayerManager playerManager, ITimeGiver timeService, int itemId, global::Common.TimerType timerType, out PlayerTimer craftProcess)
        {
            craftProcess = playerManager.Session.Timers.GetTimer(itemId, timerType);
            return craftProcess != null && timeService.UtcNow.GetUtcTimeInSeconds() < craftProcess.EndTime;
        }
    }
}