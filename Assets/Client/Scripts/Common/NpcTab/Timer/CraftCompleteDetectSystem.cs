using System.Collections.Generic;
using Client.Common.CraftReadyItems;
using Leopotam.Ecs;

namespace Client.Common.NpcTab.Timer
{
    public class CraftCompleteDetectSystem: IEcsInitSystem, IEcsRunSystem, IEcsCleanupSystem
    {
        private readonly EcsWorld _world = default;
        private readonly CraftReadyItemsService _craftReadyItemsService = default;
        private readonly EcsFilter<ItemCraftedEvent> _events = default;

        private readonly List<int> _itemsTakenThisFrame = new List<int>();

        public void Init()
        {
            _craftReadyItemsService.ItemTaken += OnItemTaken;
        }

        private void OnItemTaken(int item)
        {
            _itemsTakenThisFrame.Add(item);
        }

        public void Run()
        {
            if (_itemsTakenThisFrame.Count == 0)
            {
                return;
            }

            foreach (int itemId in _itemsTakenThisFrame)
            {
                _world.NewEntity().Get<ItemCraftedEvent>().ItemId = itemId;
            }
            
            _itemsTakenThisFrame.Clear();
        }

        public void Cleanup()
        {
            foreach (int index in _events)
            {
                _events.GetEntity(index).Destroy();
            }
        }
    }
}