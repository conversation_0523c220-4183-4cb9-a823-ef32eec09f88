using Client.Common.Configs.Components;
using Client.Common.NpcTab.Components;
using Client.Common.NpcTab.Data;
using Client.Common.NpcTab.Pieces.Views;
using Client.Common.NpcTab.Systems.ItemStates.Abstractions;
using Client.Common.NpcTab.Views;
using Client.Common.UI.Buttons.Texted;
using Client.Utils.Extensions.EcsExtensions;
using Cysharp.Threading.Tasks;
using JetBrains.Annotations;
using Leopotam.Ecs;
using UnityEngine;

namespace Client.Common.NpcTab.Systems.ItemStates.Implementations
{
    public abstract class CollectableCraftCompletedStateSystem <TItemView, TStateMachine> : CraftCompletedStateSystem<TItemView, TStateMachine>
        where TItemView : MonoBehaviour, ICollectableView
        where TStateMachine : IEcsFeature
    {
        protected override ActionButtonTexted ClaimButton { get; }
        protected IPieceView PiecesView { get; }
        
        protected CollectableCraftCompletedStateSystem(ActionButtonTexted claimButton, IPieceView pieceView)
        {
            ClaimButton = claimButton;
            PiecesView = pieceView;
        }
        
        protected override void InitView()
        {
            base.InitView();
            PiecesView.Show();
        }
        
        protected async UniTask ClaimItemWithVFX()
        {
            PlayVFX();
            await ClaimItem();
        }

        private void PlayVFX()
        {
            CraftVFX craftVFX = Items.Get3(0).View;
            craftVFX.PlayCraftFinishTake();
        }

        protected async UniTask ClaimItem()
        {
            await TakeCraftedItem();
            
            OnItemClaimed();
        }

        private async UniTask TakeCraftedItem()
        {
            CurrentEntity.Del<CraftCompletedItemMarker>();
            await CraftReadyItemsService.TakeCraftedItem(CurrentId);
            CurrentEntity.Get<EquipReadyItemMarker>();
        }

        protected abstract void OnItemClaimed();

        protected override void OnDisable()
        {
            base.OnDisable();
            PiecesView.Hide();
        }
    }
}