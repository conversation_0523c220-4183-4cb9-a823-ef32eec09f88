using System;
using System.Collections.Generic;
using Client.Common.Analytics.Helpers;
using Client.Common.Audio;
using Client.Common.Audio.Banks;
using Client.Common.Configs.Components;
using Client.Common.Configs.Components.Items;
using Client.Common.CSV;
using Client.Common.Currency;
using Client.Common.Durability;
using Client.Common.HintSystem;
using Client.Common.Network.MetaNet;
using Client.Common.NpcTab.Controllers;
using Client.Common.NpcTab.Craft.Views;
using Client.Common.NpcTab.Data;
using Client.Common.NpcTab.Data.UI;
using Client.Common.NpcTab.Timer;
using Client.Common.NpcTab.Views;
using Client.Common.Player.Controllers;
using Client.Common.Player.Timers;
using Client.Common.ResourcesTopPanel;
using Client.Common.SceneLoading;
using Client.Common.ScreenTransitions;
using Client.Common.ServiceBundles;
using Client.Common.Services.Ads;
using Client.Common.Services.Ads.Analytics;
using Client.Common.Services.Ads.Infrastructure;
using Client.Common.TimeGiver.Implementations;
using Client.Common.UI;
using Client.Common.UI.Buttons.Pay;
using Client.Common.UI.InputLockService;
using Client.Common.UI.ScrollController.Components;
using Client.Utils.ECS.ECSStateMachine.Systems;
using Client.Utils.ECS.EcsTaskTools;
using Client.Utils.Extensions;
using Client.Utils.GameLogger;
using Client.Utils.ResultTool.Results;
using Client.Utils.ViewService;
using Common;
using Cysharp.Threading.Tasks;
using JetBrains.Annotations;
using Leopotam.Ecs;
using Leopotam.Localization;
using UnityEngine;
using CurrencyType = Common.CurrencyType;
using ItemType = Common.ItemType;
using TimerType = Common.TimerType;

namespace Client.Common.NpcTab.Systems.ItemStates.Abstractions
{
    public abstract class BaseCraftStateSystem<TStateMachine> : StateSystem<TStateMachine>
    {
        private const float _UPDATE_PRICE_FREQUENCY_ = 1f;

        [UsedImplicitly] protected readonly MetaNet MetaNet;
        [UsedImplicitly] protected readonly PlayerManager PlayerManager;
        [UsedImplicitly] protected readonly CsvLocalization Localization;
        [UsedImplicitly] protected readonly GoogleDocsData GoogleDocsData;
        [UsedImplicitly] protected readonly ServerTimeService ServerTimeService;
        [UsedImplicitly] protected readonly TabAnchorsBundle Anchors;
        [UsedImplicitly] protected readonly TabIdents TabIdents;
        [UsedImplicitly] protected readonly DurabilityInfoUpdateHelper DurabilityInfoUpdateHelper;
        [UsedImplicitly] protected readonly BazarAudio AudioBank;
        [UsedImplicitly] protected readonly FullScreenLocker FullScreenLocker;
        [UsedImplicitly] protected readonly CommonServicesBundle Services;
        [UsedImplicitly] protected readonly HintAnchors HintAnchors;
        [UsedImplicitly] protected readonly AnimatedSceneLoader SceneLoader;
        [UsedImplicitly] protected readonly PopupViewService PopupViewService;
        [UsedImplicitly] protected readonly IAnalyticsHelper AnalyticsHelper;
        
        [UsedImplicitly] 
        protected readonly EcsFilter<TimerEndEvent> EndedTimers;
        [UsedImplicitly]
        protected readonly EcsFilter<CraftComponent, ItemIdData, CurrentItem> CraftedItem;

        protected abstract IPriceCalculator PriceCalculator { get; }
        protected abstract ThePayButton CompleteButton { get; }
        protected abstract SpeedUpButtonView SpeedUpButton { get; }

        private float _updatePriceTimer;
        private int _speedUpSeconds;
        private uint _priceToComplete;
        private int _currentItemId;
        protected EcsEntity CurrentConfig;

        protected override void OnEnable()
        {
            base.OnEnable();

            CurrentConfig = CraftedItem.GetEntity(0);
            _currentItemId = CurrentConfig.Get<ItemIdData>().Id;
            
            CraftComponent craftConfig = CurrentConfig.Get<CraftComponent>();
            _speedUpSeconds = craftConfig.SpeedUpTime;
            
            InitTimer(craftConfig);
            InitCompleteButton(craftConfig);
            InitSpeedUpButton();

            OnEnableInternal();
        }

        private void InitTimer(CraftComponent craftConfig)
        {
            if (CurrentConfig.Has<ViewComponent<CraftTimerView>>())
            {
                CraftTimerView timerView = CurrentConfig.Get<ViewComponent<CraftTimerView>>().View;
                timerView.SetEndTime(craftConfig.EndTime);
                timerView.Show();
                timerView.TimeUpdater.Changed += PlayTimerTickSound;
            }
        }

        private void PlayTimerTickSound()
        {
            World.NewEntity().Get<SoundPoint>().SoundAssetPath = AudioBank.CraftTimerTick;
        }

        protected override void OnUpdate()
        {
            base.OnUpdate();

            UpdateCompletePrice(Time.deltaTime);

            if (IsCraftCompleted())
            {
                CompleteCraft();
                HideCraftTimer();
            }
        }

        private void HideCraftTimer()
        {
            if (CurrentConfig.Has<ViewComponent<CraftTimerView>>())
            {
                CraftTimerView timerView = CurrentConfig.Get<ViewComponent<CraftTimerView>>().View;
                timerView.Hide();
            }
        }

        protected override void OnDisable()
        {
            base.OnDisable();

            StopTickSound();
            CompleteButton.Clicked -= OnCompleteClicked;
            CompleteButton.Hide();
            SpeedUpButton.AdsFinished -= OnSpeedUpByPromoClicked;
            SpeedUpButton.Clicked -= SendAdsAnalyticsClicked;
            SpeedUpButton.AdsStarted -= SendAdsAnalyticsStarted;
            SpeedUpButton.AdsFinished -= SendAdsAnalyticsFinished;
            SpeedUpButton.Hide();
        }

        private void StopTickSound()
        {
            if (CurrentConfig.Has<ViewComponent<CraftTimerView>>())
            {
                CraftTimerView timerView = CurrentConfig.Get<ViewComponent<CraftTimerView>>().View;
                timerView.TimeUpdater.Changed -= PlayTimerTickSound;
            }
        }

        protected abstract void OnEnableInternal();

        protected abstract void CompleteCraft();

        protected virtual async void OnCompleteClicked()
        {
            if (!PlayerManager.Session.Inventory.IsEnough(CurrencyType.Hard, _priceToComplete))
            {
                BankTransferController bankTransferController = new(Services, SceneLoader, PopupViewService);
                if (!await bankTransferController.TryShowBankTransferConfirmation(CurrencyType.Hard, _priceToComplete))
                {
                    CompleteButton.SetState(ThePayButtonState.Locked);
                    CompleteButton.SetAttractionActive(true);
                    
                    World.NewEntity().Get<SetResourcesTopPanelEffect>();
                    World.NewEntity().Get<NotEnoughCurrencyEvent>().Init(_currentItemId, CurrencyType.Hard, CurrencyOperationType.Craft);
                    return;
                }
            }
            
            await RequestHelper.CreateForceCraftRequest(World, _currentItemId, _priceToComplete, ForceCraftItemType.Hard).IsDone();
        }
        
        private void OnSpeedUpByPromoClicked(AdsResult showResult)
        {
            if (showResult != AdsResult.Finished)
            {
                GameLogger.LogWarning($"[Craft] Craft speed up for promo is not finished. Item: {_currentItemId}.");
                return;
            }
            
            PerformCraftSpeedUp().Forget();
        }
        
        private void SendAdsAnalyticsClicked()
        {
            SendAdsAnalyticsRequest(AdsActions.Click);
        }
        
        private void SendAdsAnalyticsStarted()
        {
            SendAdsAnalyticsRequest(AdsActions.Start);
        }
        
        private void SendAdsAnalyticsFinished(AdsResult showResult)
        {
            SendAdsAnalyticsRequest(showResult);
        }

        private async UniTaskVoid PerformCraftSpeedUp()
        {
            int itemId = _currentItemId;
            TimerType timerType = TimerType.Craft;
            if (!PlayerManager.Session.Timers.TryGetTimer(itemId, timerType, out PlayerTimer craftProcess))
            {
                GameLogger.LogWarning($"[{nameof(BaseCraftStateSystem<TStateMachine>)}] There's no craft process to speed up. Item: {itemId}.");
                return;
            }

            FullScreenLocker.Lock();
            
            await TrySpeedupTimer(craftProcess, itemId);
            await SpendPromo();

            UpdateCraftParameters(itemId, timerType);
            
            FullScreenLocker.Unlock();
        }

        private async UniTask TrySpeedupTimer(PlayerTimer craftProcess, int itemId)
        {
            int duration = (int) craftProcess.Duration;
            int speedUpSeconds = _speedUpSeconds >= duration ? duration : _speedUpSeconds;
            
            Result<ChangeTimerResponse> changeTimer = await MetaNet.ChangeTimer((Network.MetaNet.TimerType)craftProcess.TimerType, craftProcess.ItemId, -1000 * speedUpSeconds);
            if (changeTimer.IsFailure)
            {
                throw new InvalidOperationException("Speed up cancelled");
            };

            PlayerManager.Session.Timers.ChangeTimer(changeTimer.Value.Timer);
        }

        private async UniTask SpendPromo()
        {
            Result removeItemFromInventory = await MetaNet.RemoveItemFromInventory(AdsIdents.PROMO_ITEM_ID);
            if (removeItemFromInventory.IsFailure)
            {
                GameLogger.LogWarning($"[{nameof(BaseCraftStateSystem<TStateMachine>)}] Promo spend error: {removeItemFromInventory.Error}");
            }
        }

        private void UpdateCraftParameters(int itemId, TimerType timerType)
        {
            if (!PlayerManager.Session.Timers.TryGetTimer(itemId, timerType, out PlayerTimer craftProcess))
            {
                return;
            }

            CurrentConfig.Get<CraftComponent>().EndTime = craftProcess.EndTime;
            if (CurrentConfig.Has<ViewComponent<CraftTimerView>>())
            {
                CurrentConfig.Get<ViewComponent<CraftTimerView>>().View.SetEndTime(craftProcess.EndTime);
            }
        }

        private bool IsCraftCompleted()
        {
            foreach (int index in EndedTimers)
            {
                ref TimerEndEvent timerEndEvent = ref EndedTimers.Get1(index);
                if (timerEndEvent.Type != TimerType.Craft)
                {
                    continue;
                }

                if (_currentItemId == EndedTimers.Get1(index).ItemId)
                {
                    return true;
                }
            }

            return false;
        }

        private void UpdateCompletePrice(float deltaTime)
        {
            _updatePriceTimer -= deltaTime;
            if (_updatePriceTimer > 0)
            {
                return;
            }

            _updatePriceTimer = _UPDATE_PRICE_FREQUENCY_;

            if (!CurrentConfig.Has<CraftComponent>())
            {
                return;
            }
            
            UpdateCompletePriceView(CurrentConfig.Get<CraftComponent>());
        }

        private void InitCompleteButton(CraftComponent craftConfig)
        {
            CompleteButton.SetText(Localization.Get(TabIdents.Localization.COMPLETE_TEXT));
            CompleteButton.SetSubText(Localization.Get(TabIdents.Localization.COMPLETE_SUB_TEXT));
            InitPriceView(craftConfig);
            UpdateCompletePriceView(craftConfig);
            
            CompleteButton.Show();
            CompleteButton.Clicked += OnCompleteClicked;
        }

        private void InitSpeedUpButton()
        {
            string speedUpTime = UiHelpers.FormatTime(_speedUpSeconds, Localization);
            SpeedUpButton.SetCaption(Localization.Get(TabIdents.Localization.SPEEDUP_TEXT));
            SpeedUpButton.SetSubText(string.Format(Localization.Get(TabIdents.Localization.SPEEDUP_SUB_TEXT), speedUpTime));
            
            SpeedUpButton.Show();
            SpeedUpButton.Clicked += SendAdsAnalyticsClicked;
            SpeedUpButton.AdsStarted += SendAdsAnalyticsStarted;
            SpeedUpButton.AdsFinished += OnSpeedUpByPromoClicked;
            SpeedUpButton.AdsFinished += SendAdsAnalyticsFinished;
        }

        private void UpdateCompletePriceView(CraftComponent craftConfig)
        {
            long remainingSeconds = craftConfig.EndTime - ServerTimeService.UtcNow.GetUtcTimeInSeconds();
            
            if (TryUpdateDiscount(craftConfig, remainingSeconds, out uint discountedPrice))
            {
                _priceToComplete = discountedPrice;
                return;
            }

            _priceToComplete = (uint) PriceCalculator.Calculate(remainingSeconds, craftConfig.CraftCostToComplete.Price, craftConfig.CraftTime);
            CompleteButton.UpdatePrice(_priceToComplete);
        }

        private void InitPriceView(CraftComponent craftConfig)
        {
            if (CurrentConfig.Has<DiscountData>())
            {
                DiscountData discountData = CurrentConfig.Get<DiscountData>();
                CompleteButton.SetPrice((CurrencyType)craftConfig.CraftCostToComplete.CurrencyType, discountData.Price, discountData.DiscountPrice);
            }
            else
            {
                CompleteButton.SetPrice((CurrencyType)craftConfig.CraftCostToComplete.CurrencyType, craftConfig.CraftCostToComplete.Price);
            }
        }
        
        private bool TryUpdateDiscount(CraftComponent craftConfig, long remainingSeconds, out uint discountedPrice)
        {
            if (CurrentConfig.Has<DiscountData>())
            {
                DiscountData discountData = CurrentConfig.Get<DiscountData>();
                int discountPrice = discountData.DiscountPrice < 0
                    ? PriceCalculator.Calculate(remainingSeconds, craftConfig.CraftCostToComplete.Price, craftConfig.CraftTime)
                    : discountData.DiscountPrice;
                CompleteButton.SetPrice((CurrencyType)craftConfig.CraftCostToComplete.CurrencyType, discountData.Price, discountPrice);
                discountedPrice = discountData.Price;
                return true;
            }

            discountedPrice = 0;
            return false;
        }
        
        private void SendAdsAnalyticsRequest(AdsActions action)
        {
            SendAdsAnalyticsRequest(AdsResult.Skipped, action: action);
        }
        
        private void SendAdsAnalyticsRequest(AdsResult showResult, AdsActions action = AdsActions.None)
        {
            ItemType itemType = CurrentConfig.Get<ItemTypeData>().ItemType;
            World.NewEntity().Get<AdsAnalyticsRequest>().Construct($"{_currentItemId}", $"{itemType.ToString()}_craft_speed", showResult, action, AnalyticsHelper.GetAllRewardsAnalyticsRow(new List<ItemComponent>
            {
                new()
                {
                    Id = _currentItemId,
                    Count = 1
                }
            }));
        }
    }
}