using System;
using Client.Common.Configs.Components;
using Client.Common.Configs.Components.Items;
using Client.Common.Currency;
using Client.Common.Network.MetaNet;
using Client.Common.NpcTab.Components;
using Client.Common.NpcTab.Timer;
using Client.Common.Player.Controllers;
using Client.Common.UI.InputLockService;
using Client.Utils.ECS.EcsTaskTools;
using Client.Utils.ECS.LocalWorld;
using Client.Utils.GameLogger;
using Client.Utils.ResultTool.Results;
using Cysharp.Threading.Tasks;
using JetBrains.Annotations;
using Leopotam.Ecs;

namespace Client.Common.NpcTab.Systems.Craft
{
    public class CraftForceCompleteSystem : IEcsRunSystem, IEcsDestroySystem
    {
        [UsedImplicitly]
        protected readonly LocalEcsWorld World = default;
        [UsedImplicitly]
        protected readonly MetaNet MetaNet = default;
        [UsedImplicitly]
        protected readonly FundsChecker _fundsChecker = default;
        [UsedImplicitly]
        protected readonly PlayerManager _playerManager = default;
        [UsedImplicitly]
        protected readonly FullScreenLocker _fullScreenLocker = default;
        
        [UsedImplicitly]
        protected readonly EcsFilter<ForceCraftRequest, ItemIdData, PriceData, EcsTaskConfig> _forceCraftRequests = default;

        public void Destroy()
        {
            _fullScreenLocker.Unlock();
        }
        
        public void Run()
        {
            foreach (int craftIndex in _forceCraftRequests)
            {
                EcsTask task = _forceCraftRequests.Get4(craftIndex).Task;

                if (task.IsComplete)
                {
                    _forceCraftRequests.GetEntity(craftIndex).Destroy();
                    continue;
                }
                
                if (task.Status == EcsTask.States.Processed)
                {
                    continue;
                }
                
                ForceCraftRequest request = _forceCraftRequests.Get1(craftIndex);
                int itemId = _forceCraftRequests.Get2(craftIndex).Id;
                PriceData priceData = _forceCraftRequests.Get3(craftIndex);

                if (request.Type is ForceCraftItemType.Hard)
                {
                    if (!_playerManager.Session.Inventory.IsEnough(priceData.CurrencyType, priceData.Price))
                    {
                        _fundsChecker.PerformNotEnoughFundsAction(itemId, priceData, CurrencyOperationType.Force);
                        task.Status = EcsTask.States.Cancelled;
                        continue;
                    }
                }
                
                PerformForceCraft(itemId, request.Type, task);
            }
        }

        private async void PerformForceCraft(int itemId, ForceCraftItemType type, EcsTask task)
        {
            task.Status = EcsTask.States.Processed;
            _fullScreenLocker.Lock();
            try
            {
                ClientTimerItem updatedTimer = await ForceCraft(itemId, type);
                _playerManager.Session.Timers.ChangeTimer(updatedTimer);
                await OnForceCraft(itemId);
                task.Status = EcsTask.States.Done;
            }
            catch(InvalidOperationException exception)
            {
                GameLogger.LogError($"[ForceCraft] {exception.Message}");
                task.Status = EcsTask.States.Cancelled;
            }
            finally
            {
                _fullScreenLocker.Unlock();
            }
        }

        private async UniTask<ClientTimerItem> ForceCraft(int itemId, ForceCraftItemType type)
        {
            Result<ClientForceCraftItemResponse> forceCraft = await MetaNet.ForceCraft(itemId, type);
            if (forceCraft.IsFailure)
            {
                throw new InvalidOperationException($"Force craft of {itemId} by {type} cancelled");
            }

            return forceCraft.Value.Timer;
        }

        protected virtual UniTask OnForceCraft(int itemId)
        {
            return UniTask.CompletedTask;
        }
    }
}