using System;
using Client.Common.Configs.Components;
using Client.Common.Configs.Components.Items;
using Client.Common.Currency;
using Client.Common.Network.MetaNet;
using Client.Common.NpcTab.Components;
using Client.Common.NpcTab.Timer;
using Client.Common.Player.Controllers;
using Client.Common.UI.InputLockService;
using Client.Utils.ECS.EcsTaskTools;
using Client.Utils.ECS.LocalWorld;
using Client.Utils.GameLogger;
using Client.Utils.ResultTool.Results;
using Cysharp.Threading.Tasks;
using Leopotam.Ecs;

namespace Client.Common.NpcTab.Systems.Craft
{
    public class CraftSystem : IEcsRunSystem
    {
        private readonly LocalEcsWorld _world = default;
        private readonly MetaNet _metaNet = default;
        private readonly FundsChecker _fundsChecker = default;
        private readonly PlayerManager _playerManager = default;
        private readonly FullScreenLocker _fullScreenLocker = default;

        private readonly EcsFilter<CraftRequest, ItemIdData, PriceData, EcsTaskConfig> _craftRequests = default;
        
        public void Run()
        {
            ProcessCraft();
        }

        private void ProcessCraft()
        {
            foreach (int craftIndex in _craftRequests)
            {
                EcsTask task = _craftRequests.Get4(craftIndex).Task;

                if (task.IsComplete)
                {
                    _craftRequests.GetEntity(craftIndex).Destroy();
                    continue;
                }

                if (task.Status == EcsTask.States.Processed)
                {
                    continue;
                }

                int itemId = _craftRequests.Get2(craftIndex).Id;
                PriceData priceData = _craftRequests.Get3(craftIndex);

                if (!_playerManager.Session.Inventory.IsEnough(priceData.CurrencyType, priceData.Price))
                {
                    _fundsChecker.PerformNotEnoughFundsAction(itemId, priceData, CurrencyOperationType.Craft);
                    task.Status = EcsTask.States.Cancelled;
                    continue;
                }

                StartCraft(itemId, task).Forget();
            }
        }

        private async UniTaskVoid StartCraft(int itemId, EcsTask task)
        {
            try
            {
                task.Status = EcsTask.States.Processed;

                _fullScreenLocker.Lock();
                ClientTimerItem newTimer = await Craft(itemId);
                _playerManager.Session.Timers.ChangeTimer(newTimer);

                task.Status = EcsTask.States.Done;
            }
            catch(InvalidOperationException exception)
            {
                GameLogger.LogError($"[Craft] {exception.Message}");
                task.Status = EcsTask.States.Cancelled;
            }
            finally
            {
                _fullScreenLocker.Unlock();
            }
        }

        private async UniTask<ClientTimerItem> Craft(int itemId)
        {
            Result<ClientCraftItemResponse> craft = await _metaNet.Craft(itemId);
            if (craft.IsFailure)
            {
                throw new InvalidOperationException($"Craft of {itemId} cancelled");
            }
            return craft.Value.Timer;
        }

    }
}