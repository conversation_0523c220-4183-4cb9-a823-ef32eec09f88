using Client.Common.Configs.Components;
using Client.Common.Configs.Components.Items;
using Client.Common.Network.MetaNet;
using Client.Common.NpcTab.Components;
using Client.Common.NpcTab.Timer;
using Leopotam.Ecs;

namespace Client.Common.NpcTab.Systems.UpdateSystems
{
    public class CraftComponentUpdateSystem : IEcsRunSystem
    {
        private readonly EcsFilter<ItemIdData, CraftComponent> _craftConfigs;
        private readonly EcsFilter<TimerEndEvent> _timerEnds;
        private readonly EcsFilter<ItemIdData, PieceData> _pieceConfigs;
        private readonly EcsFilter<TimerStartEvent> _timerStarts;
        
        public void Run()
        {
            foreach (int timerIndex in _timerEnds)
            {
                ref TimerEndEvent timerEndEvent = ref _timerEnds.Get1(timerIndex);
                if (timerEndEvent.Type != global::Common.TimerType.Craft)
                {
                    continue;
                }

                TryRemoveCraftConfig(timerEndEvent.ItemId);
            }
        }
        
        private void TryRemoveCraftConfig(int endedId)
        {
            foreach (int configIndex in _craftConfigs)
            {
                if (_craftConfigs.Get1(configIndex).Id == endedId)
                {
                    EcsEntity config = _craftConfigs.GetEntity(configIndex);
                    config.Del<CraftComponent>();
                    config.Get<CraftCompletedItemMarker>();
                    config.Get<CraftedItem>();
                }
            }
        }
    }
}