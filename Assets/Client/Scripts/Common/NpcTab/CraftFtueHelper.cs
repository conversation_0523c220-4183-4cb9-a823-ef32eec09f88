using Client.Common.Configs.Components;
using Client.Common.Configs.Components.Items;
using Client.Common.Network.MetaNet;
using Client.Common.NpcTab.Timer;
using Client.Utils.ECS.LocalWorld;
using Leopotam.Ecs;

namespace Client.Common.NpcTab
{
    public class CraftFtueHelper
    {
        private readonly EcsFilter<TimerEndEvent> _timerEnds;
        private readonly EcsFilter<TimerStartEvent> _timerStarts;
        private readonly EcsFilter<ItemIdData, CraftCompletedItemMarker> _completedItems;
        
        public CraftFtueHelper(LocalEcsWorld world)
        {
            _timerEnds = world.GetFilter<EcsFilter<TimerEndEvent>>();
            _timerStarts = world.GetFilter<EcsFilter<TimerStartEvent>>();
            _completedItems = world.GetFilter<EcsFilter<ItemIdData, CraftCompletedItemMarker>>();
        }

        public bool TryFindCompletedItem(int itemId)
        {
            foreach (int index in _completedItems)
            {
                if (_completedItems.Get1(index).Id == itemId)
                {
                    return true;
                }
            }
            return false;
        }
        
        public bool CheckForCraftTimer(int itemId, bool crafting)
        {
            if (crafting)
            {
                foreach (int index in _timerEnds)
                {
                    ref TimerEndEvent timerEndEvent = ref _timerEnds.Get1(index);
                    if (timerEndEvent.Type == global::Common.TimerType.Craft && timerEndEvent.ItemId == itemId)
                    {
                        return false;
                    }
                }
            }
            else
            {
                foreach (int index in _timerStarts)
                {
                    ref TimerStartEvent timerStart = ref _timerStarts.Get1(index);
                    if (timerStart.Type == global::Common.TimerType.Craft && timerStart.ItemId == itemId)
                    {
                        return true;
                    }
                }
            }
            return crafting;
        }
    }
}