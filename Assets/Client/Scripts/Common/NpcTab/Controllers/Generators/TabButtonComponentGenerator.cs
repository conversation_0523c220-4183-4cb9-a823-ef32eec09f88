using System.Collections.Generic;
using Client.Common.Configs.Components;
using Client.Common.Network.MetaNet;
using Client.Common.NpcTab.Components;
using Client.Common.NpcTab.Data.Tabs;
using Client.Utils.ECS.LocalWorld;
using Leopotam.Ecs;
using UnityEngine;

namespace Client.Common.NpcTab.Controllers.Generators
{
    public class TabButtonComponentGenerator
    {
        private readonly LocalEcsWorld _world;

        public TabButtonComponentGenerator(LocalEcsWorld world)
        {
            _world = world;
        }
        
        public TabButtonComponentGenerator Generate<TMarker>(TabButtonContext buttonContext)
            where TMarker : struct
        {
            return Generate<TMarker>(_world.NewEntity(), buttonContext);
        }
        
        public TabButtonComponentGenerator Generate<TMarker>(TabButtonContext buttonContext, global::Common.TimerType timerType)
            where TMarker : struct
        {
            return Generate<TMarker>(_world.NewEntity(), buttonContext, timerType);
        }
        
        public TabButtonComponentGenerator Generate<TMarker>(EcsEntity config, TabButtonContext buttonContext, global::Common.TimerType timerType)
            where TMarker : struct
        {
            GenerateInternal<TMarker>(config, buttonContext);
            config.Get<TimerTypeData>().Type = timerType;

            return this;
        }
        
        public TabButtonComponentGenerator Generate<TMarker>(EcsEntity config, TabButtonContext buttonContext)
            where TMarker : struct
        {
            GenerateInternal<TMarker>(config, buttonContext);

            return this;
        }
        
        public TabButtonComponentGenerator Generate<TMarker>(IEnumerable<TabButtonContext> buttonContexts)
            where TMarker : struct
        {
            foreach (TabButtonContext buttonController in buttonContexts)
            {
                GenerateInternal<TMarker>(_world.NewEntity(), buttonController);
            }

            return this;
        }
        
        private void GenerateInternal<TMarker>(EcsEntity config, TabButtonContext buttonContext)
            where TMarker : struct
        {
            config.Get<TabButtonComponent>().ButtonContext = buttonContext;
            config.Get<TMarker>();
        }
    }
}