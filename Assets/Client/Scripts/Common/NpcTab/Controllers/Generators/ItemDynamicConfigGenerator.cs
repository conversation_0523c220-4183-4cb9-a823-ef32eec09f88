using Client.Common.Configs.Components;
using Client.Common.Configs.Components.Items;
using Client.Common.Configs.Providers;
using Client.Common.CraftReadyItems;
using Client.Common.Items;
using Client.Common.Items.QuantityProviders;
using Client.Common.Network.MetaNet;
using Client.Common.Player.Controllers;
using Client.Common.Player.ProgressItems;
using Client.Common.Player.Timers;
using Client.Utils.ECS.LocalWorld;
using Leopotam.Ecs;
using CurrencyType = Common.CurrencyType;

namespace Client.Common.NpcTab.Controllers.Generators
{
    //todo: use ConsumablesDynamicDataGenerator or some other config-related generator instead
    public class ItemDynamicConfigGenerator
    {
        private readonly LocalEcsWorld _world;
        private readonly PlayerManager _playerManager;
        private readonly ItemData _itemData;
        private readonly QuantifiedLocalizationService _quantifiedLocalization;
        private readonly EquippedItemProvider _equippedItemProvider;
        private readonly DefaultItemProvider _defaultItemProvider;
        private readonly CraftReadyItemsService _craftReadyItemsService;

        public ItemDynamicConfigGenerator(LocalEcsWorld world, PlayerManager playerManager, ItemData itemData, CraftReadyItemsService craftReadyItemsService, QuantifiedLocalizationService quantifiedLocalization)
        {
            _world = world;
            _playerManager = playerManager;
            _itemData = itemData;
            _craftReadyItemsService = craftReadyItemsService;
            _quantifiedLocalization = quantifiedLocalization;
            _equippedItemProvider = new EquippedItemProvider(playerManager.Session.Progress);
            _defaultItemProvider = new DefaultItemProvider();
        }
        
        public void InitConfig(EcsEntity config, ProgressItems.Item item, ClientItemData itemInfo)
        {
            config.Get<ItemMarker>();
            config.Get<ItemIdData>().Id = item.ItemId;
            config.Get<CountData>().Value = _playerManager.Session.Inventory.GetItemCount(item.ItemId);

            config.Get<PieceData>().UpdateCount(item.PiecesCurrent, item.PiecesTotal);
            config.Get<PieceData>().PartId = item.PieceId;
            config.Get<PriceData>().Construct(itemInfo.CraftCost, (CurrencyType) itemInfo.CraftCurrency);
            config.Get<ProgressComponent>().Construct(item.Status == ProgressItems.Status.Locked, item.BloodToUnlock, item.ProgressStep);

            if (item.ItemId == _defaultItemProvider.Get(item.Type))
            {
                config.Get<DefaultItemMarker>();
            }

            if (item.ItemId == _equippedItemProvider.Get(item.Type))
            {
                config.Get<EquippedItemMarker>();
            }
        }
        
        public EcsEntity InitItemConfig(ProgressItems.Item item, out ClientItemData itemInfo)
        {
            EcsEntity config = _world.NewEntity();
            config.Get<ItemTypeData>().ItemType = item.Type;
            itemInfo = _itemData.GetItemDataById(item.ItemId);
            InitConfig(config, item, itemInfo);
            InitDescription(config, item.ItemId);
            return config;
        }
        
        public void InitDescription(EcsEntity config, int itemId)
        {
            string name = _quantifiedLocalization.GetQuantifiedName(itemId, 1);
            string description = _quantifiedLocalization.GetQuantifiedDescription(itemId, 1);

            config.Get<ItemDescriptionData>().Construct(name, description);
        }
        
        public void InitCraftProcess(EcsEntity config, ClientItemData itemInfo)
        {
            if (TryGetCraftProcess(itemInfo.Id, global::Common.TimerType.Craft, out PlayerTimer craftProcess))
            {
                InitCraftConfig(config, itemInfo, craftProcess);
            }
        }
        
        private bool TryGetCraftProcess(int itemId, global::Common.TimerType timerType, out PlayerTimer craftProcess)
        {
            craftProcess = _playerManager.Session.Timers.GetTimer(itemId, timerType);
            return !craftProcess.IsFinished();
        }
        
        public static void InitCraftConfig(EcsEntity config, ClientItemData itemInfo, PlayerTimer craftProcess)
        {
            ref CraftComponent craftConfig = ref config.Get<CraftComponent>();
            craftConfig.CraftTime = itemInfo.CraftTime;
            craftConfig.StartTime = craftProcess.StartTime;
            craftConfig.EndTime = craftProcess.EndTime;
            craftConfig.CraftCostToComplete = new PriceData().Construct(itemInfo.CraftCostToComplete, CurrencyType.Hard);
            craftConfig.SpeedUpTime = itemInfo.CraftTimeDecreasePerAdViewing;
        }
        
        public bool TryFindInReadyCrafts(int itemId)
        {
            return _craftReadyItemsService.ReadyItems.Contains(itemId);
        }
    }
}