using System.Collections.Generic;
using System.Threading;
using Client.Common.Configs.Components;
using Client.Common.CraftReadyItems;
using Client.Common.Items;
using Client.Common.Network.MetaNet;
using Client.Common.NpcTab.Data;
using Client.Common.Player.Controllers;
using Client.Common.Player.ProgressItems;
using Client.Common.Player.ProgressItems.Providers;
using Client.Common.Player.Timers;
using Client.Utils.AsyncLoadHelper;
using Cysharp.Threading.Tasks;
using Leopotam.Ecs;
using TimerType = Common.TimerType;

namespace Client.Common.NpcTab.Controllers.Generators
{
    public class ItemCraftGenerationData
    {
        public EcsEntity Entity;
        public ClientItemData ItemData;

        public ItemCraftGenerationData(EcsEntity entity, ClientItemData itemData)
        {
            Entity = entity;
            ItemData = itemData;
        }
    }

    public class ItemCraftGenerator : IPayloadedGenerator<ItemCraftGenerationData>
    {
        private readonly PlayerTimers _playerTimers;
        private readonly PlayerInventory _playerInventory;
        private readonly CraftReadyItemsService _craftReadyItemsService;
        private readonly BaseProgressItemsProvider _itemsProvider;

        private readonly ItemCraftComponentGenerator _itemCraftComponentGenerator;
        private readonly HashSet<int> _takenItems;

        public ItemCraftGenerator(
            PlayerInventory playerInventory, 
            PlayerTimers playerTimers,
            CraftReadyItemsService craftReadyItemsService, 
            BaseProgressItemsProvider itemsProvider)
        {
            _playerInventory = playerInventory;
            _playerTimers = playerTimers;
            _craftReadyItemsService = craftReadyItemsService;
            _itemsProvider = itemsProvider;

            _itemCraftComponentGenerator = new ItemCraftComponentGenerator();
            _takenItems = GetTakenItems();
        }

        public UniTask Generate(ItemCraftGenerationData payload, CancellationToken cancellationToken = default)
        {
            int itemId = payload.ItemData.Id;

            if (TryFindGetItemCrafts(itemId))
            {
                payload.Entity.Get<EquipReadyItemMarker>();

                return UniTask.CompletedTask;
            }

            if (TryFindInReadyCrafts(itemId))
            {
                payload.Entity.Get<CraftCompletedItemMarker>();

                return UniTask.CompletedTask;
            }

            if (TryGetCraftProcess(itemId, TimerType.Craft, out PlayerTimer craftProcess))
            {
                return _itemCraftComponentGenerator.Generate(new CraftComponentGenerationData(payload.Entity, payload.ItemData, craftProcess), cancellationToken);
            }

            return UniTask.CompletedTask;
        }

        private bool TryFindGetItemCrafts(int itemId)
        {
            return _takenItems.Contains(itemId);
        }

        private bool TryFindInReadyCrafts(int itemId)
        {
            return _craftReadyItemsService.ReadyItems.Contains(itemId);
        }

        private bool TryGetCraftProcess(int itemId, TimerType timerType, out PlayerTimer craftProcess)
        {
            craftProcess = _playerTimers.GetTimer(itemId, timerType);

            return !craftProcess.IsFinished();
        }
        
        private HashSet<int> GetTakenItems()
        {
            List<int> itemsByType = _playerInventory.GetItemsByType(_itemsProvider.ProviderItemType);
            HashSet<int> takenItems = new HashSet<int>(); 
            foreach (int idx in itemsByType)
            {
                takenItems.Add(_playerInventory.GetData(idx).Id);
            }
            return takenItems;
        }
    }
}