using Client.Common.NpcTab.Timer;
using Client.Common.Quests.Goals.Types;
using Leopotam.Ecs;

namespace Client.Common.Quests.Goals.Systems
{
    public class CraftItemQuestProgressSystem : QuestProgressSystem<CraftItemGoal>
    {
        private readonly EcsFilter<ItemCraftedEvent> _craftFilter = default;
        
        protected override void OnUpdate()
        {
            foreach (int craftIndex in _craftFilter)
            {
                int craftedId = _craftFilter.Get1(craftIndex).ItemId;
                foreach (int goalIndex in ActiveGoals)
                {
                    int goalTargetId = ActiveGoals.Get1(goalIndex).ItemId;
                    
                    if (craftedId == goalTargetId)
                    {
                        Progress.Add(ActiveGoals.GetEntity(goalIndex), 1);
                    }
                }
            }
        }
    }
}