using System;
using System.Collections.Generic;
using System.Threading;
using Client.Common.Configs.Components.Items;
using Client.Common.HumanoidBody.Converters;
using Client.Common.Network.MetaNet;
using Client.Common.NpcTab.Craft;
using Client.Common.NpcTab.Timer;
using Client.Common.Player.Controllers;
using Client.Common.Player.Timers;
using Client.Utils.ECS.LocalWorld;
using Client.Utils.GameLogger;
using Client.Utils.ResultTool.Results;
using Common;
using Cysharp.Threading.Tasks;
using Leopotam.Ecs;
using BodyPart = Client.Common.Network.MetaNet.BodyPart;
using TimerType = Client.Common.Network.MetaNet.TimerType;

namespace Client.Common.Traumas
{
    public class HealTraumaHelper
    {
        private readonly MetaNet _metaNet;
        private readonly LocalEcsWorld _world;
        private readonly PlayerTimers _timers;
        
        private readonly BodyPartIdToBodyPartConverter _bodyPartIdToBodyPartConverter = new();

        public HealTraumaHelper(MetaNet metaNet, LocalEcsWorld world, PlayerTimers timers)
        {
            _metaNet = metaNet;
            _world = world;
            _timers = timers;
        }
        
        public async UniTask HealForBottle(int itemId, CancellationToken cancellationToken = default)
        {
            await HealInternal(itemId, cancellationToken);
        }

        public async UniTask<Result> HealForHard(PlayerTimer[] timers, int price, CancellationToken cancellationToken = default)
        {
            Result result = await _metaNet.RemoveItemFromInventory(PlayerInventory.Items.HARD_CURRENCY, price, cancellationToken);
            if (result.IsFailure)
            {
                GameLogger.LogError("Failed to pay for trauma heal");
                return result;
            }
            
            List<UniTask> changeTasks = new List<UniTask>();
            foreach (PlayerTimer timer in timers)
            {
                changeTasks.Add(ChangeTimersInternal(timer, cancellationToken));
            }

            await UniTask.WhenAll(changeTasks);
            return Result.Success();
        }
        
        private async UniTask ChangeTimersInternal(PlayerTimer timer, CancellationToken cancellationToken)
        {
            Result<ChangeTimerResponse> response = await _metaNet.ChangeTimer(TimerType.Trauma, timer.ItemId, -(int) timer.Duration * 1000, cancellationToken);

            if (response.IsFailure)
            {
                throw new InvalidOperationException("Change timers for hard cancelled");
            }

            _world.NewEntity().Get<TraumaHealed>().Id = timer.ItemId;
            _timers.ChangeTimer(response.Value.Timer);
        }

        public EcsEntity UseItem(int itemId)
        {
            EcsEntity itemUsedConfig = _world.NewEntity();
            itemUsedConfig.Get<ItemUsedEvent>() = new ItemUsedEvent().Construct(itemId, "Heal");
            itemUsedConfig.Get<ItemTypeData>().ItemType = ItemType.Bottle;
            return itemUsedConfig;
        }

        private async UniTask HealInternal(int itemId, CancellationToken cancellationToken)
        {
            BodyPart traumaBodyPart = _bodyPartIdToBodyPartConverter.Convert(itemId);
            Result<HealResponse> healData = await _metaNet.HealBodyPart(traumaBodyPart, cancellationToken);

            if (healData.IsFailure)
            {
                throw new InvalidOperationException("HealBodyPart cancelled");
            }

            _world.NewEntity().Get<TraumaHealed>().Id = itemId;
            _timers.ChangeTimer(healData.Value.Timer);
        }
    }
}