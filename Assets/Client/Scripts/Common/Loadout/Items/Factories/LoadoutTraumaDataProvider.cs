using System.Collections.Generic;
using System.Threading;
using Client.Common.Ftue.Infrastructure;
using Client.Common.Loadout.Services;
using Client.Common.Network.MetaNet;
using Client.Common.Player.Controllers;
using Client.Common.Player.Timers;
using Client.Common.Traumas.Data;
using Cysharp.Threading.Tasks;
using TimerType = Common.TimerType;

namespace Client.Common.Loadout.Items.Factories
{
    public class LoadoutTraumaDataProvider : ILoadoutItemDataProvider
    {
        private readonly PlayerManager _playerManager;
        private readonly FtueProgress _ftueProgress;

        public LoadoutTraumaDataProvider(PlayerManager playerManager, FtueProgress ftueProgress)
        {
            _playerManager = playerManager;
            _ftueProgress = ftueProgress;
        }

        public LoadoutItemData GetData(LoadoutItemId loadoutId)
        {
            int traumasCount = GetTraumasCount();

            LoadoutItemData traumaData = new();
            traumaData.LoadoutId = loadoutId;
            traumaData.ProgressData = GetTraumaSegmentsData(traumasCount);
            traumaData.HasFang = traumasCount == 0;
            traumaData.IsUnlocked = _ftueProgress.Conditions.IsTraumaFeatureEnabled();
            traumaData.IsClickable = traumaData.IsUnlocked && traumasCount > 0;

            return traumaData;
        }

        public UniTask<LoadoutItemData> Load(LoadoutItemData data, CancellationToken token = default)
        {
            return UniTask.FromResult(data);
        }

        private int GetTraumasCount()
        {
            PlayerTimers.Query traumasQuery =
                PlayerTimers.Query
                            .Get()
                            .WithTimerType(TimerType.Trauma);

            List<PlayerTimer> traumas = _playerManager.Session.Timers.GetTimers(traumasQuery);

            return traumas.Count;
        }

        private static LoadoutProgressData GetTraumaSegmentsData(int traumasCount)
        {
            if (traumasCount == 0)
            {
                return LoadoutProgressData.FullEmpty();
            }
            
            return new LoadoutProgressData
            {
                MaxProgress = TraumaIdents.MAX_TRAUMAS_PER_UNIT,
                CurrentProgress = traumasCount,
                CriticalProgress = TraumaIdents.MAX_TRAUMAS_PER_UNIT
            };
        }
    }
}