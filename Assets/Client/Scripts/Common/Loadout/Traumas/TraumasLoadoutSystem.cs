using System;
using Client.Common.Audio;
using Client.Common.CSV;
using Client.Common.Loadout.Services;
using Client.Common.Network.MetaNet;
using Client.Common.NpcTab.Controllers;
using Client.Common.NpcTab.Timer;
using Client.Common.Player.Controllers;
using Client.Common.TimeGiver.Abstractions;
using Client.Common.Traumas;
using Client.Common.UI.InputLockService;
using Client.Utils.ECS.LocalWorld;
using Client.Utils.ResourceLoading;
using Client.Utils.ViewService;
using Cysharp.Threading.Tasks;
using Leopotam.Ecs;
using Leopotam.Localization;

namespace Client.Common.Loadout.Traumas
{
    public struct TraumasLoadoutRequest
    {
        public Action OnClose;
    }
    public class TraumasLoadoutSystem: IEcsRunSystem
    {
        private readonly ITimeGiver _timeService = default;
        private readonly PlayerManager _playerManager = default;
        private readonly ResourceLoadingService _resourceLoadingService = default;
        private readonly CsvLocalization _localization = default;
        private readonly MetaNet _metaNet = default;
        private readonly LocalEcsWorld _world = default;
        private readonly FullScreenLocker _screenLocker = default;
        private readonly GoogleDocsData _googleDocsData = default;
        private readonly PopupViewService _viewService = default;
        private readonly LoadoutUiService _loadoutUiService = default;
        private readonly BankTransferController _bankTransferController = default;

        private readonly EcsFilter<TraumasLoadoutRequest> _traumasLoadoutRequests = default;
        private readonly EcsFilter<TimerEndEvent> _timerEndEvents = default;
        
        private LoadoutTraumasController _loadoutTraumasController;

        public void Run()
        {
            foreach (int index in _traumasLoadoutRequests)
            {
                OpenScreen(_traumasLoadoutRequests.Get1(index).OnClose);
                _traumasLoadoutRequests.GetEntity(index).Del<TraumasLoadoutRequest>();
            }

            foreach (int _ in _timerEndEvents)
            {
                _loadoutTraumasController?.UpdateView();
            }
        }

        private void OpenScreen(Action onClose)
        {
            if (_loadoutTraumasController is null)
            {
                HealTraumaHelper healTraumaHelper = new(_metaNet, _world, _playerManager.Session.Timers);
                _loadoutTraumasController = new LoadoutTraumasController(_world,
                    _viewService, _playerManager, healTraumaHelper, _resourceLoadingService,
                    _timeService, _localization, default, _screenLocker, _googleDocsData, _loadoutUiService, 
                    _bankTransferController, new UISoundHelper(_world));
                _loadoutTraumasController.Closed += onClose;
            }
            
            _loadoutTraumasController.OpenScreen().Forget();
        }
    }
}