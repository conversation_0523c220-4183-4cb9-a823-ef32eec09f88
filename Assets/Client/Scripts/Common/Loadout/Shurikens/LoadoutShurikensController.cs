using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading;
using Client.Common.Abilities.Data;
using Client.Common.Abilities.Infrastructure.Loaders;
using Client.Common.Audio;
using Client.Common.Audio.Banks;
using Client.Common.Configs;
using Client.Common.Configs.Components;
using Client.Common.Configs.Components.Abilities;
using Client.Common.Configs.Components.Items;
using Client.Common.Configs.Components.Items.TypeMarkers;
using Client.Common.Controllers.EquipHelper;
using Client.Common.Durability;
using Client.Common.Ftue.Infrastructure;
using Client.Common.ItemParts;
using Client.Common.Items;
using Client.Common.Items.Icons;
using Client.Common.Loadout.Items;
using Client.Common.Loadout.Services;
using Client.Common.Loadout.Shurikens.StateMachine.States;
using Client.Common.Network.MetaNet;
using Client.Common.NpcTab.Controllers;
using Client.Common.NpcTab.Data;
using Client.Common.NpcTab.Pieces;
using Client.Common.Player.Controllers;
using Client.Common.Profile.Ability.Circle;
using Client.Common.Profile.Ability.Circle.Presenter;
using Client.Common.ProfileShurikens.DurabilityBlock;
using Client.Common.ProfileShurikens.Item;
using Client.Common.ProfileShurikens.Paint;
using Client.Common.ProfileShurikens.ParamPopup;
using Client.Common.ProfileShurikens.Providers;
using Client.Common.ProfileShurikens.Screen;
using Client.Common.ProfileShurikens.Scroll;
using Client.Common.ProfileShurikens.Visual;
using Client.Common.Purchases.Controllers;
using Client.Common.SceneLoading;
using Client.Common.UI.InputLockService;
using Client.Common.UI.ScrollController.Controllers;
using Client.Utils.CustomTweens.VFXService;
using Client.Utils.CustomTweens.VFXService.Abstractions;
using Client.Utils.ECS.LocalWorld;
using Client.Utils.ResourceLoading;
using Client.Utils.ViewService;
using Cysharp.Threading.Tasks;
using Leopotam.Ecs;
using Leopotam.Localization;
using UnityEngine;

namespace Client.Common.Loadout.Shurikens
{
    public class LoadoutShurikensController
    {
        public event Action OnPainted;
        public event Action OnCloseItem;

        private readonly LocalEcsWorld _world;
        private readonly ConfigService _configService;
        private readonly ViewService _viewService;
        private readonly ResourceLoadingService _resourceLoadingService;
        private readonly PlayerManager _playerManager;
        private readonly ItemData _itemData;
        private readonly CsvLocalization _localization;
        private readonly MetaNet _metaNet;
        private readonly FullScreenLocker _fullScreenLocker;
        private readonly BazarAudio _bazarAudio;
        private readonly AnimatedSceneLoader _sceneLoader;
        private readonly ItemEquipHelper _equipHelper;
        private readonly AbilityGradeService _abilityGradeService;
        private readonly FtueProgress _ftueProgress;
        private readonly PieceInfoUpdater _pieceInfoUpdater;
        private readonly QuantifiedIconLoadingService _quantifiedIconLoadingService;
        private readonly DurabilityRepairService _durabilityRepairService;
        private readonly BankTransferController _bankTransferController;
        private readonly LoadoutUiService _loadoutUiService;
        private readonly DurabilityService _durabilityService;
        private readonly ManualItemPurchaseController _purchaseController;
        private EcsFilter<ItemIdData, ConfigComponent, ResourceConfigsData, ShurikenItemMarker> _itemConfigs;

        private ShurikenItemPresenterFactory _itemPresenterFactory;

        private ITweenManager _tweenManager;

        private readonly Transform _screenRoot;
        private readonly Transform _paramsPopupRoot;
        private readonly ParamsVisual _paramsVisual;
        private readonly AbilityDescriptionFormatter _abilityDescriptionFormatter;
        private CancellationToken _cancellationToken;

        private PlayerInventory PlayerInventory => _playerManager.Session.Inventory;
        private PlayerProgress PlayerProgress => _playerManager.Session.Progress;

        public LoadoutShurikensController(Transform screenRoot,
            Transform paramsPopupRoot,
            ParamsVisual paramsVisual,
            CancellationToken cancellationToken,
            LocalEcsWorld world,
            ConfigService configService,
            ViewService viewService,
            ResourceLoadingService resourceLoadingService,
            PlayerManager playerManager,
            ItemData itemData, 
            CsvLocalization localization,
            MetaNet metaNet, 
            FullScreenLocker fullScreenLocker,
            BazarAudio bazarAudio,
            AnimatedSceneLoader sceneLoader,
            ItemEquipHelper equipHelper, 
            AbilityGradeService abilityGradeService, 
            FtueProgress ftueProgress,
            PieceInfoUpdater pieceInfoUpdater,
            QuantifiedIconLoadingService quantifiedIconLoadingService,
            DurabilityRepairService durabilityRepairService, 
            BankTransferController bankTransferController,
            LoadoutUiService loadoutUiService, 
            DurabilityService durabilityService, 
            ManualItemPurchaseController purchaseController)
        {
            _screenRoot = screenRoot;
            _paramsPopupRoot = paramsPopupRoot;
            _paramsVisual = paramsVisual;
            _cancellationToken = cancellationToken;
            _world = world;
            _configService = configService;
            _viewService = viewService;
            _resourceLoadingService = resourceLoadingService;
            _playerManager = playerManager;
            _itemData = itemData;
            _localization = localization;
            _metaNet = metaNet;
            _fullScreenLocker = fullScreenLocker;
            _bazarAudio = bazarAudio;
            _sceneLoader = sceneLoader;
            _equipHelper = equipHelper;
            _abilityGradeService = abilityGradeService;
            _ftueProgress = ftueProgress;
            _pieceInfoUpdater = pieceInfoUpdater;
            _quantifiedIconLoadingService = quantifiedIconLoadingService;
            _durabilityRepairService = durabilityRepairService;
            _bankTransferController = bankTransferController;
            _loadoutUiService = loadoutUiService;
            _durabilityService = durabilityService;
            _purchaseController = purchaseController;
            _abilityDescriptionFormatter = new AbilityDescriptionFormatter(_configService, _localization, _abilityGradeService);
        }

        public async UniTask OpenShuriken(int id)
        {
            await CreateScreen(id, _cancellationToken);
        }

        public void Init()
        {
            _itemConfigs = _configService.GetFilter<EcsFilter<ItemIdData, ConfigComponent, ResourceConfigsData, ShurikenItemMarker>>();

            _tweenManager = new TweenManager();

            _itemPresenterFactory = new ShurikenItemPresenterFactory(
                _world, _configService, _viewService, _resourceLoadingService,
                PlayerProgress.WeaponStorage, PlayerProgress.BrokenWeaponSkinStorage,
                _durabilityService, _bazarAudio, _playerManager, _quantifiedIconLoadingService);
        }

        private async UniTask CreateScreen(int skinToOpen, CancellationToken cancellationToken)
        {
            ResourceConfig screenResourceConfig = new()
            {
                Path = "Loadout/LoadoutShurikenScreen",
                LoadFrom = LoadFromType.Addressables,
            };

            ProfileShurikenScrollFactory scrollFactory = await CreateScrollFactory(cancellationToken);
            DurabilityBlockController durabilityBlockController = CreateDurabilityBlockController();
            PaintController paintController = CreatePaintController();
            ParamsPlatePresenter paramsPlatePresenter = CreateParamsPlatePresenter();
            AbilityCirclePresenter abilityCirclePresenter = await CreateAbilityCircle(cancellationToken);
            LoadoutShurikensScreenFsm screenFsm = CreateScreenFsm(cancellationToken);

            LoadoutShurikensScreenPresenter shurikensScreenPresenter = null;
            shurikensScreenPresenter = await CreateScreenPresenter(cancellationToken);

            await _viewService.Open<ProfileShurikenScreenPresenter, ProfileShurikenScreenView, ProfileShurikenScreenModel>(
                shurikensScreenPresenter, screenResourceConfig, _screenRoot, cancellationToken: cancellationToken);

            return;

            async UniTask<ProfileShurikenScrollFactory> CreateScrollFactory(CancellationToken token)
            {
                ScrollGenerator scrollGenerator = await new ScrollGenerator().InitAsync(_resourceLoadingService, token);
                return new ProfileShurikenScrollFactory(scrollGenerator, token);
            }

            DurabilityBlockController CreateDurabilityBlockController()
            {
                return new DurabilityBlockController(_configService, PlayerProgress, _resourceLoadingService, _localization, _durabilityService);
            }

            PaintController CreatePaintController()
            {
                PaintRequestController paintRequestController = new(_metaNet);
                return new PaintController(_viewService, _localization, paintRequestController, _fullScreenLocker, _durabilityService, durabilityBlockController);
            }

            ParamsPlatePresenter CreateParamsPlatePresenter()
            {
                return new ParamsPlatePresenter(_paramsVisual);
            }

            async UniTask<AbilityCirclePresenter> CreateAbilityCircle(CancellationToken token)
            {
                string[] abilityConfigKeys = GetAbilityConfigKeys();

                AbilityCircleFactory abilityCircleFactory = new(_configService, _viewService, _resourceLoadingService, _tweenManager, _abilityGradeService);
                AbilityCirclePresenter circlePresenter = await abilityCircleFactory.Create(abilityConfigKeys, _screenRoot, token);
                circlePresenter?.View.SetInteractable(false);

                return circlePresenter;
            }

            LoadoutShurikensScreenFsm CreateScreenFsm(CancellationToken token)
            {
                ItemPartsChecker itemPartsChecker = new(_configService, PlayerInventory);
                PreferredSkinIdProvider preferredSkinProvider = new(_configService, itemPartsChecker);

                return new LoadoutShurikensScreenFsm(
                    skinToOpen, PlayerProgress, PlayerInventory, _localization,
                    _equipHelper, _sceneLoader, preferredSkinProvider, paintController,
                    null, _ftueProgress, token, _fullScreenLocker, _durabilityService, _durabilityRepairService,
                    _bankTransferController, _playerManager, _world, _purchaseController);
            }

            async UniTask<LoadoutShurikensScreenPresenter> CreateScreenPresenter(CancellationToken token)
            {
                List<ShurikenItemPresenter> itemPresenters = (await CreateShurikenItemPresenters(token, skinToOpen)).ToList();
                ProfileShurikenScreenModel screenModel = new(skinToOpen, itemPresenters, null, abilityCirclePresenter);
                LoadoutShurikensScreenPresenter screenPresenter = new(
                    screenModel, _playerManager, screenFsm, _localization, scrollFactory, durabilityBlockController, paintController, null, _viewService, _tweenManager,
                    paramsPlatePresenter, _fullScreenLocker, _paramsPopupRoot, token, _abilityDescriptionFormatter, _pieceInfoUpdater, new UISoundHelper(_world));
                screenPresenter.CloseClicked += CloseAndOpenScroll;
                screenPresenter.ItemEquipped += UpdateLoadoutItem;
                screenPresenter.ItemEquipped += Close;
                screenPresenter.ItemEquipped += OnPainted;
                return screenPresenter;
            }

            void Close()
            {
                shurikensScreenPresenter.Dispose();
                _viewService.CloseWithDestroy(shurikensScreenPresenter, cancellationToken: cancellationToken).Forget();
            }

            void CloseAndOpenScroll()
            {
                _world.NewEntity().Get<PlaySoundClick>().PlayClicksTypes = PlayClicksTypes.Back;
                Close();
                OnCloseItem?.Invoke();
            }
        }

        private void UpdateLoadoutItem()
        {
            _loadoutUiService.UpdateItem(LoadoutItemId.ShurikenSkin);
        }

        private string[] GetAbilityConfigKeys()
        {
            List<string> abilityConfigKeys = new();

            foreach (int index in _itemConfigs)
            {
                EcsEntity configEntity = _itemConfigs.GetEntity(index);
                int itemId = _itemConfigs.Get1(index).Id;

                if (!PlayerInventory.Contains(itemId)) //todo: Improve PlayerInventory, so it can provide configKey.
                {
                    continue;
                }

                if (configEntity.Has<AbilityKeys>())
                {
                    AbilityKeys abilityKeys = configEntity.Get<AbilityKeys>();
                    abilityConfigKeys.AddRange(abilityKeys.Keys);
                }
            }

            return abilityConfigKeys.ToArray();
        }

        private UniTask<ShurikenItemPresenter[]> CreateShurikenItemPresenters(CancellationToken cancellationToken, int itemToOpen)
        {
            List<UniTask<ShurikenItemPresenter>> createTasks = new();

            foreach (int i in _itemConfigs)
            {
                int itemId = _itemConfigs.Get1(i).Id;

                if (PlayerInventory.Contains(itemId) && _playerManager.Session.Progress.WeaponId == itemId)
                {
                    string configKey = _itemConfigs.Get2(i).Key;
                    UniTask<ShurikenItemPresenter> presenterPrepareTask = _itemPresenterFactory.CreateLoadoutItem(configKey, itemToOpen, null, cancellationToken);

                    createTasks.Add(presenterPrepareTask);
                }
            }

            return UniTask.WhenAll(createTasks);
        }
    }
}