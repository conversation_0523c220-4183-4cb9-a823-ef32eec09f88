using System.Collections.Generic;
using Client.Common.Analytics.Trackers;
using Client.Common.Analytics.Trackers.Abstract;
using Client.Common.Configs;
using Client.Common.Network.ExternalEvents.Analytics;
using Client.Common.Network.ExternalEvents.EventReceivers;
using Client.Common.Player.Controllers;
using Client.Common.Quests;
using Client.Common.Quests.Analytics;
using Client.Common.SceneLoading.Analytics;
using Client.Common.Services.Ads.Analytics;
using Client.Utils.CustomContainers;
using External;
using Leopotam.Ecs;

namespace Client.Common.Analytics.Systems
{
    public class GlobalAnalyticsSystem : IEcsInitSystem, IEcsRunSystem, IEcsDestroySystem
    {
        private readonly EcsWorld _world = default;
        private readonly PlayerManager _playerManager = default;
        private readonly QuestsData _questsData = default;
        private readonly ConfigService _configService = default;

        private readonly IExternalEventReceiver<ChangeMainBalance> _changeMainBalanceReceiver;
        private readonly IExternalEventReceiver<ChangeBloodInfo> _progressUpdateEvent = default; //TODO: fix and test
        
        private List<IAnalyticTracker> _runtimeTrackers;
        private readonly InitableContainer _initableContainer;
        private readonly DisposableContainer _disposableContainer;

        public GlobalAnalyticsSystem()
        {
            _initableContainer = new InitableContainer();
            _disposableContainer = new DisposableContainer();
        }

        public void Init()
        {
            QuestAnalyticsTracker questAnalyticsTracker = new(_world, _questsData);
            FtueQuestAnalyticsTracker ftueQuestAnalyticsTracker = new(_world);
            AdsRewardedAnalyticsTracker adsRewardedAnalyticsTracker = new(_world);
            PlayerIdAnalyticsTracker playerIdAnalyticsTracker = new(_world, _playerManager);
            DailyQuestRefreshAnalyticsTracker dailyQuestRefreshAnalyticsTracker = new(_world);
            ChangeBalanceAnalyticsTracker changeBalanceAnalyticsTracker = new(_world, _playerManager, _changeMainBalanceReceiver);
            BloodRewardAnalyticTracker bloodRewardAnalyticTracker = new(_world, _configService, _progressUpdateEvent);
            LoadingAnalyticsTracker loadingAnalyticsTracker = new(_world);
            
            _runtimeTrackers = new List<IAnalyticTracker>
            {
                questAnalyticsTracker,
                ftueQuestAnalyticsTracker,
                adsRewardedAnalyticsTracker,
                playerIdAnalyticsTracker,
                dailyQuestRefreshAnalyticsTracker,
                loadingAnalyticsTracker
            };
            
            RegisterTracker(changeBalanceAnalyticsTracker);
            RegisterTracker(questAnalyticsTracker);
            RegisterTracker(ftueQuestAnalyticsTracker);
            RegisterTracker(adsRewardedAnalyticsTracker);
            RegisterTracker(playerIdAnalyticsTracker);
            RegisterTracker(dailyQuestRefreshAnalyticsTracker);
            RegisterTracker(loadingAnalyticsTracker);
            RegisterTracker(bloodRewardAnalyticTracker);
            
            _initableContainer.Init();
        }

        public void Run()
        {
            foreach (IAnalyticTracker tracker in _runtimeTrackers)
            {
                tracker.Track();
            }
        }

        public void Destroy()
        {
            _disposableContainer?.Dispose();            
        }

        private void RegisterTracker(object tracker)
        {
            _initableContainer.Register(tracker);
            _disposableContainer.Register(tracker);
        }
    }
}