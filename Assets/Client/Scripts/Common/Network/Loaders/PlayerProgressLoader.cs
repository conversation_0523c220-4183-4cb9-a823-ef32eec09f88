using System.Threading;
using System.Threading.Tasks;
using Client.Common.Network.MetaNet;
using Client.Common.Player.Controllers;
using Client.Utils.Loaders;
using Client.Utils.ResultTool.Results;
using Cysharp.Threading.Tasks;
using External;

namespace Client.Common.Network.Loaders
{
    public class PlayerProgressLoader : BaseLoader
    {
        private readonly MetaNet.MetaNet _metaNet;
        private readonly PlayerSession _playerSession;

        public PlayerProgressLoader(MetaNet.MetaNet metaNet, PlayerSession playerSession)
        {
            _metaNet = metaNet;
            _playerSession = playerSession;
        }

        protected override async Task<LoadResult> LoadInternal(CancellationToken token)
        {
            UniTask<Result<ExternalGetProgressResponse>> progressTask = _metaNet.GetProgress(token);
            UniTask<Result<BattleProgressResponse>> battleProgressTask = _metaNet.GetBattleProgress(token);
            
            (Result<ExternalGetProgressResponse> progress, Result<BattleProgressResponse> battleProgress) progressResults = 
                await UniTask.WhenAll(progressTask, battleProgressTask);

            if (progressResults.progress.IsSuccess)
            {
                _playerSession.Progress.SetProgress(progressResults.progress.Value);
                _playerSession.Progress.SetBattleProgress(progressResults.battleProgress.Value);
            }

            return new LoadResult(progressResults.progress.IsSuccess, progressResults.progress.Error?.ToString());
        }
    }
}