using System.Threading;
using System.Threading.Tasks;
using Client.Common.Network.MetaNet;
using Client.Common.Player.Timers;
using Client.Utils.Loaders;
using Client.Utils.ResultTool.Results;

namespace Client.Common.Network.Loaders
{
    public class TimersLoader: BaseLoader
    {
        private readonly MetaNet.MetaNet _metaNet;
        private readonly PlayerTimers _playerTimers;

        public TimersLoader(MetaNet.MetaNet metaNet, PlayerTimers playerTimers)
        {
            _metaNet = metaNet;
            _playerTimers = playerTimers;
        }
        protected override async Task<LoadResult> LoadInternal(CancellationToken token)
        {
            Result<ClientTimerResponse> getTimers = await _metaNet.GetTimers(token);
            if (getTimers.IsFailure)
            {
                return new LoadResult(false, getTimers.Error.ToString());
            }
            _playerTimers.Update(getTimers.Value);
            return new LoadResult(true);
        }
    }
}