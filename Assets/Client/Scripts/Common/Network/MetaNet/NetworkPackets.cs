// Auto-generated file. Dont change manually, use gen-tool instead.
using System;
using System.Collections.Generic;
using Client.Utils.SimpleBinary;

// ReSharper disable InconsistentNaming

namespace Client.Common.Network.MetaNet {
    public enum SB_PacketType : ushort {
        ItemQuantity,
        Preference,
        Battle,
        BattleOpponent,
        BattleOpponentDamage,
        BattleOpponentInjuries,
        HitBodyPart,
        BattleLocation,
        AccountInfo,
        TournamentAward,
        UserTournamentAward,
        AchievementProgress,
        Achievement,
        AchievementAward,
        ChestOpenAward,
        KillLocalProgress,
        KillLocalInfo,
        InsideItemData,
        ClientAuthRequest,
        ClientAuthResponse,
        ClientNicknameRequest,
        ClientNicknameResponse,
        WeaponSkin,
        AccountPocket,
        ClientProgressRequest,
        ClientProgressResponse,
        ClientItemData,
        ClientBloodReward,
        ClientItemDataRequest,
        ClientItemDataResponse,
        ClientInventoryUnique,
        ClientInventoryRequest,
        ClientInventoryResponse,
        ClientTimerItem,
        ClientTimerRequest,
        ClientTimerResponse,
        ClientItemEquipRequest,
        ClientItemEquipResponse,
        ClientChestOpenItem,
        ClientChestOpenRequest,
        ClientChestOpenResponse,
        ClientBattleEnterRequest,
        ClientBattleEnterResponse,
        ClientRegionLeaveRequest,
        ClientRegionLeaveResponse,
        ClientRegionEnterRequest,
        ClientRegionEnterResponse,
        ClientRegionReviveRequest,
        ClientRegionReviveResponse,
        ClientTraumaAddRequest,
        ClientTraumaAddResponse,
        ClientHardPacksRequest,
        ClientHardPacksResponse,
        ClientCraftItemRequest,
        ClientCraftItemResponse,
        ClientForceCraftItemRequest,
        ClientForceCraftItemResponse,
        ClientItemBuyRequest,
        ClientItemBuyResponse,
        ClientBattleResultRequest,
        ClientBattleResultResponse,
        ClientPrefRequest,
        ClientPrefResponse,
        ClientTopRequest,
        ClientTopResponse,
        ClientTournamentAwardsRequest,
        ClientTournamentAwardsResponse,
        ClientUserTournamentAwardRequest,
        ClientUserTournamentAwardResponse,
        ClientTournamentInfoRequest,
        ClientTournamentInfoResponse,
        ClientKillLocalInfoRequest,
        ClientKillLocalInfoResponse,
        ClientKillLocalAwardRequest,
        ClientKillLocalAwardResponse,
        ClientCreatePurchaseRequest,
        ClientCreatePurchaseResponse,
        ClientPurchaseListRequest,
        ClientPurchaseListResponse,
        Purchase,
        ConfirmPurchaseRequest,
        ConfirmPurchaseResponse,
        FrontGuidesRequest,
        FrontGuidesResponse,
        QuestGoalProgress,
        QuestGoal,
        Quest,
        UserQuest,
        QuestsInfoRequest,
        QuestsInfoResponse,
        QuestsStateRequest,
        QuestsStateResponse,
        QuestsProgressRequest,
        QuestsProgressResponse,
        QuestRewardRequest,
        QuestsRewardResponse,
        ServerTimeRequest,
        ServerTimeResponse,
        FeedbackRequest,
        FeedbackResponse,
        HealRequest,
        HealResponse,
        LootRequest,
        LootResponse,
        BattleProgressRequest,
        BattleProgressResponse,
        AdminProgressRequest,
        StatusResponse,
        DrainBloodRequest,
        DrainBloodResponse,
        CollectBloodRequest,
        CollectBloodResponse,
        StageProgressRequest,
        AdminChangeRankRequest,
        SetBloodBottleMaxSizeRequest,
        ResetBloodBottleMaxSizeRequest,
        SetStartingRankRequest,
        SetStartingRankResponse,
        ClientCraftedItemsRequest,
        ClientCraftedItemsResponse,
        ClientTakeCraftedItemRequest,
        ClientTakeCraftedItemResponse,
        OpenMultiLootBoxRequest,
        OpenMultiLootBoxResponse,
        ChangeTimerRequest,
        ChangeTimerResponse,
        NotificationSettings,
        ChangeSettingsRequest,
        ChangeSettingsResponse,
        BloodChunkRequest,
        BloodChunkResponse,
        SkinDurability,
        AdminSkinDurabilitiesRequest,
        ClientStartFacebookRequest,
        ClientStartFacebookResponse,
    }

    public partial struct ItemQuantity {
        #if DEBUG
        [Obsolete("Use SB_PacketType.ItemQuantity instead.")]
        #endif
        public const ushort SB_PacketId = (ushort)SB_PacketType.ItemQuantity;
        public int Id;
        public uint Count;

        public static ItemQuantity New() {
            ItemQuantity v = default;
            return v;
        }

        public void Recycle() {
        }

        public static ItemQuantity Deserialize(ref SimpleBinarySerializer sbs, bool withPacketType = true) {
            if (withPacketType && sbs.ReadU16() != (ushort)SB_PacketType.ItemQuantity) { throw new Exception(); }
            var v = New();
            v.Id = sbs.ReadI32();
            v.Count = sbs.ReadU32();
            return v;
        }

        public void Serialize(ref SimpleBinarySerializer sbs, bool withPacketType = true) {
            if (withPacketType) { sbs.WriteU16((ushort)SB_PacketType.ItemQuantity); }
            sbs.WriteI32(Id);
            sbs.WriteU32(Count);
        }
    }

    public enum BooleanType : byte {
        False,
        True,
    }

    public struct Preference {
        #if DEBUG
        [Obsolete("Use SB_PacketType.Preference instead.")]
        #endif
        public const ushort SB_PacketId = (ushort)SB_PacketType.Preference;
        public ushort HeadTraumaTime;
        public ushort BodyTraumaTime;
        public ushort ArmTraumaTime;
        public ushort LegTraumaTime;
        public ushort HipsTraumaTime;
        public ushort RegionReviveCost;
        public ushort BankHardPackFirstSales;
        public ushort CraftSkipCost;
        public ushort CraftFreeCompletionTime;
        public ushort CraftCostResettingPerMinute;
        public ushort RankPointsToIncrease;
        public ushort RankPointsDowngrading;
        public ushort RankCount;
        public ushort TournamentRatingPointsForWin;
        public short TournamentRatingPointsForLose;
        public uint TournamentDuration;
        public ushort TournamentResettingTotalRatingPoints;
        public ushort DoubleGoldEffectTime;
        public ushort DoubleExpEffectTime;
        public ushort PreventLossEffectTime;
        public ushort AccountBloodBottleSize;
        public byte RankStarterNumber;

        public static Preference New() {
            Preference v = default;
            return v;
        }

        public void Recycle() {
        }

        public static Preference Deserialize(ref SimpleBinarySerializer sbs, bool withPacketType = true) {
            if (withPacketType && sbs.ReadU16() != (ushort)SB_PacketType.Preference) { throw new Exception(); }
            var v = New();
            v.HeadTraumaTime = sbs.ReadU16();
            v.BodyTraumaTime = sbs.ReadU16();
            v.ArmTraumaTime = sbs.ReadU16();
            v.LegTraumaTime = sbs.ReadU16();
            v.HipsTraumaTime = sbs.ReadU16();
            v.RegionReviveCost = sbs.ReadU16();
            v.BankHardPackFirstSales = sbs.ReadU16();
            v.CraftSkipCost = sbs.ReadU16();
            v.CraftFreeCompletionTime = sbs.ReadU16();
            v.CraftCostResettingPerMinute = sbs.ReadU16();
            v.RankPointsToIncrease = sbs.ReadU16();
            v.RankPointsDowngrading = sbs.ReadU16();
            v.RankCount = sbs.ReadU16();
            v.TournamentRatingPointsForWin = sbs.ReadU16();
            v.TournamentRatingPointsForLose = sbs.ReadI16();
            v.TournamentDuration = sbs.ReadU32();
            v.TournamentResettingTotalRatingPoints = sbs.ReadU16();
            v.DoubleGoldEffectTime = sbs.ReadU16();
            v.DoubleExpEffectTime = sbs.ReadU16();
            v.PreventLossEffectTime = sbs.ReadU16();
            v.AccountBloodBottleSize = sbs.ReadU16();
            v.RankStarterNumber = sbs.ReadU8();
            return v;
        }

        public void Serialize(ref SimpleBinarySerializer sbs, bool withPacketType = true) {
            if (withPacketType) { sbs.WriteU16((ushort)SB_PacketType.Preference); }
            sbs.WriteU16(HeadTraumaTime);
            sbs.WriteU16(BodyTraumaTime);
            sbs.WriteU16(ArmTraumaTime);
            sbs.WriteU16(LegTraumaTime);
            sbs.WriteU16(HipsTraumaTime);
            sbs.WriteU16(RegionReviveCost);
            sbs.WriteU16(BankHardPackFirstSales);
            sbs.WriteU16(CraftSkipCost);
            sbs.WriteU16(CraftFreeCompletionTime);
            sbs.WriteU16(CraftCostResettingPerMinute);
            sbs.WriteU16(RankPointsToIncrease);
            sbs.WriteU16(RankPointsDowngrading);
            sbs.WriteU16(RankCount);
            sbs.WriteU16(TournamentRatingPointsForWin);
            sbs.WriteI16(TournamentRatingPointsForLose);
            sbs.WriteU32(TournamentDuration);
            sbs.WriteU16(TournamentResettingTotalRatingPoints);
            sbs.WriteU16(DoubleGoldEffectTime);
            sbs.WriteU16(DoubleExpEffectTime);
            sbs.WriteU16(PreventLossEffectTime);
            sbs.WriteU16(AccountBloodBottleSize);
            sbs.WriteU8(RankStarterNumber);
        }
    }

    public struct Battle {
        #if DEBUG
        [Obsolete("Use SB_PacketType.Battle instead.")]
        #endif
        public const ushort SB_PacketId = (ushort)SB_PacketType.Battle;
        public string Id;
        public BattleState State;
        public BattleResult Result;
        public BattleLocation Location;
        public BattleOpponent Opponent;
        public List<SkinDurability> SkinDurabilities;
        public ushort Hp;
        static ListPool<SkinDurability> _poolOfSkinDurabilities = new ListPool<SkinDurability>();

        public static Battle New() {
            Battle v = default;
            v.Id = "";
            v.SkinDurabilities = _poolOfSkinDurabilities.Get();
            return v;
        }

        public void Recycle() {
            if (SkinDurabilities != null) {
                for (int i = 0, iMax = SkinDurabilities.Count; i < iMax; i++) {
                    SkinDurabilities[i].Recycle();
                }
                _poolOfSkinDurabilities.Recycle(SkinDurabilities);
                SkinDurabilities = null;
            }
        }

        public static Battle Deserialize(ref SimpleBinarySerializer sbs, bool withPacketType = true) {
            if (withPacketType && sbs.ReadU16() != (ushort)SB_PacketType.Battle) { throw new Exception(); }
            var v = New();
            v.Id = sbs.ReadS16();
            v.State = (BattleState)sbs.ReadU8();
            v.Result = (BattleResult)sbs.ReadU8();
            v.Location = BattleLocation.Deserialize(ref sbs, false);
            v.Opponent = BattleOpponent.Deserialize(ref sbs, false);
            for (int i = 0, iMax = sbs.ReadU16(); i < iMax; i++) {
                v.SkinDurabilities.Add(SkinDurability.Deserialize(ref sbs, false));
            }
            v.Hp = sbs.ReadU16();
            return v;
        }

        public void Serialize(ref SimpleBinarySerializer sbs, bool withPacketType = true) {
            if (withPacketType) { sbs.WriteU16((ushort)SB_PacketType.Battle); }
            sbs.WriteS16(Id);
            sbs.WriteU8((byte)State);
            sbs.WriteU8((byte)Result);
            Location.Serialize(ref sbs, false);
            Opponent.Serialize(ref sbs, false);
            var SkinDurabilitiesCount = SkinDurabilities.Count;
            sbs.WriteU16((ushort)SkinDurabilitiesCount);
            for (var i = 0; i < SkinDurabilitiesCount; i++) {
                SkinDurabilities[i].Serialize(ref sbs, false);
            }
            sbs.WriteU16(Hp);
        }
    }

    public enum BattleState : byte {
        Finished,
        Started,
    }

    public enum BattleResult : byte {
        Unknown,
        Win,
        Lose,
    }

    public enum BattleOpponentType : byte {
        Standard,
        Turtle,
        Berserk,
        Smurf,
        Killer,
    }

    public struct BattleOpponent {
        #if DEBUG
        [Obsolete("Use SB_PacketType.BattleOpponent instead.")]
        #endif
        public const ushort SB_PacketId = (ushort)SB_PacketType.BattleOpponent;
        public ushort Rank;
        public ushort Points;
        public BattleOpponentType Type;
        public ushort Outfit;
        public string Country;
        public ushort Shuriken;
        public BattleOpponentInjuries Injuries;
        public BattleOpponentDamage Damage;

        public static BattleOpponent New() {
            BattleOpponent v = default;
            v.Country = "";
            return v;
        }

        public void Recycle() {
        }

        public static BattleOpponent Deserialize(ref SimpleBinarySerializer sbs, bool withPacketType = true) {
            if (withPacketType && sbs.ReadU16() != (ushort)SB_PacketType.BattleOpponent) { throw new Exception(); }
            var v = New();
            v.Rank = sbs.ReadU16();
            v.Points = sbs.ReadU16();
            v.Type = (BattleOpponentType)sbs.ReadU8();
            v.Outfit = sbs.ReadU16();
            v.Country = sbs.ReadS16();
            v.Shuriken = sbs.ReadU16();
            v.Injuries = BattleOpponentInjuries.Deserialize(ref sbs, false);
            v.Damage = BattleOpponentDamage.Deserialize(ref sbs, false);
            return v;
        }

        public void Serialize(ref SimpleBinarySerializer sbs, bool withPacketType = true) {
            if (withPacketType) { sbs.WriteU16((ushort)SB_PacketType.BattleOpponent); }
            sbs.WriteU16(Rank);
            sbs.WriteU16(Points);
            sbs.WriteU8((byte)Type);
            sbs.WriteU16(Outfit);
            sbs.WriteS16(Country);
            sbs.WriteU16(Shuriken);
            Injuries.Serialize(ref sbs, false);
            Damage.Serialize(ref sbs, false);
        }
    }

    public struct BattleOpponentDamage {
        #if DEBUG
        [Obsolete("Use SB_PacketType.BattleOpponentDamage instead.")]
        #endif
        public const ushort SB_PacketId = (ushort)SB_PacketType.BattleOpponentDamage;
        public int Inflicted;
        public int Received;

        public static BattleOpponentDamage New() {
            BattleOpponentDamage v = default;
            return v;
        }

        public void Recycle() {
        }

        public static BattleOpponentDamage Deserialize(ref SimpleBinarySerializer sbs, bool withPacketType = true) {
            if (withPacketType && sbs.ReadU16() != (ushort)SB_PacketType.BattleOpponentDamage) { throw new Exception(); }
            var v = New();
            v.Inflicted = sbs.ReadI32();
            v.Received = sbs.ReadI32();
            return v;
        }

        public void Serialize(ref SimpleBinarySerializer sbs, bool withPacketType = true) {
            if (withPacketType) { sbs.WriteU16((ushort)SB_PacketType.BattleOpponentDamage); }
            sbs.WriteI32(Inflicted);
            sbs.WriteI32(Received);
        }
    }

    public struct BattleOpponentInjuries {
        #if DEBUG
        [Obsolete("Use SB_PacketType.BattleOpponentInjuries instead.")]
        #endif
        public const ushort SB_PacketId = (ushort)SB_PacketType.BattleOpponentInjuries;
        public HitBodyPart Inflicted;
        public HitBodyPart Received;

        public static BattleOpponentInjuries New() {
            BattleOpponentInjuries v = default;
            return v;
        }

        public void Recycle() {
        }

        public static BattleOpponentInjuries Deserialize(ref SimpleBinarySerializer sbs, bool withPacketType = true) {
            if (withPacketType && sbs.ReadU16() != (ushort)SB_PacketType.BattleOpponentInjuries) { throw new Exception(); }
            var v = New();
            v.Inflicted = HitBodyPart.Deserialize(ref sbs, false);
            v.Received = HitBodyPart.Deserialize(ref sbs, false);
            return v;
        }

        public void Serialize(ref SimpleBinarySerializer sbs, bool withPacketType = true) {
            if (withPacketType) { sbs.WriteU16((ushort)SB_PacketType.BattleOpponentInjuries); }
            Inflicted.Serialize(ref sbs, false);
            Received.Serialize(ref sbs, false);
        }
    }

    public struct HitBodyPart {
        #if DEBUG
        [Obsolete("Use SB_PacketType.HitBodyPart instead.")]
        #endif
        public const ushort SB_PacketId = (ushort)SB_PacketType.HitBodyPart;
        public byte Head;
        public byte Body;
        public byte Arm;
        public byte Leg;
        public byte Hips;

        public static HitBodyPart New() {
            HitBodyPart v = default;
            return v;
        }

        public void Recycle() {
        }

        public static HitBodyPart Deserialize(ref SimpleBinarySerializer sbs, bool withPacketType = true) {
            if (withPacketType && sbs.ReadU16() != (ushort)SB_PacketType.HitBodyPart) { throw new Exception(); }
            var v = New();
            v.Head = sbs.ReadU8();
            v.Body = sbs.ReadU8();
            v.Arm = sbs.ReadU8();
            v.Leg = sbs.ReadU8();
            v.Hips = sbs.ReadU8();
            return v;
        }

        public void Serialize(ref SimpleBinarySerializer sbs, bool withPacketType = true) {
            if (withPacketType) { sbs.WriteU16((ushort)SB_PacketType.HitBodyPart); }
            sbs.WriteU8(Head);
            sbs.WriteU8(Body);
            sbs.WriteU8(Arm);
            sbs.WriteU8(Leg);
            sbs.WriteU8(Hips);
        }
    }

    public struct BattleLocation {
        #if DEBUG
        [Obsolete("Use SB_PacketType.BattleLocation instead.")]
        #endif
        public const ushort SB_PacketId = (ushort)SB_PacketType.BattleLocation;
        public byte Region;
        public byte Point;

        public static BattleLocation New() {
            BattleLocation v = default;
            return v;
        }

        public void Recycle() {
        }

        public static BattleLocation Deserialize(ref SimpleBinarySerializer sbs, bool withPacketType = true) {
            if (withPacketType && sbs.ReadU16() != (ushort)SB_PacketType.BattleLocation) { throw new Exception(); }
            var v = New();
            v.Region = sbs.ReadU8();
            v.Point = sbs.ReadU8();
            return v;
        }

        public void Serialize(ref SimpleBinarySerializer sbs, bool withPacketType = true) {
            if (withPacketType) { sbs.WriteU16((ushort)SB_PacketType.BattleLocation); }
            sbs.WriteU8(Region);
            sbs.WriteU8(Point);
        }
    }

    public enum ForceCraftItemType : byte {
        Free,
        Hard,
        Promo,
    }

    public struct AccountInfo {
        #if DEBUG
        [Obsolete("Use SB_PacketType.AccountInfo instead.")]
        #endif
        public const ushort SB_PacketId = (ushort)SB_PacketType.AccountInfo;
        public string UserId;
        public ushort Position;
        public ushort Outfit;
        public ushort Rank;
        public string Nickname;
        public ushort Points;
        public string Country;

        public static AccountInfo New() {
            AccountInfo v = default;
            v.UserId = "";
            v.Nickname = "";
            v.Country = "";
            return v;
        }

        public void Recycle() {
        }

        public static AccountInfo Deserialize(ref SimpleBinarySerializer sbs, bool withPacketType = true) {
            if (withPacketType && sbs.ReadU16() != (ushort)SB_PacketType.AccountInfo) { throw new Exception(); }
            var v = New();
            v.UserId = sbs.ReadS16();
            v.Position = sbs.ReadU16();
            v.Outfit = sbs.ReadU16();
            v.Rank = sbs.ReadU16();
            v.Nickname = sbs.ReadS16();
            v.Points = sbs.ReadU16();
            v.Country = sbs.ReadS16();
            return v;
        }

        public void Serialize(ref SimpleBinarySerializer sbs, bool withPacketType = true) {
            if (withPacketType) { sbs.WriteU16((ushort)SB_PacketType.AccountInfo); }
            sbs.WriteS16(UserId);
            sbs.WriteU16(Position);
            sbs.WriteU16(Outfit);
            sbs.WriteU16(Rank);
            sbs.WriteS16(Nickname);
            sbs.WriteU16(Points);
            sbs.WriteS16(Country);
        }
    }

    public struct TournamentAward {
        #if DEBUG
        [Obsolete("Use SB_PacketType.TournamentAward instead.")]
        #endif
        public const ushort SB_PacketId = (ushort)SB_PacketType.TournamentAward;
        public byte FromPlace;
        public byte ToPlace;
        public List<ItemQuantity> Items;
        static ListPool<ItemQuantity> _poolOfItems = new ListPool<ItemQuantity>();

        public static TournamentAward New() {
            TournamentAward v = default;
            v.Items = _poolOfItems.Get();
            return v;
        }

        public void Recycle() {
            if (Items != null) {
                for (int i = 0, iMax = Items.Count; i < iMax; i++) {
                    Items[i].Recycle();
                }
                _poolOfItems.Recycle(Items);
                Items = null;
            }
        }

        public static TournamentAward Deserialize(ref SimpleBinarySerializer sbs, bool withPacketType = true) {
            if (withPacketType && sbs.ReadU16() != (ushort)SB_PacketType.TournamentAward) { throw new Exception(); }
            var v = New();
            v.FromPlace = sbs.ReadU8();
            v.ToPlace = sbs.ReadU8();
            for (int i = 0, iMax = sbs.ReadU16(); i < iMax; i++) {
                v.Items.Add(ItemQuantity.Deserialize(ref sbs, false));
            }
            return v;
        }

        public void Serialize(ref SimpleBinarySerializer sbs, bool withPacketType = true) {
            if (withPacketType) { sbs.WriteU16((ushort)SB_PacketType.TournamentAward); }
            sbs.WriteU8(FromPlace);
            sbs.WriteU8(ToPlace);
            var ItemsCount = Items.Count;
            sbs.WriteU16((ushort)ItemsCount);
            for (var i = 0; i < ItemsCount; i++) {
                Items[i].Serialize(ref sbs, false);
            }
        }
    }

    public struct UserTournamentAward {
        #if DEBUG
        [Obsolete("Use SB_PacketType.UserTournamentAward instead.")]
        #endif
        public const ushort SB_PacketId = (ushort)SB_PacketType.UserTournamentAward;
        public byte Place;
        public ushort Points;
        public List<ItemQuantity> Items;
        static ListPool<ItemQuantity> _poolOfItems = new ListPool<ItemQuantity>();

        public static UserTournamentAward New() {
            UserTournamentAward v = default;
            v.Items = _poolOfItems.Get();
            return v;
        }

        public void Recycle() {
            if (Items != null) {
                for (int i = 0, iMax = Items.Count; i < iMax; i++) {
                    Items[i].Recycle();
                }
                _poolOfItems.Recycle(Items);
                Items = null;
            }
        }

        public static UserTournamentAward Deserialize(ref SimpleBinarySerializer sbs, bool withPacketType = true) {
            if (withPacketType && sbs.ReadU16() != (ushort)SB_PacketType.UserTournamentAward) { throw new Exception(); }
            var v = New();
            v.Place = sbs.ReadU8();
            v.Points = sbs.ReadU16();
            for (int i = 0, iMax = sbs.ReadU16(); i < iMax; i++) {
                v.Items.Add(ItemQuantity.Deserialize(ref sbs, false));
            }
            return v;
        }

        public void Serialize(ref SimpleBinarySerializer sbs, bool withPacketType = true) {
            if (withPacketType) { sbs.WriteU16((ushort)SB_PacketType.UserTournamentAward); }
            sbs.WriteU8(Place);
            sbs.WriteU16(Points);
            var ItemsCount = Items.Count;
            sbs.WriteU16((ushort)ItemsCount);
            for (var i = 0; i < ItemsCount; i++) {
                Items[i].Serialize(ref sbs, false);
            }
        }
    }

    public enum UserTournamentAwardAction : byte {
        Get,
        Remove,
    }

    public enum AchievementState : byte {
        Unavailable,
        Available,
    }

    public struct AchievementProgress {
        #if DEBUG
        [Obsolete("Use SB_PacketType.AchievementProgress instead.")]
        #endif
        public const ushort SB_PacketId = (ushort)SB_PacketType.AchievementProgress;
        public List<ushort> Parts;
        public ushort Current;
        static ListPool<ushort> _poolOfParts = new ListPool<ushort>();

        public static AchievementProgress New() {
            AchievementProgress v = default;
            v.Parts = _poolOfParts.Get();
            return v;
        }

        public void Recycle() {
            _poolOfParts.Recycle(Parts);
        }

        public static AchievementProgress Deserialize(ref SimpleBinarySerializer sbs, bool withPacketType = true) {
            if (withPacketType && sbs.ReadU16() != (ushort)SB_PacketType.AchievementProgress) { throw new Exception(); }
            var v = New();
            for (int i = 0, iMax = sbs.ReadU16(); i < iMax; i++) {
                v.Parts.Add(sbs.ReadU16());
            }
            v.Current = sbs.ReadU16();
            return v;
        }

        public void Serialize(ref SimpleBinarySerializer sbs, bool withPacketType = true) {
            if (withPacketType) { sbs.WriteU16((ushort)SB_PacketType.AchievementProgress); }
            var PartsCount = Parts.Count;
            sbs.WriteU16((ushort)PartsCount);
            for (var i = 0; i < PartsCount; i++) {
                sbs.WriteU16(Parts[i]);
            }
            sbs.WriteU16(Current);
        }
    }

    public struct Achievement {
        #if DEBUG
        [Obsolete("Use SB_PacketType.Achievement instead.")]
        #endif
        public const ushort SB_PacketId = (ushort)SB_PacketType.Achievement;
        public ushort Id;
        public string Name;
        public string Title;
        public string Description;
        public List<AchievementAward> Awards;
        public AchievementState State;
        public AchievementProgress Progress;
        static ListPool<AchievementAward> _poolOfAwards = new ListPool<AchievementAward>();

        public static Achievement New() {
            Achievement v = default;
            v.Name = "";
            v.Title = "";
            v.Description = "";
            v.Awards = _poolOfAwards.Get();
            return v;
        }

        public void Recycle() {
            if (Awards != null) {
                for (int i = 0, iMax = Awards.Count; i < iMax; i++) {
                    Awards[i].Recycle();
                }
                _poolOfAwards.Recycle(Awards);
                Awards = null;
            }
        }

        public static Achievement Deserialize(ref SimpleBinarySerializer sbs, bool withPacketType = true) {
            if (withPacketType && sbs.ReadU16() != (ushort)SB_PacketType.Achievement) { throw new Exception(); }
            var v = New();
            v.Id = sbs.ReadU16();
            v.Name = sbs.ReadS16();
            v.Title = sbs.ReadS16();
            v.Description = sbs.ReadS16();
            for (int i = 0, iMax = sbs.ReadU16(); i < iMax; i++) {
                v.Awards.Add(AchievementAward.Deserialize(ref sbs, false));
            }
            v.State = (AchievementState)sbs.ReadU8();
            v.Progress = AchievementProgress.Deserialize(ref sbs, false);
            return v;
        }

        public void Serialize(ref SimpleBinarySerializer sbs, bool withPacketType = true) {
            if (withPacketType) { sbs.WriteU16((ushort)SB_PacketType.Achievement); }
            sbs.WriteU16(Id);
            sbs.WriteS16(Name);
            sbs.WriteS16(Title);
            sbs.WriteS16(Description);
            var AwardsCount = Awards.Count;
            sbs.WriteU16((ushort)AwardsCount);
            for (var i = 0; i < AwardsCount; i++) {
                Awards[i].Serialize(ref sbs, false);
            }
            sbs.WriteU8((byte)State);
            Progress.Serialize(ref sbs, false);
        }
    }

    public enum AchievementAction : byte {
        Add,
        Award,
    }

    public enum AchievementAwardState : byte {
        Received,
        Available,
    }

    public struct AchievementAward {
        #if DEBUG
        [Obsolete("Use SB_PacketType.AchievementAward instead.")]
        #endif
        public const ushort SB_PacketId = (ushort)SB_PacketType.AchievementAward;
        public List<ushort> Items;
        public AchievementAwardState State;
        static ListPool<ushort> _poolOfItems = new ListPool<ushort>();

        public static AchievementAward New() {
            AchievementAward v = default;
            v.Items = _poolOfItems.Get();
            return v;
        }

        public void Recycle() {
            _poolOfItems.Recycle(Items);
        }

        public static AchievementAward Deserialize(ref SimpleBinarySerializer sbs, bool withPacketType = true) {
            if (withPacketType && sbs.ReadU16() != (ushort)SB_PacketType.AchievementAward) { throw new Exception(); }
            var v = New();
            for (int i = 0, iMax = sbs.ReadU16(); i < iMax; i++) {
                v.Items.Add(sbs.ReadU16());
            }
            v.State = (AchievementAwardState)sbs.ReadU8();
            return v;
        }

        public void Serialize(ref SimpleBinarySerializer sbs, bool withPacketType = true) {
            if (withPacketType) { sbs.WriteU16((ushort)SB_PacketType.AchievementAward); }
            var ItemsCount = Items.Count;
            sbs.WriteU16((ushort)ItemsCount);
            for (var i = 0; i < ItemsCount; i++) {
                sbs.WriteU16(Items[i]);
            }
            sbs.WriteU8((byte)State);
        }
    }

    public enum ChestOpenState : byte {
        Failure,
        Success,
    }

    public struct ChestOpenAward {
        #if DEBUG
        [Obsolete("Use SB_PacketType.ChestOpenAward instead.")]
        #endif
        public const ushort SB_PacketId = (ushort)SB_PacketType.ChestOpenAward;
        public ushort ItemId;
        public ushort Count;

        public static ChestOpenAward New() {
            ChestOpenAward v = default;
            return v;
        }

        public void Recycle() {
        }

        public static ChestOpenAward Deserialize(ref SimpleBinarySerializer sbs, bool withPacketType = true) {
            if (withPacketType && sbs.ReadU16() != (ushort)SB_PacketType.ChestOpenAward) { throw new Exception(); }
            var v = New();
            v.ItemId = sbs.ReadU16();
            v.Count = sbs.ReadU16();
            return v;
        }

        public void Serialize(ref SimpleBinarySerializer sbs, bool withPacketType = true) {
            if (withPacketType) { sbs.WriteU16((ushort)SB_PacketType.ChestOpenAward); }
            sbs.WriteU16(ItemId);
            sbs.WriteU16(Count);
        }
    }

    public struct KillLocalProgress {
        #if DEBUG
        [Obsolete("Use SB_PacketType.KillLocalProgress instead.")]
        #endif
        public const ushort SB_PacketId = (ushort)SB_PacketType.KillLocalProgress;
        public ushort Total;
        public ushort Current;

        public static KillLocalProgress New() {
            KillLocalProgress v = default;
            return v;
        }

        public void Recycle() {
        }

        public static KillLocalProgress Deserialize(ref SimpleBinarySerializer sbs, bool withPacketType = true) {
            if (withPacketType && sbs.ReadU16() != (ushort)SB_PacketType.KillLocalProgress) { throw new Exception(); }
            var v = New();
            v.Total = sbs.ReadU16();
            v.Current = sbs.ReadU16();
            return v;
        }

        public void Serialize(ref SimpleBinarySerializer sbs, bool withPacketType = true) {
            if (withPacketType) { sbs.WriteU16((ushort)SB_PacketType.KillLocalProgress); }
            sbs.WriteU16(Total);
            sbs.WriteU16(Current);
        }
    }

    public struct KillLocalInfo {
        #if DEBUG
        [Obsolete("Use SB_PacketType.KillLocalInfo instead.")]
        #endif
        public const ushort SB_PacketId = (ushort)SB_PacketType.KillLocalInfo;
        public byte RegionId;
        public byte LocationId;
        public KillLocalProgress Progress;
        public ushort Reward;

        public static KillLocalInfo New() {
            KillLocalInfo v = default;
            return v;
        }

        public void Recycle() {
        }

        public static KillLocalInfo Deserialize(ref SimpleBinarySerializer sbs, bool withPacketType = true) {
            if (withPacketType && sbs.ReadU16() != (ushort)SB_PacketType.KillLocalInfo) { throw new Exception(); }
            var v = New();
            v.RegionId = sbs.ReadU8();
            v.LocationId = sbs.ReadU8();
            v.Progress = KillLocalProgress.Deserialize(ref sbs, false);
            v.Reward = sbs.ReadU16();
            return v;
        }

        public void Serialize(ref SimpleBinarySerializer sbs, bool withPacketType = true) {
            if (withPacketType) { sbs.WriteU16((ushort)SB_PacketType.KillLocalInfo); }
            sbs.WriteU8(RegionId);
            sbs.WriteU8(LocationId);
            Progress.Serialize(ref sbs, false);
            sbs.WriteU16(Reward);
        }
    }

    public struct InsideItemData {
        #if DEBUG
        [Obsolete("Use SB_PacketType.InsideItemData instead.")]
        #endif
        public const ushort SB_PacketId = (ushort)SB_PacketType.InsideItemData;
        public int Id;
        public int CraftedId;
        public ushort Min;
        public ushort Max;
        public ushort Chance;

        public static InsideItemData New() {
            InsideItemData v = default;
            return v;
        }

        public void Recycle() {
        }

        public static InsideItemData Deserialize(ref SimpleBinarySerializer sbs, bool withPacketType = true) {
            if (withPacketType && sbs.ReadU16() != (ushort)SB_PacketType.InsideItemData) { throw new Exception(); }
            var v = New();
            v.Id = sbs.ReadI32();
            v.CraftedId = sbs.ReadI32();
            v.Min = sbs.ReadU16();
            v.Max = sbs.ReadU16();
            v.Chance = sbs.ReadU16();
            return v;
        }

        public void Serialize(ref SimpleBinarySerializer sbs, bool withPacketType = true) {
            if (withPacketType) { sbs.WriteU16((ushort)SB_PacketType.InsideItemData); }
            sbs.WriteI32(Id);
            sbs.WriteI32(CraftedId);
            sbs.WriteU16(Min);
            sbs.WriteU16(Max);
            sbs.WriteU16(Chance);
        }
    }

    public enum AuthType : byte {
        Init,
        Reconnect,
    }

    public struct ClientAuthRequest {
        #if DEBUG
        [Obsolete("Use SB_PacketType.ClientAuthRequest instead.")]
        #endif
        public const ushort SB_PacketId = (ushort)SB_PacketType.ClientAuthRequest;
        public string Name;
        public string Pass;
        public string Ip;
        public string FcmToken;
        public AuthType Type;

        public static ClientAuthRequest New() {
            ClientAuthRequest v = default;
            v.Name = "";
            v.Pass = "";
            v.Ip = "";
            v.FcmToken = "";
            return v;
        }

        public void Recycle() {
        }

        public static ClientAuthRequest Deserialize(ref SimpleBinarySerializer sbs, bool withPacketType = true) {
            if (withPacketType && sbs.ReadU16() != (ushort)SB_PacketType.ClientAuthRequest) { throw new Exception(); }
            var v = New();
            v.Name = sbs.ReadS16();
            v.Pass = sbs.ReadS16();
            v.Ip = sbs.ReadS16();
            v.FcmToken = sbs.ReadS16();
            v.Type = (AuthType)sbs.ReadU8();
            return v;
        }

        public void Serialize(ref SimpleBinarySerializer sbs, bool withPacketType = true) {
            if (withPacketType) { sbs.WriteU16((ushort)SB_PacketType.ClientAuthRequest); }
            sbs.WriteS16(Name);
            sbs.WriteS16(Pass);
            sbs.WriteS16(Ip);
            sbs.WriteS16(FcmToken);
            sbs.WriteU8((byte)Type);
        }
    }

    public struct ClientAuthResponse {
        #if DEBUG
        [Obsolete("Use SB_PacketType.ClientAuthResponse instead.")]
        #endif
        public const ushort SB_PacketId = (ushort)SB_PacketType.ClientAuthResponse;
        public string Nickname;
        public AccountInfo Info;

        public static ClientAuthResponse New() {
            ClientAuthResponse v = default;
            v.Nickname = "";
            return v;
        }

        public void Recycle() {
        }

        public static ClientAuthResponse Deserialize(ref SimpleBinarySerializer sbs, bool withPacketType = true) {
            if (withPacketType && sbs.ReadU16() != (ushort)SB_PacketType.ClientAuthResponse) { throw new Exception(); }
            var v = New();
            v.Nickname = sbs.ReadS16();
            v.Info = AccountInfo.Deserialize(ref sbs, false);
            return v;
        }

        public void Serialize(ref SimpleBinarySerializer sbs, bool withPacketType = true) {
            if (withPacketType) { sbs.WriteU16((ushort)SB_PacketType.ClientAuthResponse); }
            sbs.WriteS16(Nickname);
            Info.Serialize(ref sbs, false);
        }
    }

    public struct ClientNicknameRequest {
        #if DEBUG
        [Obsolete("Use SB_PacketType.ClientNicknameRequest instead.")]
        #endif
        public const ushort SB_PacketId = (ushort)SB_PacketType.ClientNicknameRequest;
        public string Nickname;

        public static ClientNicknameRequest New() {
            ClientNicknameRequest v = default;
            v.Nickname = "";
            return v;
        }

        public void Recycle() {
        }

        public static ClientNicknameRequest Deserialize(ref SimpleBinarySerializer sbs, bool withPacketType = true) {
            if (withPacketType && sbs.ReadU16() != (ushort)SB_PacketType.ClientNicknameRequest) { throw new Exception(); }
            var v = New();
            v.Nickname = sbs.ReadS16();
            return v;
        }

        public void Serialize(ref SimpleBinarySerializer sbs, bool withPacketType = true) {
            if (withPacketType) { sbs.WriteU16((ushort)SB_PacketType.ClientNicknameRequest); }
            sbs.WriteS16(Nickname);
        }
    }

    public struct ClientNicknameResponse {
        #if DEBUG
        [Obsolete("Use SB_PacketType.ClientNicknameResponse instead.")]
        #endif
        public const ushort SB_PacketId = (ushort)SB_PacketType.ClientNicknameResponse;
        public string Nickname;

        public static ClientNicknameResponse New() {
            ClientNicknameResponse v = default;
            v.Nickname = "";
            return v;
        }

        public void Recycle() {
        }

        public static ClientNicknameResponse Deserialize(ref SimpleBinarySerializer sbs, bool withPacketType = true) {
            if (withPacketType && sbs.ReadU16() != (ushort)SB_PacketType.ClientNicknameResponse) { throw new Exception(); }
            var v = New();
            v.Nickname = sbs.ReadS16();
            return v;
        }

        public void Serialize(ref SimpleBinarySerializer sbs, bool withPacketType = true) {
            if (withPacketType) { sbs.WriteU16((ushort)SB_PacketType.ClientNicknameResponse); }
            sbs.WriteS16(Nickname);
        }
    }

    public struct WeaponSkin {
        #if DEBUG
        [Obsolete("Use SB_PacketType.WeaponSkin instead.")]
        #endif
        public const ushort SB_PacketId = (ushort)SB_PacketType.WeaponSkin;
        public ushort Id;
        public ushort WeaponId;
        public uint Durability;

        public static WeaponSkin New() {
            WeaponSkin v = default;
            return v;
        }

        public void Recycle() {
        }

        public static WeaponSkin Deserialize(ref SimpleBinarySerializer sbs, bool withPacketType = true) {
            if (withPacketType && sbs.ReadU16() != (ushort)SB_PacketType.WeaponSkin) { throw new Exception(); }
            var v = New();
            v.Id = sbs.ReadU16();
            v.WeaponId = sbs.ReadU16();
            v.Durability = sbs.ReadU32();
            return v;
        }

        public void Serialize(ref SimpleBinarySerializer sbs, bool withPacketType = true) {
            if (withPacketType) { sbs.WriteU16((ushort)SB_PacketType.WeaponSkin); }
            sbs.WriteU16(Id);
            sbs.WriteU16(WeaponId);
            sbs.WriteU32(Durability);
        }
    }

    public enum AccountPocketType : byte {
        PowerStone,
        Hook,
        Bomb,
    }

    public struct AccountPocket {
        #if DEBUG
        [Obsolete("Use SB_PacketType.AccountPocket instead.")]
        #endif
        public const ushort SB_PacketId = (ushort)SB_PacketType.AccountPocket;
        public AccountPocketType Type;
        public ItemQuantity Item;

        public static AccountPocket New() {
            AccountPocket v = default;
            return v;
        }

        public void Recycle() {
        }

        public static AccountPocket Deserialize(ref SimpleBinarySerializer sbs, bool withPacketType = true) {
            if (withPacketType && sbs.ReadU16() != (ushort)SB_PacketType.AccountPocket) { throw new Exception(); }
            var v = New();
            v.Type = (AccountPocketType)sbs.ReadU8();
            v.Item = ItemQuantity.Deserialize(ref sbs, false);
            return v;
        }

        public void Serialize(ref SimpleBinarySerializer sbs, bool withPacketType = true) {
            if (withPacketType) { sbs.WriteU16((ushort)SB_PacketType.AccountPocket); }
            sbs.WriteU8((byte)Type);
            Item.Serialize(ref sbs, false);
        }
    }

    public struct ClientProgressRequest {
        #if DEBUG
        [Obsolete("Use SB_PacketType.ClientProgressRequest instead.")]
        #endif
        public const ushort SB_PacketId = (ushort)SB_PacketType.ClientProgressRequest;
        public static ClientProgressRequest New() {
            ClientProgressRequest v = default;
            return v;
        }

        public void Recycle() {
        }

        public static ClientProgressRequest Deserialize(ref SimpleBinarySerializer sbs, bool withPacketType = true) {
            if (withPacketType && sbs.ReadU16() != (ushort)SB_PacketType.ClientProgressRequest) { throw new Exception(); }
            var v = New();
            return v;
        }

        public void Serialize(ref SimpleBinarySerializer sbs, bool withPacketType = true) {
            if (withPacketType) { sbs.WriteU16((ushort)SB_PacketType.ClientProgressRequest); }
        }
    }

    public struct ClientProgressResponse {
        #if DEBUG
        [Obsolete("Use SB_PacketType.ClientProgressResponse instead.")]
        #endif
        public const ushort SB_PacketId = (ushort)SB_PacketType.ClientProgressResponse;
        public byte Rank;
        public ushort Kills;
        public ushort WeaponId;
        public ushort OutfitId;
        public ushort AmuletId;
        public List<WeaponSkin> WeaponSkins;
        public ushort BloodStep;
        public List<SkinDurability> SkinDurabilities;
        public ushort BloodBottleMaxSize;
        public ushort RankPoints;
        public List<string> Stages;
        public List<byte> Steps;
        public List<AccountPocket> Pockets;
        static ListPool<WeaponSkin> _poolOfWeaponSkins = new ListPool<WeaponSkin>();
        static ListPool<SkinDurability> _poolOfSkinDurabilities = new ListPool<SkinDurability>();
        static ListPool<string> _poolOfStages = new ListPool<string>();
        static ListPool<byte> _poolOfSteps = new ListPool<byte>();
        static ListPool<AccountPocket> _poolOfPockets = new ListPool<AccountPocket>();

        public static ClientProgressResponse New() {
            ClientProgressResponse v = default;
            v.WeaponSkins = _poolOfWeaponSkins.Get();
            v.SkinDurabilities = _poolOfSkinDurabilities.Get();
            v.Stages = _poolOfStages.Get();
            v.Steps = _poolOfSteps.Get();
            v.Pockets = _poolOfPockets.Get();
            return v;
        }

        public void Recycle() {
            if (WeaponSkins != null) {
                for (int i = 0, iMax = WeaponSkins.Count; i < iMax; i++) {
                    WeaponSkins[i].Recycle();
                }
                _poolOfWeaponSkins.Recycle(WeaponSkins);
                WeaponSkins = null;
            }
            if (SkinDurabilities != null) {
                for (int i = 0, iMax = SkinDurabilities.Count; i < iMax; i++) {
                    SkinDurabilities[i].Recycle();
                }
                _poolOfSkinDurabilities.Recycle(SkinDurabilities);
                SkinDurabilities = null;
            }
            _poolOfStages.Recycle(Stages);
            _poolOfSteps.Recycle(Steps);
            if (Pockets != null) {
                for (int i = 0, iMax = Pockets.Count; i < iMax; i++) {
                    Pockets[i].Recycle();
                }
                _poolOfPockets.Recycle(Pockets);
                Pockets = null;
            }
        }

        public static ClientProgressResponse Deserialize(ref SimpleBinarySerializer sbs, bool withPacketType = true) {
            if (withPacketType && sbs.ReadU16() != (ushort)SB_PacketType.ClientProgressResponse) { throw new Exception(); }
            var v = New();
            v.Rank = sbs.ReadU8();
            v.Kills = sbs.ReadU16();
            v.WeaponId = sbs.ReadU16();
            v.OutfitId = sbs.ReadU16();
            v.AmuletId = sbs.ReadU16();
            for (int i = 0, iMax = sbs.ReadU16(); i < iMax; i++) {
                v.WeaponSkins.Add(WeaponSkin.Deserialize(ref sbs, false));
            }
            v.BloodStep = sbs.ReadU16();
            for (int i = 0, iMax = sbs.ReadU16(); i < iMax; i++) {
                v.SkinDurabilities.Add(SkinDurability.Deserialize(ref sbs, false));
            }
            v.BloodBottleMaxSize = sbs.ReadU16();
            v.RankPoints = sbs.ReadU16();
            for (int i = 0, iMax = sbs.ReadU16(); i < iMax; i++) {
                v.Stages.Add(sbs.ReadS16());
            }
            for (int i = 0, iMax = sbs.ReadU16(); i < iMax; i++) {
                v.Steps.Add(sbs.ReadU8());
            }
            for (int i = 0, iMax = sbs.ReadU16(); i < iMax; i++) {
                v.Pockets.Add(AccountPocket.Deserialize(ref sbs, false));
            }
            return v;
        }

        public void Serialize(ref SimpleBinarySerializer sbs, bool withPacketType = true) {
            if (withPacketType) { sbs.WriteU16((ushort)SB_PacketType.ClientProgressResponse); }
            sbs.WriteU8(Rank);
            sbs.WriteU16(Kills);
            sbs.WriteU16(WeaponId);
            sbs.WriteU16(OutfitId);
            sbs.WriteU16(AmuletId);
            var WeaponSkinsCount = WeaponSkins.Count;
            sbs.WriteU16((ushort)WeaponSkinsCount);
            for (var i = 0; i < WeaponSkinsCount; i++) {
                WeaponSkins[i].Serialize(ref sbs, false);
            }
            sbs.WriteU16(BloodStep);
            var SkinDurabilitiesCount = SkinDurabilities.Count;
            sbs.WriteU16((ushort)SkinDurabilitiesCount);
            for (var i = 0; i < SkinDurabilitiesCount; i++) {
                SkinDurabilities[i].Serialize(ref sbs, false);
            }
            sbs.WriteU16(BloodBottleMaxSize);
            sbs.WriteU16(RankPoints);
            var StagesCount = Stages.Count;
            sbs.WriteU16((ushort)StagesCount);
            for (var i = 0; i < StagesCount; i++) {
                sbs.WriteS16(Stages[i]);
            }
            var StepsCount = Steps.Count;
            sbs.WriteU16((ushort)StepsCount);
            for (var i = 0; i < StepsCount; i++) {
                sbs.WriteU8(Steps[i]);
            }
            var PocketsCount = Pockets.Count;
            sbs.WriteU16((ushort)PocketsCount);
            for (var i = 0; i < PocketsCount; i++) {
                Pockets[i].Serialize(ref sbs, false);
            }
        }
    }

    public struct ClientItemData {
        #if DEBUG
        [Obsolete("Use SB_PacketType.ClientItemData instead.")]
        #endif
        public const ushort SB_PacketId = (ushort)SB_PacketType.ClientItemData;
        public int Id;
        public byte Type;
        public byte BuyCurrency;
        public ushort BuyCost;
        public byte CraftCurrency;
        public ushort CraftCost;
        public uint CraftTime;
        public ushort CraftCostToComplete;
        public ushort CraftTimeDecreasePerAdViewing;
        public uint MaxDurability;
        public uint RepairCurrency;
        public uint RepairCost;
        public List<ItemQuantity> Components;
        public List<InsideItemData> Content;
        static ListPool<ItemQuantity> _poolOfComponents = new ListPool<ItemQuantity>();
        static ListPool<InsideItemData> _poolOfContent = new ListPool<InsideItemData>();

        public static ClientItemData New() {
            ClientItemData v = default;
            v.Components = _poolOfComponents.Get();
            v.Content = _poolOfContent.Get();
            return v;
        }

        public void Recycle() {
            if (Components != null) {
                for (int i = 0, iMax = Components.Count; i < iMax; i++) {
                    Components[i].Recycle();
                }
                _poolOfComponents.Recycle(Components);
                Components = null;
            }
            if (Content != null) {
                for (int i = 0, iMax = Content.Count; i < iMax; i++) {
                    Content[i].Recycle();
                }
                _poolOfContent.Recycle(Content);
                Content = null;
            }
        }

        public static ClientItemData Deserialize(ref SimpleBinarySerializer sbs, bool withPacketType = true) {
            if (withPacketType && sbs.ReadU16() != (ushort)SB_PacketType.ClientItemData) { throw new Exception(); }
            var v = New();
            v.Id = sbs.ReadI32();
            v.Type = sbs.ReadU8();
            v.BuyCurrency = sbs.ReadU8();
            v.BuyCost = sbs.ReadU16();
            v.CraftCurrency = sbs.ReadU8();
            v.CraftCost = sbs.ReadU16();
            v.CraftTime = sbs.ReadU32();
            v.CraftCostToComplete = sbs.ReadU16();
            v.CraftTimeDecreasePerAdViewing = sbs.ReadU16();
            v.MaxDurability = sbs.ReadU32();
            v.RepairCurrency = sbs.ReadU32();
            v.RepairCost = sbs.ReadU32();
            for (int i = 0, iMax = sbs.ReadU16(); i < iMax; i++) {
                v.Components.Add(ItemQuantity.Deserialize(ref sbs, false));
            }
            for (int i = 0, iMax = sbs.ReadU16(); i < iMax; i++) {
                v.Content.Add(InsideItemData.Deserialize(ref sbs, false));
            }
            return v;
        }

        public void Serialize(ref SimpleBinarySerializer sbs, bool withPacketType = true) {
            if (withPacketType) { sbs.WriteU16((ushort)SB_PacketType.ClientItemData); }
            sbs.WriteI32(Id);
            sbs.WriteU8(Type);
            sbs.WriteU8(BuyCurrency);
            sbs.WriteU16(BuyCost);
            sbs.WriteU8(CraftCurrency);
            sbs.WriteU16(CraftCost);
            sbs.WriteU32(CraftTime);
            sbs.WriteU16(CraftCostToComplete);
            sbs.WriteU16(CraftTimeDecreasePerAdViewing);
            sbs.WriteU32(MaxDurability);
            sbs.WriteU32(RepairCurrency);
            sbs.WriteU32(RepairCost);
            var ComponentsCount = Components.Count;
            sbs.WriteU16((ushort)ComponentsCount);
            for (var i = 0; i < ComponentsCount; i++) {
                Components[i].Serialize(ref sbs, false);
            }
            var ContentCount = Content.Count;
            sbs.WriteU16((ushort)ContentCount);
            for (var i = 0; i < ContentCount; i++) {
                Content[i].Serialize(ref sbs, false);
            }
        }
    }

    public struct ClientBloodReward {
        #if DEBUG
        [Obsolete("Use SB_PacketType.ClientBloodReward instead.")]
        #endif
        public const ushort SB_PacketId = (ushort)SB_PacketType.ClientBloodReward;
        public ushort Id;
        public ushort Item;
        public string Description;
        public ushort Price;
        public ushort RegionId;

        public static ClientBloodReward New() {
            ClientBloodReward v = default;
            v.Description = "";
            return v;
        }

        public void Recycle() {
        }

        public static ClientBloodReward Deserialize(ref SimpleBinarySerializer sbs, bool withPacketType = true) {
            if (withPacketType && sbs.ReadU16() != (ushort)SB_PacketType.ClientBloodReward) { throw new Exception(); }
            var v = New();
            v.Id = sbs.ReadU16();
            v.Item = sbs.ReadU16();
            v.Description = sbs.ReadS16();
            v.Price = sbs.ReadU16();
            v.RegionId = sbs.ReadU16();
            return v;
        }

        public void Serialize(ref SimpleBinarySerializer sbs, bool withPacketType = true) {
            if (withPacketType) { sbs.WriteU16((ushort)SB_PacketType.ClientBloodReward); }
            sbs.WriteU16(Id);
            sbs.WriteU16(Item);
            sbs.WriteS16(Description);
            sbs.WriteU16(Price);
            sbs.WriteU16(RegionId);
        }
    }

    public struct ClientItemDataRequest {
        #if DEBUG
        [Obsolete("Use SB_PacketType.ClientItemDataRequest instead.")]
        #endif
        public const ushort SB_PacketId = (ushort)SB_PacketType.ClientItemDataRequest;
        public static ClientItemDataRequest New() {
            ClientItemDataRequest v = default;
            return v;
        }

        public void Recycle() {
        }

        public static ClientItemDataRequest Deserialize(ref SimpleBinarySerializer sbs, bool withPacketType = true) {
            if (withPacketType && sbs.ReadU16() != (ushort)SB_PacketType.ClientItemDataRequest) { throw new Exception(); }
            var v = New();
            return v;
        }

        public void Serialize(ref SimpleBinarySerializer sbs, bool withPacketType = true) {
            if (withPacketType) { sbs.WriteU16((ushort)SB_PacketType.ClientItemDataRequest); }
        }
    }

    public struct ClientItemDataResponse {
        #if DEBUG
        [Obsolete("Use SB_PacketType.ClientItemDataResponse instead.")]
        #endif
        public const ushort SB_PacketId = (ushort)SB_PacketType.ClientItemDataResponse;
        public List<ClientItemData> Items;
        public List<ClientBloodReward> BloodRewards;
        static ListPool<ClientItemData> _poolOfItems = new ListPool<ClientItemData>();
        static ListPool<ClientBloodReward> _poolOfBloodRewards = new ListPool<ClientBloodReward>();

        public static ClientItemDataResponse New() {
            ClientItemDataResponse v = default;
            v.Items = _poolOfItems.Get();
            v.BloodRewards = _poolOfBloodRewards.Get();
            return v;
        }

        public void Recycle() {
            if (Items != null) {
                for (int i = 0, iMax = Items.Count; i < iMax; i++) {
                    Items[i].Recycle();
                }
                _poolOfItems.Recycle(Items);
                Items = null;
            }
            if (BloodRewards != null) {
                for (int i = 0, iMax = BloodRewards.Count; i < iMax; i++) {
                    BloodRewards[i].Recycle();
                }
                _poolOfBloodRewards.Recycle(BloodRewards);
                BloodRewards = null;
            }
        }

        public static ClientItemDataResponse Deserialize(ref SimpleBinarySerializer sbs, bool withPacketType = true) {
            if (withPacketType && sbs.ReadU16() != (ushort)SB_PacketType.ClientItemDataResponse) { throw new Exception(); }
            var v = New();
            for (int i = 0, iMax = sbs.ReadU16(); i < iMax; i++) {
                v.Items.Add(ClientItemData.Deserialize(ref sbs, false));
            }
            for (int i = 0, iMax = sbs.ReadU16(); i < iMax; i++) {
                v.BloodRewards.Add(ClientBloodReward.Deserialize(ref sbs, false));
            }
            return v;
        }

        public void Serialize(ref SimpleBinarySerializer sbs, bool withPacketType = true) {
            if (withPacketType) { sbs.WriteU16((ushort)SB_PacketType.ClientItemDataResponse); }
            var ItemsCount = Items.Count;
            sbs.WriteU16((ushort)ItemsCount);
            for (var i = 0; i < ItemsCount; i++) {
                Items[i].Serialize(ref sbs, false);
            }
            var BloodRewardsCount = BloodRewards.Count;
            sbs.WriteU16((ushort)BloodRewardsCount);
            for (var i = 0; i < BloodRewardsCount; i++) {
                BloodRewards[i].Serialize(ref sbs, false);
            }
        }
    }

    public struct ClientInventoryUnique {
        #if DEBUG
        [Obsolete("Use SB_PacketType.ClientInventoryUnique instead.")]
        #endif
        public const ushort SB_PacketId = (ushort)SB_PacketType.ClientInventoryUnique;
        public ushort Id;
        public float Value1;
        public float Value2;

        public static ClientInventoryUnique New() {
            ClientInventoryUnique v = default;
            return v;
        }

        public void Recycle() {
        }

        public static ClientInventoryUnique Deserialize(ref SimpleBinarySerializer sbs, bool withPacketType = true) {
            if (withPacketType && sbs.ReadU16() != (ushort)SB_PacketType.ClientInventoryUnique) { throw new Exception(); }
            var v = New();
            v.Id = sbs.ReadU16();
            v.Value1 = sbs.ReadF32();
            v.Value2 = sbs.ReadF32();
            return v;
        }

        public void Serialize(ref SimpleBinarySerializer sbs, bool withPacketType = true) {
            if (withPacketType) { sbs.WriteU16((ushort)SB_PacketType.ClientInventoryUnique); }
            sbs.WriteU16(Id);
            sbs.WriteF32(Value1);
            sbs.WriteF32(Value2);
        }
    }

    public struct ClientInventoryRequest {
        #if DEBUG
        [Obsolete("Use SB_PacketType.ClientInventoryRequest instead.")]
        #endif
        public const ushort SB_PacketId = (ushort)SB_PacketType.ClientInventoryRequest;
        public static ClientInventoryRequest New() {
            ClientInventoryRequest v = default;
            return v;
        }

        public void Recycle() {
        }

        public static ClientInventoryRequest Deserialize(ref SimpleBinarySerializer sbs, bool withPacketType = true) {
            if (withPacketType && sbs.ReadU16() != (ushort)SB_PacketType.ClientInventoryRequest) { throw new Exception(); }
            var v = New();
            return v;
        }

        public void Serialize(ref SimpleBinarySerializer sbs, bool withPacketType = true) {
            if (withPacketType) { sbs.WriteU16((ushort)SB_PacketType.ClientInventoryRequest); }
        }
    }

    public struct ClientInventoryResponse {
        #if DEBUG
        [Obsolete("Use SB_PacketType.ClientInventoryResponse instead.")]
        #endif
        public const ushort SB_PacketId = (ushort)SB_PacketType.ClientInventoryResponse;
        public List<ItemQuantity> Items;
        public List<ClientInventoryUnique> Uniques;
        static ListPool<ItemQuantity> _poolOfItems = new ListPool<ItemQuantity>();
        static ListPool<ClientInventoryUnique> _poolOfUniques = new ListPool<ClientInventoryUnique>();

        public static ClientInventoryResponse New() {
            ClientInventoryResponse v = default;
            v.Items = _poolOfItems.Get();
            v.Uniques = _poolOfUniques.Get();
            return v;
        }

        public void Recycle() {
            if (Items != null) {
                for (int i = 0, iMax = Items.Count; i < iMax; i++) {
                    Items[i].Recycle();
                }
                _poolOfItems.Recycle(Items);
                Items = null;
            }
            if (Uniques != null) {
                for (int i = 0, iMax = Uniques.Count; i < iMax; i++) {
                    Uniques[i].Recycle();
                }
                _poolOfUniques.Recycle(Uniques);
                Uniques = null;
            }
        }

        public static ClientInventoryResponse Deserialize(ref SimpleBinarySerializer sbs, bool withPacketType = true) {
            if (withPacketType && sbs.ReadU16() != (ushort)SB_PacketType.ClientInventoryResponse) { throw new Exception(); }
            var v = New();
            for (int i = 0, iMax = sbs.ReadU16(); i < iMax; i++) {
                v.Items.Add(ItemQuantity.Deserialize(ref sbs, false));
            }
            for (int i = 0, iMax = sbs.ReadU16(); i < iMax; i++) {
                v.Uniques.Add(ClientInventoryUnique.Deserialize(ref sbs, false));
            }
            return v;
        }

        public void Serialize(ref SimpleBinarySerializer sbs, bool withPacketType = true) {
            if (withPacketType) { sbs.WriteU16((ushort)SB_PacketType.ClientInventoryResponse); }
            var ItemsCount = Items.Count;
            sbs.WriteU16((ushort)ItemsCount);
            for (var i = 0; i < ItemsCount; i++) {
                Items[i].Serialize(ref sbs, false);
            }
            var UniquesCount = Uniques.Count;
            sbs.WriteU16((ushort)UniquesCount);
            for (var i = 0; i < UniquesCount; i++) {
                Uniques[i].Serialize(ref sbs, false);
            }
        }
    }

    public enum TimerType : byte {
        Use,
        Craft,
        Trauma,
        Boost,
    }

    public struct ClientTimerItem {
        #if DEBUG
        [Obsolete("Use SB_PacketType.ClientTimerItem instead.")]
        #endif
        public const ushort SB_PacketId = (ushort)SB_PacketType.ClientTimerItem;
        public TimerType TimerType;
        public ushort ItemId;
        public string StartTime;
        public string DeadTime;

        public static ClientTimerItem New() {
            ClientTimerItem v = default;
            v.StartTime = "";
            v.DeadTime = "";
            return v;
        }

        public void Recycle() {
        }

        public static ClientTimerItem Deserialize(ref SimpleBinarySerializer sbs, bool withPacketType = true) {
            if (withPacketType && sbs.ReadU16() != (ushort)SB_PacketType.ClientTimerItem) { throw new Exception(); }
            var v = New();
            v.TimerType = (TimerType)sbs.ReadU8();
            v.ItemId = sbs.ReadU16();
            v.StartTime = sbs.ReadS16();
            v.DeadTime = sbs.ReadS16();
            return v;
        }

        public void Serialize(ref SimpleBinarySerializer sbs, bool withPacketType = true) {
            if (withPacketType) { sbs.WriteU16((ushort)SB_PacketType.ClientTimerItem); }
            sbs.WriteU8((byte)TimerType);
            sbs.WriteU16(ItemId);
            sbs.WriteS16(StartTime);
            sbs.WriteS16(DeadTime);
        }
    }

    public struct ClientTimerRequest {
        #if DEBUG
        [Obsolete("Use SB_PacketType.ClientTimerRequest instead.")]
        #endif
        public const ushort SB_PacketId = (ushort)SB_PacketType.ClientTimerRequest;
        public static ClientTimerRequest New() {
            ClientTimerRequest v = default;
            return v;
        }

        public void Recycle() {
        }

        public static ClientTimerRequest Deserialize(ref SimpleBinarySerializer sbs, bool withPacketType = true) {
            if (withPacketType && sbs.ReadU16() != (ushort)SB_PacketType.ClientTimerRequest) { throw new Exception(); }
            var v = New();
            return v;
        }

        public void Serialize(ref SimpleBinarySerializer sbs, bool withPacketType = true) {
            if (withPacketType) { sbs.WriteU16((ushort)SB_PacketType.ClientTimerRequest); }
        }
    }

    public struct ClientTimerResponse {
        #if DEBUG
        [Obsolete("Use SB_PacketType.ClientTimerResponse instead.")]
        #endif
        public const ushort SB_PacketId = (ushort)SB_PacketType.ClientTimerResponse;
        public List<ClientTimerItem> Ended;
        public List<ClientTimerItem> Active;
        static ListPool<ClientTimerItem> _poolOfEnded = new ListPool<ClientTimerItem>();
        static ListPool<ClientTimerItem> _poolOfActive = new ListPool<ClientTimerItem>();

        public static ClientTimerResponse New() {
            ClientTimerResponse v = default;
            v.Ended = _poolOfEnded.Get();
            v.Active = _poolOfActive.Get();
            return v;
        }

        public void Recycle() {
            if (Ended != null) {
                for (int i = 0, iMax = Ended.Count; i < iMax; i++) {
                    Ended[i].Recycle();
                }
                _poolOfEnded.Recycle(Ended);
                Ended = null;
            }
            if (Active != null) {
                for (int i = 0, iMax = Active.Count; i < iMax; i++) {
                    Active[i].Recycle();
                }
                _poolOfActive.Recycle(Active);
                Active = null;
            }
        }

        public static ClientTimerResponse Deserialize(ref SimpleBinarySerializer sbs, bool withPacketType = true) {
            if (withPacketType && sbs.ReadU16() != (ushort)SB_PacketType.ClientTimerResponse) { throw new Exception(); }
            var v = New();
            for (int i = 0, iMax = sbs.ReadU16(); i < iMax; i++) {
                v.Ended.Add(ClientTimerItem.Deserialize(ref sbs, false));
            }
            for (int i = 0, iMax = sbs.ReadU16(); i < iMax; i++) {
                v.Active.Add(ClientTimerItem.Deserialize(ref sbs, false));
            }
            return v;
        }

        public void Serialize(ref SimpleBinarySerializer sbs, bool withPacketType = true) {
            if (withPacketType) { sbs.WriteU16((ushort)SB_PacketType.ClientTimerResponse); }
            var EndedCount = Ended.Count;
            sbs.WriteU16((ushort)EndedCount);
            for (var i = 0; i < EndedCount; i++) {
                Ended[i].Serialize(ref sbs, false);
            }
            var ActiveCount = Active.Count;
            sbs.WriteU16((ushort)ActiveCount);
            for (var i = 0; i < ActiveCount; i++) {
                Active[i].Serialize(ref sbs, false);
            }
        }
    }

    public struct ClientItemEquipRequest {
        #if DEBUG
        [Obsolete("Use SB_PacketType.ClientItemEquipRequest instead.")]
        #endif
        public const ushort SB_PacketId = (ushort)SB_PacketType.ClientItemEquipRequest;
        public string ItemId;

        public static ClientItemEquipRequest New() {
            ClientItemEquipRequest v = default;
            v.ItemId = "";
            return v;
        }

        public void Recycle() {
        }

        public static ClientItemEquipRequest Deserialize(ref SimpleBinarySerializer sbs, bool withPacketType = true) {
            if (withPacketType && sbs.ReadU16() != (ushort)SB_PacketType.ClientItemEquipRequest) { throw new Exception(); }
            var v = New();
            v.ItemId = sbs.ReadS16();
            return v;
        }

        public void Serialize(ref SimpleBinarySerializer sbs, bool withPacketType = true) {
            if (withPacketType) { sbs.WriteU16((ushort)SB_PacketType.ClientItemEquipRequest); }
            sbs.WriteS16(ItemId);
        }
    }

    public struct ClientItemEquipResponse {
        #if DEBUG
        [Obsolete("Use SB_PacketType.ClientItemEquipResponse instead.")]
        #endif
        public const ushort SB_PacketId = (ushort)SB_PacketType.ClientItemEquipResponse;
        public static ClientItemEquipResponse New() {
            ClientItemEquipResponse v = default;
            return v;
        }

        public void Recycle() {
        }

        public static ClientItemEquipResponse Deserialize(ref SimpleBinarySerializer sbs, bool withPacketType = true) {
            if (withPacketType && sbs.ReadU16() != (ushort)SB_PacketType.ClientItemEquipResponse) { throw new Exception(); }
            var v = New();
            return v;
        }

        public void Serialize(ref SimpleBinarySerializer sbs, bool withPacketType = true) {
            if (withPacketType) { sbs.WriteU16((ushort)SB_PacketType.ClientItemEquipResponse); }
        }
    }

    public struct ClientChestOpenItem {
        #if DEBUG
        [Obsolete("Use SB_PacketType.ClientChestOpenItem instead.")]
        #endif
        public const ushort SB_PacketId = (ushort)SB_PacketType.ClientChestOpenItem;
        public ushort X;
        public ushort Y;

        public static ClientChestOpenItem New() {
            ClientChestOpenItem v = default;
            return v;
        }

        public void Recycle() {
        }

        public static ClientChestOpenItem Deserialize(ref SimpleBinarySerializer sbs, bool withPacketType = true) {
            if (withPacketType && sbs.ReadU16() != (ushort)SB_PacketType.ClientChestOpenItem) { throw new Exception(); }
            var v = New();
            v.X = sbs.ReadU16();
            v.Y = sbs.ReadU16();
            return v;
        }

        public void Serialize(ref SimpleBinarySerializer sbs, bool withPacketType = true) {
            if (withPacketType) { sbs.WriteU16((ushort)SB_PacketType.ClientChestOpenItem); }
            sbs.WriteU16(X);
            sbs.WriteU16(Y);
        }
    }

    public struct ClientChestOpenRequest {
        #if DEBUG
        [Obsolete("Use SB_PacketType.ClientChestOpenRequest instead.")]
        #endif
        public const ushort SB_PacketId = (ushort)SB_PacketType.ClientChestOpenRequest;
        public ushort ChestId;
        public ushort LockpickId;

        public static ClientChestOpenRequest New() {
            ClientChestOpenRequest v = default;
            return v;
        }

        public void Recycle() {
        }

        public static ClientChestOpenRequest Deserialize(ref SimpleBinarySerializer sbs, bool withPacketType = true) {
            if (withPacketType && sbs.ReadU16() != (ushort)SB_PacketType.ClientChestOpenRequest) { throw new Exception(); }
            var v = New();
            v.ChestId = sbs.ReadU16();
            v.LockpickId = sbs.ReadU16();
            return v;
        }

        public void Serialize(ref SimpleBinarySerializer sbs, bool withPacketType = true) {
            if (withPacketType) { sbs.WriteU16((ushort)SB_PacketType.ClientChestOpenRequest); }
            sbs.WriteU16(ChestId);
            sbs.WriteU16(LockpickId);
        }
    }

    public struct ClientChestOpenResponse {
        #if DEBUG
        [Obsolete("Use SB_PacketType.ClientChestOpenResponse instead.")]
        #endif
        public const ushort SB_PacketId = (ushort)SB_PacketType.ClientChestOpenResponse;
        public ChestOpenAward Item;
        public ushort ChestId;
        public ChestOpenState State;

        public static ClientChestOpenResponse New() {
            ClientChestOpenResponse v = default;
            return v;
        }

        public void Recycle() {
        }

        public static ClientChestOpenResponse Deserialize(ref SimpleBinarySerializer sbs, bool withPacketType = true) {
            if (withPacketType && sbs.ReadU16() != (ushort)SB_PacketType.ClientChestOpenResponse) { throw new Exception(); }
            var v = New();
            v.Item = ChestOpenAward.Deserialize(ref sbs, false);
            v.ChestId = sbs.ReadU16();
            v.State = (ChestOpenState)sbs.ReadU8();
            return v;
        }

        public void Serialize(ref SimpleBinarySerializer sbs, bool withPacketType = true) {
            if (withPacketType) { sbs.WriteU16((ushort)SB_PacketType.ClientChestOpenResponse); }
            Item.Serialize(ref sbs, false);
            sbs.WriteU16(ChestId);
            sbs.WriteU8((byte)State);
        }
    }

    public struct ClientBattleEnterRequest {
        #if DEBUG
        [Obsolete("Use SB_PacketType.ClientBattleEnterRequest instead.")]
        #endif
        public const ushort SB_PacketId = (ushort)SB_PacketType.ClientBattleEnterRequest;
        public BattleOpponent Opponent;

        public static ClientBattleEnterRequest New() {
            ClientBattleEnterRequest v = default;
            return v;
        }

        public void Recycle() {
        }

        public static ClientBattleEnterRequest Deserialize(ref SimpleBinarySerializer sbs, bool withPacketType = true) {
            if (withPacketType && sbs.ReadU16() != (ushort)SB_PacketType.ClientBattleEnterRequest) { throw new Exception(); }
            var v = New();
            v.Opponent = BattleOpponent.Deserialize(ref sbs, false);
            return v;
        }

        public void Serialize(ref SimpleBinarySerializer sbs, bool withPacketType = true) {
            if (withPacketType) { sbs.WriteU16((ushort)SB_PacketType.ClientBattleEnterRequest); }
            Opponent.Serialize(ref sbs, false);
        }
    }

    public struct ClientBattleEnterResponse {
        #if DEBUG
        [Obsolete("Use SB_PacketType.ClientBattleEnterResponse instead.")]
        #endif
        public const ushort SB_PacketId = (ushort)SB_PacketType.ClientBattleEnterResponse;
        public Battle Battle;

        public static ClientBattleEnterResponse New() {
            ClientBattleEnterResponse v = default;
            return v;
        }

        public void Recycle() {
        }

        public static ClientBattleEnterResponse Deserialize(ref SimpleBinarySerializer sbs, bool withPacketType = true) {
            if (withPacketType && sbs.ReadU16() != (ushort)SB_PacketType.ClientBattleEnterResponse) { throw new Exception(); }
            var v = New();
            v.Battle = Battle.Deserialize(ref sbs, false);
            return v;
        }

        public void Serialize(ref SimpleBinarySerializer sbs, bool withPacketType = true) {
            if (withPacketType) { sbs.WriteU16((ushort)SB_PacketType.ClientBattleEnterResponse); }
            Battle.Serialize(ref sbs, false);
        }
    }

    public struct ClientRegionLeaveRequest {
        #if DEBUG
        [Obsolete("Use SB_PacketType.ClientRegionLeaveRequest instead.")]
        #endif
        public const ushort SB_PacketId = (ushort)SB_PacketType.ClientRegionLeaveRequest;
        public byte Lose;

        public static ClientRegionLeaveRequest New() {
            ClientRegionLeaveRequest v = default;
            return v;
        }

        public void Recycle() {
        }

        public static ClientRegionLeaveRequest Deserialize(ref SimpleBinarySerializer sbs, bool withPacketType = true) {
            if (withPacketType && sbs.ReadU16() != (ushort)SB_PacketType.ClientRegionLeaveRequest) { throw new Exception(); }
            var v = New();
            v.Lose = sbs.ReadU8();
            return v;
        }

        public void Serialize(ref SimpleBinarySerializer sbs, bool withPacketType = true) {
            if (withPacketType) { sbs.WriteU16((ushort)SB_PacketType.ClientRegionLeaveRequest); }
            sbs.WriteU8(Lose);
        }
    }

    public struct ClientRegionLeaveResponse {
        #if DEBUG
        [Obsolete("Use SB_PacketType.ClientRegionLeaveResponse instead.")]
        #endif
        public const ushort SB_PacketId = (ushort)SB_PacketType.ClientRegionLeaveResponse;
        public static ClientRegionLeaveResponse New() {
            ClientRegionLeaveResponse v = default;
            return v;
        }

        public void Recycle() {
        }

        public static ClientRegionLeaveResponse Deserialize(ref SimpleBinarySerializer sbs, bool withPacketType = true) {
            if (withPacketType && sbs.ReadU16() != (ushort)SB_PacketType.ClientRegionLeaveResponse) { throw new Exception(); }
            var v = New();
            return v;
        }

        public void Serialize(ref SimpleBinarySerializer sbs, bool withPacketType = true) {
            if (withPacketType) { sbs.WriteU16((ushort)SB_PacketType.ClientRegionLeaveResponse); }
        }
    }

    public struct ClientRegionEnterRequest {
        #if DEBUG
        [Obsolete("Use SB_PacketType.ClientRegionEnterRequest instead.")]
        #endif
        public const ushort SB_PacketId = (ushort)SB_PacketType.ClientRegionEnterRequest;
        public static ClientRegionEnterRequest New() {
            ClientRegionEnterRequest v = default;
            return v;
        }

        public void Recycle() {
        }

        public static ClientRegionEnterRequest Deserialize(ref SimpleBinarySerializer sbs, bool withPacketType = true) {
            if (withPacketType && sbs.ReadU16() != (ushort)SB_PacketType.ClientRegionEnterRequest) { throw new Exception(); }
            var v = New();
            return v;
        }

        public void Serialize(ref SimpleBinarySerializer sbs, bool withPacketType = true) {
            if (withPacketType) { sbs.WriteU16((ushort)SB_PacketType.ClientRegionEnterRequest); }
        }
    }

    public struct ClientRegionEnterResponse {
        #if DEBUG
        [Obsolete("Use SB_PacketType.ClientRegionEnterResponse instead.")]
        #endif
        public const ushort SB_PacketId = (ushort)SB_PacketType.ClientRegionEnterResponse;
        public static ClientRegionEnterResponse New() {
            ClientRegionEnterResponse v = default;
            return v;
        }

        public void Recycle() {
        }

        public static ClientRegionEnterResponse Deserialize(ref SimpleBinarySerializer sbs, bool withPacketType = true) {
            if (withPacketType && sbs.ReadU16() != (ushort)SB_PacketType.ClientRegionEnterResponse) { throw new Exception(); }
            var v = New();
            return v;
        }

        public void Serialize(ref SimpleBinarySerializer sbs, bool withPacketType = true) {
            if (withPacketType) { sbs.WriteU16((ushort)SB_PacketType.ClientRegionEnterResponse); }
        }
    }

    public enum CurrencyType : byte {
        Unknown,
        Soft,
        Hard,
        Promo,
        Real,
        Blood,
        RP,
    }

    public struct ClientRegionReviveRequest {
        #if DEBUG
        [Obsolete("Use SB_PacketType.ClientRegionReviveRequest instead.")]
        #endif
        public const ushort SB_PacketId = (ushort)SB_PacketType.ClientRegionReviveRequest;
        public CurrencyType Paid;

        public static ClientRegionReviveRequest New() {
            ClientRegionReviveRequest v = default;
            return v;
        }

        public void Recycle() {
        }

        public static ClientRegionReviveRequest Deserialize(ref SimpleBinarySerializer sbs, bool withPacketType = true) {
            if (withPacketType && sbs.ReadU16() != (ushort)SB_PacketType.ClientRegionReviveRequest) { throw new Exception(); }
            var v = New();
            v.Paid = (CurrencyType)sbs.ReadU8();
            return v;
        }

        public void Serialize(ref SimpleBinarySerializer sbs, bool withPacketType = true) {
            if (withPacketType) { sbs.WriteU16((ushort)SB_PacketType.ClientRegionReviveRequest); }
            sbs.WriteU8((byte)Paid);
        }
    }

    public struct ClientRegionReviveResponse {
        #if DEBUG
        [Obsolete("Use SB_PacketType.ClientRegionReviveResponse instead.")]
        #endif
        public const ushort SB_PacketId = (ushort)SB_PacketType.ClientRegionReviveResponse;
        public static ClientRegionReviveResponse New() {
            ClientRegionReviveResponse v = default;
            return v;
        }

        public void Recycle() {
        }

        public static ClientRegionReviveResponse Deserialize(ref SimpleBinarySerializer sbs, bool withPacketType = true) {
            if (withPacketType && sbs.ReadU16() != (ushort)SB_PacketType.ClientRegionReviveResponse) { throw new Exception(); }
            var v = New();
            return v;
        }

        public void Serialize(ref SimpleBinarySerializer sbs, bool withPacketType = true) {
            if (withPacketType) { sbs.WriteU16((ushort)SB_PacketType.ClientRegionReviveResponse); }
        }
    }

    public struct ClientTraumaAddRequest {
        #if DEBUG
        [Obsolete("Use SB_PacketType.ClientTraumaAddRequest instead.")]
        #endif
        public const ushort SB_PacketId = (ushort)SB_PacketType.ClientTraumaAddRequest;
        public BodyPart Part;

        public static ClientTraumaAddRequest New() {
            ClientTraumaAddRequest v = default;
            return v;
        }

        public void Recycle() {
        }

        public static ClientTraumaAddRequest Deserialize(ref SimpleBinarySerializer sbs, bool withPacketType = true) {
            if (withPacketType && sbs.ReadU16() != (ushort)SB_PacketType.ClientTraumaAddRequest) { throw new Exception(); }
            var v = New();
            v.Part = (BodyPart)sbs.ReadU8();
            return v;
        }

        public void Serialize(ref SimpleBinarySerializer sbs, bool withPacketType = true) {
            if (withPacketType) { sbs.WriteU16((ushort)SB_PacketType.ClientTraumaAddRequest); }
            sbs.WriteU8((byte)Part);
        }
    }

    public struct ClientTraumaAddResponse {
        #if DEBUG
        [Obsolete("Use SB_PacketType.ClientTraumaAddResponse instead.")]
        #endif
        public const ushort SB_PacketId = (ushort)SB_PacketType.ClientTraumaAddResponse;
        public ClientTimerItem Timer;

        public static ClientTraumaAddResponse New() {
            ClientTraumaAddResponse v = default;
            return v;
        }

        public void Recycle() {
        }

        public static ClientTraumaAddResponse Deserialize(ref SimpleBinarySerializer sbs, bool withPacketType = true) {
            if (withPacketType && sbs.ReadU16() != (ushort)SB_PacketType.ClientTraumaAddResponse) { throw new Exception(); }
            var v = New();
            v.Timer = ClientTimerItem.Deserialize(ref sbs, false);
            return v;
        }

        public void Serialize(ref SimpleBinarySerializer sbs, bool withPacketType = true) {
            if (withPacketType) { sbs.WriteU16((ushort)SB_PacketType.ClientTraumaAddResponse); }
            Timer.Serialize(ref sbs, false);
        }
    }

    public struct ClientHardPacksRequest {
        #if DEBUG
        [Obsolete("Use SB_PacketType.ClientHardPacksRequest instead.")]
        #endif
        public const ushort SB_PacketId = (ushort)SB_PacketType.ClientHardPacksRequest;
        public static ClientHardPacksRequest New() {
            ClientHardPacksRequest v = default;
            return v;
        }

        public void Recycle() {
        }

        public static ClientHardPacksRequest Deserialize(ref SimpleBinarySerializer sbs, bool withPacketType = true) {
            if (withPacketType && sbs.ReadU16() != (ushort)SB_PacketType.ClientHardPacksRequest) { throw new Exception(); }
            var v = New();
            return v;
        }

        public void Serialize(ref SimpleBinarySerializer sbs, bool withPacketType = true) {
            if (withPacketType) { sbs.WriteU16((ushort)SB_PacketType.ClientHardPacksRequest); }
        }
    }

    public struct ClientHardPacksResponse {
        #if DEBUG
        [Obsolete("Use SB_PacketType.ClientHardPacksResponse instead.")]
        #endif
        public const ushort SB_PacketId = (ushort)SB_PacketType.ClientHardPacksResponse;
        public byte Sales;

        public static ClientHardPacksResponse New() {
            ClientHardPacksResponse v = default;
            return v;
        }

        public void Recycle() {
        }

        public static ClientHardPacksResponse Deserialize(ref SimpleBinarySerializer sbs, bool withPacketType = true) {
            if (withPacketType && sbs.ReadU16() != (ushort)SB_PacketType.ClientHardPacksResponse) { throw new Exception(); }
            var v = New();
            v.Sales = sbs.ReadU8();
            return v;
        }

        public void Serialize(ref SimpleBinarySerializer sbs, bool withPacketType = true) {
            if (withPacketType) { sbs.WriteU16((ushort)SB_PacketType.ClientHardPacksResponse); }
            sbs.WriteU8(Sales);
        }
    }

    public struct ClientCraftItemRequest {
        #if DEBUG
        [Obsolete("Use SB_PacketType.ClientCraftItemRequest instead.")]
        #endif
        public const ushort SB_PacketId = (ushort)SB_PacketType.ClientCraftItemRequest;
        public ushort ItemId;

        public static ClientCraftItemRequest New() {
            ClientCraftItemRequest v = default;
            return v;
        }

        public void Recycle() {
        }

        public static ClientCraftItemRequest Deserialize(ref SimpleBinarySerializer sbs, bool withPacketType = true) {
            if (withPacketType && sbs.ReadU16() != (ushort)SB_PacketType.ClientCraftItemRequest) { throw new Exception(); }
            var v = New();
            v.ItemId = sbs.ReadU16();
            return v;
        }

        public void Serialize(ref SimpleBinarySerializer sbs, bool withPacketType = true) {
            if (withPacketType) { sbs.WriteU16((ushort)SB_PacketType.ClientCraftItemRequest); }
            sbs.WriteU16(ItemId);
        }
    }

    public struct ClientCraftItemResponse {
        #if DEBUG
        [Obsolete("Use SB_PacketType.ClientCraftItemResponse instead.")]
        #endif
        public const ushort SB_PacketId = (ushort)SB_PacketType.ClientCraftItemResponse;
        public ClientTimerItem Timer;

        public static ClientCraftItemResponse New() {
            ClientCraftItemResponse v = default;
            return v;
        }

        public void Recycle() {
        }

        public static ClientCraftItemResponse Deserialize(ref SimpleBinarySerializer sbs, bool withPacketType = true) {
            if (withPacketType && sbs.ReadU16() != (ushort)SB_PacketType.ClientCraftItemResponse) { throw new Exception(); }
            var v = New();
            v.Timer = ClientTimerItem.Deserialize(ref sbs, false);
            return v;
        }

        public void Serialize(ref SimpleBinarySerializer sbs, bool withPacketType = true) {
            if (withPacketType) { sbs.WriteU16((ushort)SB_PacketType.ClientCraftItemResponse); }
            Timer.Serialize(ref sbs, false);
        }
    }

    public struct ClientForceCraftItemRequest {
        #if DEBUG
        [Obsolete("Use SB_PacketType.ClientForceCraftItemRequest instead.")]
        #endif
        public const ushort SB_PacketId = (ushort)SB_PacketType.ClientForceCraftItemRequest;
        public ushort ItemId;
        public ForceCraftItemType Type;

        public static ClientForceCraftItemRequest New() {
            ClientForceCraftItemRequest v = default;
            return v;
        }

        public void Recycle() {
        }

        public static ClientForceCraftItemRequest Deserialize(ref SimpleBinarySerializer sbs, bool withPacketType = true) {
            if (withPacketType && sbs.ReadU16() != (ushort)SB_PacketType.ClientForceCraftItemRequest) { throw new Exception(); }
            var v = New();
            v.ItemId = sbs.ReadU16();
            v.Type = (ForceCraftItemType)sbs.ReadU8();
            return v;
        }

        public void Serialize(ref SimpleBinarySerializer sbs, bool withPacketType = true) {
            if (withPacketType) { sbs.WriteU16((ushort)SB_PacketType.ClientForceCraftItemRequest); }
            sbs.WriteU16(ItemId);
            sbs.WriteU8((byte)Type);
        }
    }

    public struct ClientForceCraftItemResponse {
        #if DEBUG
        [Obsolete("Use SB_PacketType.ClientForceCraftItemResponse instead.")]
        #endif
        public const ushort SB_PacketId = (ushort)SB_PacketType.ClientForceCraftItemResponse;
        public ClientTimerItem Timer;

        public static ClientForceCraftItemResponse New() {
            ClientForceCraftItemResponse v = default;
            return v;
        }

        public void Recycle() {
        }

        public static ClientForceCraftItemResponse Deserialize(ref SimpleBinarySerializer sbs, bool withPacketType = true) {
            if (withPacketType && sbs.ReadU16() != (ushort)SB_PacketType.ClientForceCraftItemResponse) { throw new Exception(); }
            var v = New();
            v.Timer = ClientTimerItem.Deserialize(ref sbs, false);
            return v;
        }

        public void Serialize(ref SimpleBinarySerializer sbs, bool withPacketType = true) {
            if (withPacketType) { sbs.WriteU16((ushort)SB_PacketType.ClientForceCraftItemResponse); }
            Timer.Serialize(ref sbs, false);
        }
    }

    public struct ClientItemBuyRequest {
        #if DEBUG
        [Obsolete("Use SB_PacketType.ClientItemBuyRequest instead.")]
        #endif
        public const ushort SB_PacketId = (ushort)SB_PacketType.ClientItemBuyRequest;
        public string ItemId;
        public ushort Count;

        public static ClientItemBuyRequest New() {
            ClientItemBuyRequest v = default;
            v.ItemId = "";
            return v;
        }

        public void Recycle() {
        }

        public static ClientItemBuyRequest Deserialize(ref SimpleBinarySerializer sbs, bool withPacketType = true) {
            if (withPacketType && sbs.ReadU16() != (ushort)SB_PacketType.ClientItemBuyRequest) { throw new Exception(); }
            var v = New();
            v.ItemId = sbs.ReadS16();
            v.Count = sbs.ReadU16();
            return v;
        }

        public void Serialize(ref SimpleBinarySerializer sbs, bool withPacketType = true) {
            if (withPacketType) { sbs.WriteU16((ushort)SB_PacketType.ClientItemBuyRequest); }
            sbs.WriteS16(ItemId);
            sbs.WriteU16(Count);
        }
    }

    public struct ClientItemBuyResponse {
        #if DEBUG
        [Obsolete("Use SB_PacketType.ClientItemBuyResponse instead.")]
        #endif
        public const ushort SB_PacketId = (ushort)SB_PacketType.ClientItemBuyResponse;
        public static ClientItemBuyResponse New() {
            ClientItemBuyResponse v = default;
            return v;
        }

        public void Recycle() {
        }

        public static ClientItemBuyResponse Deserialize(ref SimpleBinarySerializer sbs, bool withPacketType = true) {
            if (withPacketType && sbs.ReadU16() != (ushort)SB_PacketType.ClientItemBuyResponse) { throw new Exception(); }
            var v = New();
            return v;
        }

        public void Serialize(ref SimpleBinarySerializer sbs, bool withPacketType = true) {
            if (withPacketType) { sbs.WriteU16((ushort)SB_PacketType.ClientItemBuyResponse); }
        }
    }

    public struct ClientBattleResultRequest {
        #if DEBUG
        [Obsolete("Use SB_PacketType.ClientBattleResultRequest instead.")]
        #endif
        public const ushort SB_PacketId = (ushort)SB_PacketType.ClientBattleResultRequest;
        public Battle Battle;

        public static ClientBattleResultRequest New() {
            ClientBattleResultRequest v = default;
            return v;
        }

        public void Recycle() {
        }

        public static ClientBattleResultRequest Deserialize(ref SimpleBinarySerializer sbs, bool withPacketType = true) {
            if (withPacketType && sbs.ReadU16() != (ushort)SB_PacketType.ClientBattleResultRequest) { throw new Exception(); }
            var v = New();
            v.Battle = Battle.Deserialize(ref sbs, false);
            return v;
        }

        public void Serialize(ref SimpleBinarySerializer sbs, bool withPacketType = true) {
            if (withPacketType) { sbs.WriteU16((ushort)SB_PacketType.ClientBattleResultRequest); }
            Battle.Serialize(ref sbs, false);
        }
    }

    public struct ClientBattleResultResponse {
        #if DEBUG
        [Obsolete("Use SB_PacketType.ClientBattleResultResponse instead.")]
        #endif
        public const ushort SB_PacketId = (ushort)SB_PacketType.ClientBattleResultResponse;
        public ushort Kills;
        public ushort Rank;
        public ushort Points;

        public static ClientBattleResultResponse New() {
            ClientBattleResultResponse v = default;
            return v;
        }

        public void Recycle() {
        }

        public static ClientBattleResultResponse Deserialize(ref SimpleBinarySerializer sbs, bool withPacketType = true) {
            if (withPacketType && sbs.ReadU16() != (ushort)SB_PacketType.ClientBattleResultResponse) { throw new Exception(); }
            var v = New();
            v.Kills = sbs.ReadU16();
            v.Rank = sbs.ReadU16();
            v.Points = sbs.ReadU16();
            return v;
        }

        public void Serialize(ref SimpleBinarySerializer sbs, bool withPacketType = true) {
            if (withPacketType) { sbs.WriteU16((ushort)SB_PacketType.ClientBattleResultResponse); }
            sbs.WriteU16(Kills);
            sbs.WriteU16(Rank);
            sbs.WriteU16(Points);
        }
    }

    public struct ClientPrefRequest {
        #if DEBUG
        [Obsolete("Use SB_PacketType.ClientPrefRequest instead.")]
        #endif
        public const ushort SB_PacketId = (ushort)SB_PacketType.ClientPrefRequest;
        public static ClientPrefRequest New() {
            ClientPrefRequest v = default;
            return v;
        }

        public void Recycle() {
        }

        public static ClientPrefRequest Deserialize(ref SimpleBinarySerializer sbs, bool withPacketType = true) {
            if (withPacketType && sbs.ReadU16() != (ushort)SB_PacketType.ClientPrefRequest) { throw new Exception(); }
            var v = New();
            return v;
        }

        public void Serialize(ref SimpleBinarySerializer sbs, bool withPacketType = true) {
            if (withPacketType) { sbs.WriteU16((ushort)SB_PacketType.ClientPrefRequest); }
        }
    }

    public struct ClientPrefResponse {
        #if DEBUG
        [Obsolete("Use SB_PacketType.ClientPrefResponse instead.")]
        #endif
        public const ushort SB_PacketId = (ushort)SB_PacketType.ClientPrefResponse;
        public Preference Data;

        public static ClientPrefResponse New() {
            ClientPrefResponse v = default;
            return v;
        }

        public void Recycle() {
        }

        public static ClientPrefResponse Deserialize(ref SimpleBinarySerializer sbs, bool withPacketType = true) {
            if (withPacketType && sbs.ReadU16() != (ushort)SB_PacketType.ClientPrefResponse) { throw new Exception(); }
            var v = New();
            v.Data = Preference.Deserialize(ref sbs, false);
            return v;
        }

        public void Serialize(ref SimpleBinarySerializer sbs, bool withPacketType = true) {
            if (withPacketType) { sbs.WriteU16((ushort)SB_PacketType.ClientPrefResponse); }
            Data.Serialize(ref sbs, false);
        }
    }

    public struct ClientTopRequest {
        #if DEBUG
        [Obsolete("Use SB_PacketType.ClientTopRequest instead.")]
        #endif
        public const ushort SB_PacketId = (ushort)SB_PacketType.ClientTopRequest;
        public static ClientTopRequest New() {
            ClientTopRequest v = default;
            return v;
        }

        public void Recycle() {
        }

        public static ClientTopRequest Deserialize(ref SimpleBinarySerializer sbs, bool withPacketType = true) {
            if (withPacketType && sbs.ReadU16() != (ushort)SB_PacketType.ClientTopRequest) { throw new Exception(); }
            var v = New();
            return v;
        }

        public void Serialize(ref SimpleBinarySerializer sbs, bool withPacketType = true) {
            if (withPacketType) { sbs.WriteU16((ushort)SB_PacketType.ClientTopRequest); }
        }
    }

    public struct ClientTopResponse {
        #if DEBUG
        [Obsolete("Use SB_PacketType.ClientTopResponse instead.")]
        #endif
        public const ushort SB_PacketId = (ushort)SB_PacketType.ClientTopResponse;
        public List<AccountInfo> Data;
        static ListPool<AccountInfo> _poolOfData = new ListPool<AccountInfo>();

        public static ClientTopResponse New() {
            ClientTopResponse v = default;
            v.Data = _poolOfData.Get();
            return v;
        }

        public void Recycle() {
            if (Data != null) {
                for (int i = 0, iMax = Data.Count; i < iMax; i++) {
                    Data[i].Recycle();
                }
                _poolOfData.Recycle(Data);
                Data = null;
            }
        }

        public static ClientTopResponse Deserialize(ref SimpleBinarySerializer sbs, bool withPacketType = true) {
            if (withPacketType && sbs.ReadU16() != (ushort)SB_PacketType.ClientTopResponse) { throw new Exception(); }
            var v = New();
            for (int i = 0, iMax = sbs.ReadU16(); i < iMax; i++) {
                v.Data.Add(AccountInfo.Deserialize(ref sbs, false));
            }
            return v;
        }

        public void Serialize(ref SimpleBinarySerializer sbs, bool withPacketType = true) {
            if (withPacketType) { sbs.WriteU16((ushort)SB_PacketType.ClientTopResponse); }
            var DataCount = Data.Count;
            sbs.WriteU16((ushort)DataCount);
            for (var i = 0; i < DataCount; i++) {
                Data[i].Serialize(ref sbs, false);
            }
        }
    }

    public struct ClientTournamentAwardsRequest {
        #if DEBUG
        [Obsolete("Use SB_PacketType.ClientTournamentAwardsRequest instead.")]
        #endif
        public const ushort SB_PacketId = (ushort)SB_PacketType.ClientTournamentAwardsRequest;
        public static ClientTournamentAwardsRequest New() {
            ClientTournamentAwardsRequest v = default;
            return v;
        }

        public void Recycle() {
        }

        public static ClientTournamentAwardsRequest Deserialize(ref SimpleBinarySerializer sbs, bool withPacketType = true) {
            if (withPacketType && sbs.ReadU16() != (ushort)SB_PacketType.ClientTournamentAwardsRequest) { throw new Exception(); }
            var v = New();
            return v;
        }

        public void Serialize(ref SimpleBinarySerializer sbs, bool withPacketType = true) {
            if (withPacketType) { sbs.WriteU16((ushort)SB_PacketType.ClientTournamentAwardsRequest); }
        }
    }

    public struct ClientTournamentAwardsResponse {
        #if DEBUG
        [Obsolete("Use SB_PacketType.ClientTournamentAwardsResponse instead.")]
        #endif
        public const ushort SB_PacketId = (ushort)SB_PacketType.ClientTournamentAwardsResponse;
        public List<TournamentAward> Data;
        static ListPool<TournamentAward> _poolOfData = new ListPool<TournamentAward>();

        public static ClientTournamentAwardsResponse New() {
            ClientTournamentAwardsResponse v = default;
            v.Data = _poolOfData.Get();
            return v;
        }

        public void Recycle() {
            if (Data != null) {
                for (int i = 0, iMax = Data.Count; i < iMax; i++) {
                    Data[i].Recycle();
                }
                _poolOfData.Recycle(Data);
                Data = null;
            }
        }

        public static ClientTournamentAwardsResponse Deserialize(ref SimpleBinarySerializer sbs, bool withPacketType = true) {
            if (withPacketType && sbs.ReadU16() != (ushort)SB_PacketType.ClientTournamentAwardsResponse) { throw new Exception(); }
            var v = New();
            for (int i = 0, iMax = sbs.ReadU16(); i < iMax; i++) {
                v.Data.Add(TournamentAward.Deserialize(ref sbs, false));
            }
            return v;
        }

        public void Serialize(ref SimpleBinarySerializer sbs, bool withPacketType = true) {
            if (withPacketType) { sbs.WriteU16((ushort)SB_PacketType.ClientTournamentAwardsResponse); }
            var DataCount = Data.Count;
            sbs.WriteU16((ushort)DataCount);
            for (var i = 0; i < DataCount; i++) {
                Data[i].Serialize(ref sbs, false);
            }
        }
    }

    public struct ClientUserTournamentAwardRequest {
        #if DEBUG
        [Obsolete("Use SB_PacketType.ClientUserTournamentAwardRequest instead.")]
        #endif
        public const ushort SB_PacketId = (ushort)SB_PacketType.ClientUserTournamentAwardRequest;
        public UserTournamentAwardAction Action;

        public static ClientUserTournamentAwardRequest New() {
            ClientUserTournamentAwardRequest v = default;
            return v;
        }

        public void Recycle() {
        }

        public static ClientUserTournamentAwardRequest Deserialize(ref SimpleBinarySerializer sbs, bool withPacketType = true) {
            if (withPacketType && sbs.ReadU16() != (ushort)SB_PacketType.ClientUserTournamentAwardRequest) { throw new Exception(); }
            var v = New();
            v.Action = (UserTournamentAwardAction)sbs.ReadU8();
            return v;
        }

        public void Serialize(ref SimpleBinarySerializer sbs, bool withPacketType = true) {
            if (withPacketType) { sbs.WriteU16((ushort)SB_PacketType.ClientUserTournamentAwardRequest); }
            sbs.WriteU8((byte)Action);
        }
    }

    public struct ClientUserTournamentAwardResponse {
        #if DEBUG
        [Obsolete("Use SB_PacketType.ClientUserTournamentAwardResponse instead.")]
        #endif
        public const ushort SB_PacketId = (ushort)SB_PacketType.ClientUserTournamentAwardResponse;
        public UserTournamentAward Award;
        public byte State;

        public static ClientUserTournamentAwardResponse New() {
            ClientUserTournamentAwardResponse v = default;
            return v;
        }

        public void Recycle() {
        }

        public static ClientUserTournamentAwardResponse Deserialize(ref SimpleBinarySerializer sbs, bool withPacketType = true) {
            if (withPacketType && sbs.ReadU16() != (ushort)SB_PacketType.ClientUserTournamentAwardResponse) { throw new Exception(); }
            var v = New();
            v.Award = UserTournamentAward.Deserialize(ref sbs, false);
            v.State = sbs.ReadU8();
            return v;
        }

        public void Serialize(ref SimpleBinarySerializer sbs, bool withPacketType = true) {
            if (withPacketType) { sbs.WriteU16((ushort)SB_PacketType.ClientUserTournamentAwardResponse); }
            Award.Serialize(ref sbs, false);
            sbs.WriteU8(State);
        }
    }

    public struct ClientTournamentInfoRequest {
        #if DEBUG
        [Obsolete("Use SB_PacketType.ClientTournamentInfoRequest instead.")]
        #endif
        public const ushort SB_PacketId = (ushort)SB_PacketType.ClientTournamentInfoRequest;
        public static ClientTournamentInfoRequest New() {
            ClientTournamentInfoRequest v = default;
            return v;
        }

        public void Recycle() {
        }

        public static ClientTournamentInfoRequest Deserialize(ref SimpleBinarySerializer sbs, bool withPacketType = true) {
            if (withPacketType && sbs.ReadU16() != (ushort)SB_PacketType.ClientTournamentInfoRequest) { throw new Exception(); }
            var v = New();
            return v;
        }

        public void Serialize(ref SimpleBinarySerializer sbs, bool withPacketType = true) {
            if (withPacketType) { sbs.WriteU16((ushort)SB_PacketType.ClientTournamentInfoRequest); }
        }
    }

    public struct ClientTournamentInfoResponse {
        #if DEBUG
        [Obsolete("Use SB_PacketType.ClientTournamentInfoResponse instead.")]
        #endif
        public const ushort SB_PacketId = (ushort)SB_PacketType.ClientTournamentInfoResponse;
        public string Finished;
        public List<AccountInfo> Data;
        static ListPool<AccountInfo> _poolOfData = new ListPool<AccountInfo>();

        public static ClientTournamentInfoResponse New() {
            ClientTournamentInfoResponse v = default;
            v.Finished = "";
            v.Data = _poolOfData.Get();
            return v;
        }

        public void Recycle() {
            if (Data != null) {
                for (int i = 0, iMax = Data.Count; i < iMax; i++) {
                    Data[i].Recycle();
                }
                _poolOfData.Recycle(Data);
                Data = null;
            }
        }

        public static ClientTournamentInfoResponse Deserialize(ref SimpleBinarySerializer sbs, bool withPacketType = true) {
            if (withPacketType && sbs.ReadU16() != (ushort)SB_PacketType.ClientTournamentInfoResponse) { throw new Exception(); }
            var v = New();
            v.Finished = sbs.ReadS16();
            for (int i = 0, iMax = sbs.ReadU16(); i < iMax; i++) {
                v.Data.Add(AccountInfo.Deserialize(ref sbs, false));
            }
            return v;
        }

        public void Serialize(ref SimpleBinarySerializer sbs, bool withPacketType = true) {
            if (withPacketType) { sbs.WriteU16((ushort)SB_PacketType.ClientTournamentInfoResponse); }
            sbs.WriteS16(Finished);
            var DataCount = Data.Count;
            sbs.WriteU16((ushort)DataCount);
            for (var i = 0; i < DataCount; i++) {
                Data[i].Serialize(ref sbs, false);
            }
        }
    }

    public struct ClientKillLocalInfoRequest {
        #if DEBUG
        [Obsolete("Use SB_PacketType.ClientKillLocalInfoRequest instead.")]
        #endif
        public const ushort SB_PacketId = (ushort)SB_PacketType.ClientKillLocalInfoRequest;
        public static ClientKillLocalInfoRequest New() {
            ClientKillLocalInfoRequest v = default;
            return v;
        }

        public void Recycle() {
        }

        public static ClientKillLocalInfoRequest Deserialize(ref SimpleBinarySerializer sbs, bool withPacketType = true) {
            if (withPacketType && sbs.ReadU16() != (ushort)SB_PacketType.ClientKillLocalInfoRequest) { throw new Exception(); }
            var v = New();
            return v;
        }

        public void Serialize(ref SimpleBinarySerializer sbs, bool withPacketType = true) {
            if (withPacketType) { sbs.WriteU16((ushort)SB_PacketType.ClientKillLocalInfoRequest); }
        }
    }

    public struct ClientKillLocalInfoResponse {
        #if DEBUG
        [Obsolete("Use SB_PacketType.ClientKillLocalInfoResponse instead.")]
        #endif
        public const ushort SB_PacketId = (ushort)SB_PacketType.ClientKillLocalInfoResponse;
        public List<KillLocalInfo> Data;
        static ListPool<KillLocalInfo> _poolOfData = new ListPool<KillLocalInfo>();

        public static ClientKillLocalInfoResponse New() {
            ClientKillLocalInfoResponse v = default;
            v.Data = _poolOfData.Get();
            return v;
        }

        public void Recycle() {
            if (Data != null) {
                for (int i = 0, iMax = Data.Count; i < iMax; i++) {
                    Data[i].Recycle();
                }
                _poolOfData.Recycle(Data);
                Data = null;
            }
        }

        public static ClientKillLocalInfoResponse Deserialize(ref SimpleBinarySerializer sbs, bool withPacketType = true) {
            if (withPacketType && sbs.ReadU16() != (ushort)SB_PacketType.ClientKillLocalInfoResponse) { throw new Exception(); }
            var v = New();
            for (int i = 0, iMax = sbs.ReadU16(); i < iMax; i++) {
                v.Data.Add(KillLocalInfo.Deserialize(ref sbs, false));
            }
            return v;
        }

        public void Serialize(ref SimpleBinarySerializer sbs, bool withPacketType = true) {
            if (withPacketType) { sbs.WriteU16((ushort)SB_PacketType.ClientKillLocalInfoResponse); }
            var DataCount = Data.Count;
            sbs.WriteU16((ushort)DataCount);
            for (var i = 0; i < DataCount; i++) {
                Data[i].Serialize(ref sbs, false);
            }
        }
    }

    public struct ClientKillLocalAwardRequest {
        #if DEBUG
        [Obsolete("Use SB_PacketType.ClientKillLocalAwardRequest instead.")]
        #endif
        public const ushort SB_PacketId = (ushort)SB_PacketType.ClientKillLocalAwardRequest;
        public byte RegionId;
        public byte LocationId;

        public static ClientKillLocalAwardRequest New() {
            ClientKillLocalAwardRequest v = default;
            return v;
        }

        public void Recycle() {
        }

        public static ClientKillLocalAwardRequest Deserialize(ref SimpleBinarySerializer sbs, bool withPacketType = true) {
            if (withPacketType && sbs.ReadU16() != (ushort)SB_PacketType.ClientKillLocalAwardRequest) { throw new Exception(); }
            var v = New();
            v.RegionId = sbs.ReadU8();
            v.LocationId = sbs.ReadU8();
            return v;
        }

        public void Serialize(ref SimpleBinarySerializer sbs, bool withPacketType = true) {
            if (withPacketType) { sbs.WriteU16((ushort)SB_PacketType.ClientKillLocalAwardRequest); }
            sbs.WriteU8(RegionId);
            sbs.WriteU8(LocationId);
        }
    }

    public struct ClientKillLocalAwardResponse {
        #if DEBUG
        [Obsolete("Use SB_PacketType.ClientKillLocalAwardResponse instead.")]
        #endif
        public const ushort SB_PacketId = (ushort)SB_PacketType.ClientKillLocalAwardResponse;
        public PurchaseStatus Status;
        public List<ItemQuantity> Rewards;
        static ListPool<ItemQuantity> _poolOfRewards = new ListPool<ItemQuantity>();

        public static ClientKillLocalAwardResponse New() {
            ClientKillLocalAwardResponse v = default;
            v.Rewards = _poolOfRewards.Get();
            return v;
        }

        public void Recycle() {
            if (Rewards != null) {
                for (int i = 0, iMax = Rewards.Count; i < iMax; i++) {
                    Rewards[i].Recycle();
                }
                _poolOfRewards.Recycle(Rewards);
                Rewards = null;
            }
        }

        public static ClientKillLocalAwardResponse Deserialize(ref SimpleBinarySerializer sbs, bool withPacketType = true) {
            if (withPacketType && sbs.ReadU16() != (ushort)SB_PacketType.ClientKillLocalAwardResponse) { throw new Exception(); }
            var v = New();
            v.Status = (PurchaseStatus)sbs.ReadU8();
            for (int i = 0, iMax = sbs.ReadU16(); i < iMax; i++) {
                v.Rewards.Add(ItemQuantity.Deserialize(ref sbs, false));
            }
            return v;
        }

        public void Serialize(ref SimpleBinarySerializer sbs, bool withPacketType = true) {
            if (withPacketType) { sbs.WriteU16((ushort)SB_PacketType.ClientKillLocalAwardResponse); }
            sbs.WriteU8((byte)Status);
            var RewardsCount = Rewards.Count;
            sbs.WriteU16((ushort)RewardsCount);
            for (var i = 0; i < RewardsCount; i++) {
                Rewards[i].Serialize(ref sbs, false);
            }
        }
    }

    public struct ClientCreatePurchaseRequest {
        #if DEBUG
        [Obsolete("Use SB_PacketType.ClientCreatePurchaseRequest instead.")]
        #endif
        public const ushort SB_PacketId = (ushort)SB_PacketType.ClientCreatePurchaseRequest;
        public string StoreId;
        public GatewayType Gateway;

        public static ClientCreatePurchaseRequest New() {
            ClientCreatePurchaseRequest v = default;
            v.StoreId = "";
            return v;
        }

        public void Recycle() {
        }

        public static ClientCreatePurchaseRequest Deserialize(ref SimpleBinarySerializer sbs, bool withPacketType = true) {
            if (withPacketType && sbs.ReadU16() != (ushort)SB_PacketType.ClientCreatePurchaseRequest) { throw new Exception(); }
            var v = New();
            v.StoreId = sbs.ReadS16();
            v.Gateway = (GatewayType)sbs.ReadU8();
            return v;
        }

        public void Serialize(ref SimpleBinarySerializer sbs, bool withPacketType = true) {
            if (withPacketType) { sbs.WriteU16((ushort)SB_PacketType.ClientCreatePurchaseRequest); }
            sbs.WriteS16(StoreId);
            sbs.WriteU8((byte)Gateway);
        }
    }

    public struct ClientCreatePurchaseResponse {
        #if DEBUG
        [Obsolete("Use SB_PacketType.ClientCreatePurchaseResponse instead.")]
        #endif
        public const ushort SB_PacketId = (ushort)SB_PacketType.ClientCreatePurchaseResponse;
        public Purchase Purchase;
        public string Order;
        public string Reference;
        public PurchaseState State;
        public GatewayType Gateway;

        public static ClientCreatePurchaseResponse New() {
            ClientCreatePurchaseResponse v = default;
            v.Order = "";
            v.Reference = "";
            return v;
        }

        public void Recycle() {
        }

        public static ClientCreatePurchaseResponse Deserialize(ref SimpleBinarySerializer sbs, bool withPacketType = true) {
            if (withPacketType && sbs.ReadU16() != (ushort)SB_PacketType.ClientCreatePurchaseResponse) { throw new Exception(); }
            var v = New();
            v.Purchase = Purchase.Deserialize(ref sbs, false);
            v.Order = sbs.ReadS16();
            v.Reference = sbs.ReadS16();
            v.State = (PurchaseState)sbs.ReadU8();
            v.Gateway = (GatewayType)sbs.ReadU8();
            return v;
        }

        public void Serialize(ref SimpleBinarySerializer sbs, bool withPacketType = true) {
            if (withPacketType) { sbs.WriteU16((ushort)SB_PacketType.ClientCreatePurchaseResponse); }
            Purchase.Serialize(ref sbs, false);
            sbs.WriteS16(Order);
            sbs.WriteS16(Reference);
            sbs.WriteU8((byte)State);
            sbs.WriteU8((byte)Gateway);
        }
    }

    public struct ClientPurchaseListRequest {
        #if DEBUG
        [Obsolete("Use SB_PacketType.ClientPurchaseListRequest instead.")]
        #endif
        public const ushort SB_PacketId = (ushort)SB_PacketType.ClientPurchaseListRequest;
        public static ClientPurchaseListRequest New() {
            ClientPurchaseListRequest v = default;
            return v;
        }

        public void Recycle() {
        }

        public static ClientPurchaseListRequest Deserialize(ref SimpleBinarySerializer sbs, bool withPacketType = true) {
            if (withPacketType && sbs.ReadU16() != (ushort)SB_PacketType.ClientPurchaseListRequest) { throw new Exception(); }
            var v = New();
            return v;
        }

        public void Serialize(ref SimpleBinarySerializer sbs, bool withPacketType = true) {
            if (withPacketType) { sbs.WriteU16((ushort)SB_PacketType.ClientPurchaseListRequest); }
        }
    }

    public struct ClientPurchaseListResponse {
        #if DEBUG
        [Obsolete("Use SB_PacketType.ClientPurchaseListResponse instead.")]
        #endif
        public const ushort SB_PacketId = (ushort)SB_PacketType.ClientPurchaseListResponse;
        public List<Purchase> Data;
        static ListPool<Purchase> _poolOfData = new ListPool<Purchase>();

        public static ClientPurchaseListResponse New() {
            ClientPurchaseListResponse v = default;
            v.Data = _poolOfData.Get();
            return v;
        }

        public void Recycle() {
            if (Data != null) {
                for (int i = 0, iMax = Data.Count; i < iMax; i++) {
                    Data[i].Recycle();
                }
                _poolOfData.Recycle(Data);
                Data = null;
            }
        }

        public static ClientPurchaseListResponse Deserialize(ref SimpleBinarySerializer sbs, bool withPacketType = true) {
            if (withPacketType && sbs.ReadU16() != (ushort)SB_PacketType.ClientPurchaseListResponse) { throw new Exception(); }
            var v = New();
            for (int i = 0, iMax = sbs.ReadU16(); i < iMax; i++) {
                v.Data.Add(Purchase.Deserialize(ref sbs, false));
            }
            return v;
        }

        public void Serialize(ref SimpleBinarySerializer sbs, bool withPacketType = true) {
            if (withPacketType) { sbs.WriteU16((ushort)SB_PacketType.ClientPurchaseListResponse); }
            var DataCount = Data.Count;
            sbs.WriteU16((ushort)DataCount);
            for (var i = 0; i < DataCount; i++) {
                Data[i].Serialize(ref sbs, false);
            }
        }
    }

    public enum GatewayType : byte {
        Unknown,
        GooglePay,
        ApplePay,
        Promo,
        Exchange,
        HardPay,
    }

    public enum PurchaseType : byte {
        Unknown,
        Store,
        Promo,
        Exchange,
    }

    public enum PurchaseStatus : byte {
        Off,
        On,
    }

    public enum PurchaseState : byte {
        Unknown,
        Create,
        InProgress,
        Decline,
        Success,
    }

    public struct Purchase {
        #if DEBUG
        [Obsolete("Use SB_PacketType.Purchase instead.")]
        #endif
        public const ushort SB_PacketId = (ushort)SB_PacketType.Purchase;
        public ushort Id;
        public string Name;
        public string StoreId;
        public PurchaseType Type;
        public PurchaseStatus Status;
        public string BeginDate;
        public string EndDate;
        public float HardAmount;
        public float SoftAmount;
        public byte LimitWindowCount;
        public byte LimitWindowHours;
        public BooleanType IsBonus;

        public static Purchase New() {
            Purchase v = default;
            v.Name = "";
            v.StoreId = "";
            v.BeginDate = "";
            v.EndDate = "";
            return v;
        }

        public void Recycle() {
        }

        public static Purchase Deserialize(ref SimpleBinarySerializer sbs, bool withPacketType = true) {
            if (withPacketType && sbs.ReadU16() != (ushort)SB_PacketType.Purchase) { throw new Exception(); }
            var v = New();
            v.Id = sbs.ReadU16();
            v.Name = sbs.ReadS16();
            v.StoreId = sbs.ReadS16();
            v.Type = (PurchaseType)sbs.ReadU8();
            v.Status = (PurchaseStatus)sbs.ReadU8();
            v.BeginDate = sbs.ReadS16();
            v.EndDate = sbs.ReadS16();
            v.HardAmount = sbs.ReadF32();
            v.SoftAmount = sbs.ReadF32();
            v.LimitWindowCount = sbs.ReadU8();
            v.LimitWindowHours = sbs.ReadU8();
            v.IsBonus = (BooleanType)sbs.ReadU8();
            return v;
        }

        public void Serialize(ref SimpleBinarySerializer sbs, bool withPacketType = true) {
            if (withPacketType) { sbs.WriteU16((ushort)SB_PacketType.Purchase); }
            sbs.WriteU16(Id);
            sbs.WriteS16(Name);
            sbs.WriteS16(StoreId);
            sbs.WriteU8((byte)Type);
            sbs.WriteU8((byte)Status);
            sbs.WriteS16(BeginDate);
            sbs.WriteS16(EndDate);
            sbs.WriteF32(HardAmount);
            sbs.WriteF32(SoftAmount);
            sbs.WriteU8(LimitWindowCount);
            sbs.WriteU8(LimitWindowHours);
            sbs.WriteU8((byte)IsBonus);
        }
    }

    public struct ConfirmPurchaseRequest {
        #if DEBUG
        [Obsolete("Use SB_PacketType.ConfirmPurchaseRequest instead.")]
        #endif
        public const ushort SB_PacketId = (ushort)SB_PacketType.ConfirmPurchaseRequest;
        public string Order;
        public string Details;

        public static ConfirmPurchaseRequest New() {
            ConfirmPurchaseRequest v = default;
            v.Order = "";
            v.Details = "";
            return v;
        }

        public void Recycle() {
        }

        public static ConfirmPurchaseRequest Deserialize(ref SimpleBinarySerializer sbs, bool withPacketType = true) {
            if (withPacketType && sbs.ReadU16() != (ushort)SB_PacketType.ConfirmPurchaseRequest) { throw new Exception(); }
            var v = New();
            v.Order = sbs.ReadS16();
            v.Details = sbs.ReadS16();
            return v;
        }

        public void Serialize(ref SimpleBinarySerializer sbs, bool withPacketType = true) {
            if (withPacketType) { sbs.WriteU16((ushort)SB_PacketType.ConfirmPurchaseRequest); }
            sbs.WriteS16(Order);
            sbs.WriteS16(Details);
        }
    }

    public struct ConfirmPurchaseResponse {
        #if DEBUG
        [Obsolete("Use SB_PacketType.ConfirmPurchaseResponse instead.")]
        #endif
        public const ushort SB_PacketId = (ushort)SB_PacketType.ConfirmPurchaseResponse;
        public Purchase Purchase;
        public string Order;
        public PurchaseState State;

        public static ConfirmPurchaseResponse New() {
            ConfirmPurchaseResponse v = default;
            v.Order = "";
            return v;
        }

        public void Recycle() {
        }

        public static ConfirmPurchaseResponse Deserialize(ref SimpleBinarySerializer sbs, bool withPacketType = true) {
            if (withPacketType && sbs.ReadU16() != (ushort)SB_PacketType.ConfirmPurchaseResponse) { throw new Exception(); }
            var v = New();
            v.Purchase = Purchase.Deserialize(ref sbs, false);
            v.Order = sbs.ReadS16();
            v.State = (PurchaseState)sbs.ReadU8();
            return v;
        }

        public void Serialize(ref SimpleBinarySerializer sbs, bool withPacketType = true) {
            if (withPacketType) { sbs.WriteU16((ushort)SB_PacketType.ConfirmPurchaseResponse); }
            Purchase.Serialize(ref sbs, false);
            sbs.WriteS16(Order);
            sbs.WriteU8((byte)State);
        }
    }

    public enum BodyPart : byte {
        Unknown,
        Head,
        Body,
        Arm,
        Leg,
        Hips,
    }

    public enum FrontGuideType : byte {
        Unknown,
        Preference,
        Localization,
        BotNames,
        Countries,
        BotDifficulty,
        BotTypes,
        ServerInfo,
        AbilitiesGrade,
        ItemAbilities,
        EffectModifiersByItems,
        RankInfo,
        ItemsIcon,
        Effects,
        ItemEffects,
        Ability,
        BotDifficultyWinStreak,
        RegionResultWheel,
        MMR,
        DailyBonus,
        DailyBonusRankCoeff,
        DailyBonusDayStreak,
        DailyBonusNew,
    }

    public struct FrontGuidesRequest {
        #if DEBUG
        [Obsolete("Use SB_PacketType.FrontGuidesRequest instead.")]
        #endif
        public const ushort SB_PacketId = (ushort)SB_PacketType.FrontGuidesRequest;
        public string Timestamp;
        public FrontGuideType GuideType;

        public static FrontGuidesRequest New() {
            FrontGuidesRequest v = default;
            v.Timestamp = "";
            return v;
        }

        public void Recycle() {
        }

        public static FrontGuidesRequest Deserialize(ref SimpleBinarySerializer sbs, bool withPacketType = true) {
            if (withPacketType && sbs.ReadU16() != (ushort)SB_PacketType.FrontGuidesRequest) { throw new Exception(); }
            var v = New();
            v.Timestamp = sbs.ReadS16();
            v.GuideType = (FrontGuideType)sbs.ReadU8();
            return v;
        }

        public void Serialize(ref SimpleBinarySerializer sbs, bool withPacketType = true) {
            if (withPacketType) { sbs.WriteU16((ushort)SB_PacketType.FrontGuidesRequest); }
            sbs.WriteS16(Timestamp);
            sbs.WriteU8((byte)GuideType);
        }
    }

    public struct FrontGuidesResponse {
        #if DEBUG
        [Obsolete("Use SB_PacketType.FrontGuidesResponse instead.")]
        #endif
        public const ushort SB_PacketId = (ushort)SB_PacketType.FrontGuidesResponse;
        public string Timestamp;
        public FrontGuideType GuideType;
        public List<string> Data;
        static ListPool<string> _poolOfData = new ListPool<string>();

        public static FrontGuidesResponse New() {
            FrontGuidesResponse v = default;
            v.Timestamp = "";
            v.Data = _poolOfData.Get();
            return v;
        }

        public void Recycle() {
            _poolOfData.Recycle(Data);
        }

        public static FrontGuidesResponse Deserialize(ref SimpleBinarySerializer sbs, bool withPacketType = true) {
            if (withPacketType && sbs.ReadU16() != (ushort)SB_PacketType.FrontGuidesResponse) { throw new Exception(); }
            var v = New();
            v.Timestamp = sbs.ReadS16();
            v.GuideType = (FrontGuideType)sbs.ReadU8();
            for (int i = 0, iMax = sbs.ReadU16(); i < iMax; i++) {
                v.Data.Add(sbs.ReadS16());
            }
            return v;
        }

        public void Serialize(ref SimpleBinarySerializer sbs, bool withPacketType = true) {
            if (withPacketType) { sbs.WriteU16((ushort)SB_PacketType.FrontGuidesResponse); }
            sbs.WriteS16(Timestamp);
            sbs.WriteU8((byte)GuideType);
            var DataCount = Data.Count;
            sbs.WriteU16((ushort)DataCount);
            for (var i = 0; i < DataCount; i++) {
                sbs.WriteS16(Data[i]);
            }
        }
    }

    public enum QuestType : byte {
        Unknown,
        Entry,
        Daily,
        Achievement,
        TrainingCamp,
        Regular,
    }

    public enum QuestState : byte {
        Init,
        InProgress,
        Completed,
        Canceled,
    }

    public struct QuestGoalProgress {
        #if DEBUG
        [Obsolete("Use SB_PacketType.QuestGoalProgress instead.")]
        #endif
        public const ushort SB_PacketId = (ushort)SB_PacketType.QuestGoalProgress;
        public ushort GoalId;
        public uint Step;

        public static QuestGoalProgress New() {
            QuestGoalProgress v = default;
            return v;
        }

        public void Recycle() {
        }

        public static QuestGoalProgress Deserialize(ref SimpleBinarySerializer sbs, bool withPacketType = true) {
            if (withPacketType && sbs.ReadU16() != (ushort)SB_PacketType.QuestGoalProgress) { throw new Exception(); }
            var v = New();
            v.GoalId = sbs.ReadU16();
            v.Step = sbs.ReadU32();
            return v;
        }

        public void Serialize(ref SimpleBinarySerializer sbs, bool withPacketType = true) {
            if (withPacketType) { sbs.WriteU16((ushort)SB_PacketType.QuestGoalProgress); }
            sbs.WriteU16(GoalId);
            sbs.WriteU32(Step);
        }
    }

    public struct QuestGoal {
        #if DEBUG
        [Obsolete("Use SB_PacketType.QuestGoal instead.")]
        #endif
        public const ushort SB_PacketId = (ushort)SB_PacketType.QuestGoal;
        public ushort Id;
        public string Type;
        public uint Steps;
        public string Parameters;
        public string GoTo;

        public static QuestGoal New() {
            QuestGoal v = default;
            v.Type = "";
            v.Parameters = "";
            v.GoTo = "";
            return v;
        }

        public void Recycle() {
        }

        public static QuestGoal Deserialize(ref SimpleBinarySerializer sbs, bool withPacketType = true) {
            if (withPacketType && sbs.ReadU16() != (ushort)SB_PacketType.QuestGoal) { throw new Exception(); }
            var v = New();
            v.Id = sbs.ReadU16();
            v.Type = sbs.ReadS16();
            v.Steps = sbs.ReadU32();
            v.Parameters = sbs.ReadS16();
            v.GoTo = sbs.ReadS16();
            return v;
        }

        public void Serialize(ref SimpleBinarySerializer sbs, bool withPacketType = true) {
            if (withPacketType) { sbs.WriteU16((ushort)SB_PacketType.QuestGoal); }
            sbs.WriteU16(Id);
            sbs.WriteS16(Type);
            sbs.WriteU32(Steps);
            sbs.WriteS16(Parameters);
            sbs.WriteS16(GoTo);
        }
    }

    public enum QuestRewardState : byte {
        Available,
        Claimed,
    }

    public struct Quest {
        #if DEBUG
        [Obsolete("Use SB_PacketType.Quest instead.")]
        #endif
        public const ushort SB_PacketId = (ushort)SB_PacketType.Quest;
        public ushort Id;
        public string Name;
        public string Description;
        public QuestType Type;
        public byte Rank;
        public List<QuestGoal> Goals;
        public List<ItemQuantity> Rewards;
        static ListPool<QuestGoal> _poolOfGoals = new ListPool<QuestGoal>();
        static ListPool<ItemQuantity> _poolOfRewards = new ListPool<ItemQuantity>();

        public static Quest New() {
            Quest v = default;
            v.Name = "";
            v.Description = "";
            v.Goals = _poolOfGoals.Get();
            v.Rewards = _poolOfRewards.Get();
            return v;
        }

        public void Recycle() {
            if (Goals != null) {
                for (int i = 0, iMax = Goals.Count; i < iMax; i++) {
                    Goals[i].Recycle();
                }
                _poolOfGoals.Recycle(Goals);
                Goals = null;
            }
            if (Rewards != null) {
                for (int i = 0, iMax = Rewards.Count; i < iMax; i++) {
                    Rewards[i].Recycle();
                }
                _poolOfRewards.Recycle(Rewards);
                Rewards = null;
            }
        }

        public static Quest Deserialize(ref SimpleBinarySerializer sbs, bool withPacketType = true) {
            if (withPacketType && sbs.ReadU16() != (ushort)SB_PacketType.Quest) { throw new Exception(); }
            var v = New();
            v.Id = sbs.ReadU16();
            v.Name = sbs.ReadS16();
            v.Description = sbs.ReadS16();
            v.Type = (QuestType)sbs.ReadU8();
            v.Rank = sbs.ReadU8();
            for (int i = 0, iMax = sbs.ReadU16(); i < iMax; i++) {
                v.Goals.Add(QuestGoal.Deserialize(ref sbs, false));
            }
            for (int i = 0, iMax = sbs.ReadU16(); i < iMax; i++) {
                v.Rewards.Add(ItemQuantity.Deserialize(ref sbs, false));
            }
            return v;
        }

        public void Serialize(ref SimpleBinarySerializer sbs, bool withPacketType = true) {
            if (withPacketType) { sbs.WriteU16((ushort)SB_PacketType.Quest); }
            sbs.WriteU16(Id);
            sbs.WriteS16(Name);
            sbs.WriteS16(Description);
            sbs.WriteU8((byte)Type);
            sbs.WriteU8(Rank);
            var GoalsCount = Goals.Count;
            sbs.WriteU16((ushort)GoalsCount);
            for (var i = 0; i < GoalsCount; i++) {
                Goals[i].Serialize(ref sbs, false);
            }
            var RewardsCount = Rewards.Count;
            sbs.WriteU16((ushort)RewardsCount);
            for (var i = 0; i < RewardsCount; i++) {
                Rewards[i].Serialize(ref sbs, false);
            }
        }
    }

    public struct UserQuest {
        #if DEBUG
        [Obsolete("Use SB_PacketType.UserQuest instead.")]
        #endif
        public const ushort SB_PacketId = (ushort)SB_PacketType.UserQuest;
        public ushort QuestId;
        public QuestState QuestState;
        public QuestRewardState RewardState;
        public List<QuestGoalProgress> GoalsProgress;
        static ListPool<QuestGoalProgress> _poolOfGoalsProgress = new ListPool<QuestGoalProgress>();

        public static UserQuest New() {
            UserQuest v = default;
            v.GoalsProgress = _poolOfGoalsProgress.Get();
            return v;
        }

        public void Recycle() {
            if (GoalsProgress != null) {
                for (int i = 0, iMax = GoalsProgress.Count; i < iMax; i++) {
                    GoalsProgress[i].Recycle();
                }
                _poolOfGoalsProgress.Recycle(GoalsProgress);
                GoalsProgress = null;
            }
        }

        public static UserQuest Deserialize(ref SimpleBinarySerializer sbs, bool withPacketType = true) {
            if (withPacketType && sbs.ReadU16() != (ushort)SB_PacketType.UserQuest) { throw new Exception(); }
            var v = New();
            v.QuestId = sbs.ReadU16();
            v.QuestState = (QuestState)sbs.ReadU8();
            v.RewardState = (QuestRewardState)sbs.ReadU8();
            for (int i = 0, iMax = sbs.ReadU16(); i < iMax; i++) {
                v.GoalsProgress.Add(QuestGoalProgress.Deserialize(ref sbs, false));
            }
            return v;
        }

        public void Serialize(ref SimpleBinarySerializer sbs, bool withPacketType = true) {
            if (withPacketType) { sbs.WriteU16((ushort)SB_PacketType.UserQuest); }
            sbs.WriteU16(QuestId);
            sbs.WriteU8((byte)QuestState);
            sbs.WriteU8((byte)RewardState);
            var GoalsProgressCount = GoalsProgress.Count;
            sbs.WriteU16((ushort)GoalsProgressCount);
            for (var i = 0; i < GoalsProgressCount; i++) {
                GoalsProgress[i].Serialize(ref sbs, false);
            }
        }
    }

    public struct QuestsInfoRequest {
        #if DEBUG
        [Obsolete("Use SB_PacketType.QuestsInfoRequest instead.")]
        #endif
        public const ushort SB_PacketId = (ushort)SB_PacketType.QuestsInfoRequest;
        public string Timestamp;

        public static QuestsInfoRequest New() {
            QuestsInfoRequest v = default;
            v.Timestamp = "";
            return v;
        }

        public void Recycle() {
        }

        public static QuestsInfoRequest Deserialize(ref SimpleBinarySerializer sbs, bool withPacketType = true) {
            if (withPacketType && sbs.ReadU16() != (ushort)SB_PacketType.QuestsInfoRequest) { throw new Exception(); }
            var v = New();
            v.Timestamp = sbs.ReadS16();
            return v;
        }

        public void Serialize(ref SimpleBinarySerializer sbs, bool withPacketType = true) {
            if (withPacketType) { sbs.WriteU16((ushort)SB_PacketType.QuestsInfoRequest); }
            sbs.WriteS16(Timestamp);
        }
    }

    public struct QuestsInfoResponse {
        #if DEBUG
        [Obsolete("Use SB_PacketType.QuestsInfoResponse instead.")]
        #endif
        public const ushort SB_PacketId = (ushort)SB_PacketType.QuestsInfoResponse;
        public string Timestamp;
        public List<Quest> Quests;
        static ListPool<Quest> _poolOfQuests = new ListPool<Quest>();

        public static QuestsInfoResponse New() {
            QuestsInfoResponse v = default;
            v.Timestamp = "";
            v.Quests = _poolOfQuests.Get();
            return v;
        }

        public void Recycle() {
            if (Quests != null) {
                for (int i = 0, iMax = Quests.Count; i < iMax; i++) {
                    Quests[i].Recycle();
                }
                _poolOfQuests.Recycle(Quests);
                Quests = null;
            }
        }

        public static QuestsInfoResponse Deserialize(ref SimpleBinarySerializer sbs, bool withPacketType = true) {
            if (withPacketType && sbs.ReadU16() != (ushort)SB_PacketType.QuestsInfoResponse) { throw new Exception(); }
            var v = New();
            v.Timestamp = sbs.ReadS16();
            for (int i = 0, iMax = sbs.ReadU16(); i < iMax; i++) {
                v.Quests.Add(Quest.Deserialize(ref sbs, false));
            }
            return v;
        }

        public void Serialize(ref SimpleBinarySerializer sbs, bool withPacketType = true) {
            if (withPacketType) { sbs.WriteU16((ushort)SB_PacketType.QuestsInfoResponse); }
            sbs.WriteS16(Timestamp);
            var QuestsCount = Quests.Count;
            sbs.WriteU16((ushort)QuestsCount);
            for (var i = 0; i < QuestsCount; i++) {
                Quests[i].Serialize(ref sbs, false);
            }
        }
    }

    public struct QuestsStateRequest {
        #if DEBUG
        [Obsolete("Use SB_PacketType.QuestsStateRequest instead.")]
        #endif
        public const ushort SB_PacketId = (ushort)SB_PacketType.QuestsStateRequest;
        public QuestType Type;

        public static QuestsStateRequest New() {
            QuestsStateRequest v = default;
            return v;
        }

        public void Recycle() {
        }

        public static QuestsStateRequest Deserialize(ref SimpleBinarySerializer sbs, bool withPacketType = true) {
            if (withPacketType && sbs.ReadU16() != (ushort)SB_PacketType.QuestsStateRequest) { throw new Exception(); }
            var v = New();
            v.Type = (QuestType)sbs.ReadU8();
            return v;
        }

        public void Serialize(ref SimpleBinarySerializer sbs, bool withPacketType = true) {
            if (withPacketType) { sbs.WriteU16((ushort)SB_PacketType.QuestsStateRequest); }
            sbs.WriteU8((byte)Type);
        }
    }

    public struct QuestsStateResponse {
        #if DEBUG
        [Obsolete("Use SB_PacketType.QuestsStateResponse instead.")]
        #endif
        public const ushort SB_PacketId = (ushort)SB_PacketType.QuestsStateResponse;
        public List<UserQuest> Quests;
        static ListPool<UserQuest> _poolOfQuests = new ListPool<UserQuest>();

        public static QuestsStateResponse New() {
            QuestsStateResponse v = default;
            v.Quests = _poolOfQuests.Get();
            return v;
        }

        public void Recycle() {
            if (Quests != null) {
                for (int i = 0, iMax = Quests.Count; i < iMax; i++) {
                    Quests[i].Recycle();
                }
                _poolOfQuests.Recycle(Quests);
                Quests = null;
            }
        }

        public static QuestsStateResponse Deserialize(ref SimpleBinarySerializer sbs, bool withPacketType = true) {
            if (withPacketType && sbs.ReadU16() != (ushort)SB_PacketType.QuestsStateResponse) { throw new Exception(); }
            var v = New();
            for (int i = 0, iMax = sbs.ReadU16(); i < iMax; i++) {
                v.Quests.Add(UserQuest.Deserialize(ref sbs, false));
            }
            return v;
        }

        public void Serialize(ref SimpleBinarySerializer sbs, bool withPacketType = true) {
            if (withPacketType) { sbs.WriteU16((ushort)SB_PacketType.QuestsStateResponse); }
            var QuestsCount = Quests.Count;
            sbs.WriteU16((ushort)QuestsCount);
            for (var i = 0; i < QuestsCount; i++) {
                Quests[i].Serialize(ref sbs, false);
            }
        }
    }

    public struct QuestsProgressRequest {
        #if DEBUG
        [Obsolete("Use SB_PacketType.QuestsProgressRequest instead.")]
        #endif
        public const ushort SB_PacketId = (ushort)SB_PacketType.QuestsProgressRequest;
        public List<UserQuest> Quests;
        static ListPool<UserQuest> _poolOfQuests = new ListPool<UserQuest>();

        public static QuestsProgressRequest New() {
            QuestsProgressRequest v = default;
            v.Quests = _poolOfQuests.Get();
            return v;
        }

        public void Recycle() {
            if (Quests != null) {
                for (int i = 0, iMax = Quests.Count; i < iMax; i++) {
                    Quests[i].Recycle();
                }
                _poolOfQuests.Recycle(Quests);
                Quests = null;
            }
        }

        public static QuestsProgressRequest Deserialize(ref SimpleBinarySerializer sbs, bool withPacketType = true) {
            if (withPacketType && sbs.ReadU16() != (ushort)SB_PacketType.QuestsProgressRequest) { throw new Exception(); }
            var v = New();
            for (int i = 0, iMax = sbs.ReadU16(); i < iMax; i++) {
                v.Quests.Add(UserQuest.Deserialize(ref sbs, false));
            }
            return v;
        }

        public void Serialize(ref SimpleBinarySerializer sbs, bool withPacketType = true) {
            if (withPacketType) { sbs.WriteU16((ushort)SB_PacketType.QuestsProgressRequest); }
            var QuestsCount = Quests.Count;
            sbs.WriteU16((ushort)QuestsCount);
            for (var i = 0; i < QuestsCount; i++) {
                Quests[i].Serialize(ref sbs, false);
            }
        }
    }

    public struct QuestsProgressResponse {
        #if DEBUG
        [Obsolete("Use SB_PacketType.QuestsProgressResponse instead.")]
        #endif
        public const ushort SB_PacketId = (ushort)SB_PacketType.QuestsProgressResponse;
        public static QuestsProgressResponse New() {
            QuestsProgressResponse v = default;
            return v;
        }

        public void Recycle() {
        }

        public static QuestsProgressResponse Deserialize(ref SimpleBinarySerializer sbs, bool withPacketType = true) {
            if (withPacketType && sbs.ReadU16() != (ushort)SB_PacketType.QuestsProgressResponse) { throw new Exception(); }
            var v = New();
            return v;
        }

        public void Serialize(ref SimpleBinarySerializer sbs, bool withPacketType = true) {
            if (withPacketType) { sbs.WriteU16((ushort)SB_PacketType.QuestsProgressResponse); }
        }
    }

    public enum QuestRewardMultiplier : byte {
        Unknown,
        Default,
        Double,
    }

    public struct QuestRewardRequest {
        #if DEBUG
        [Obsolete("Use SB_PacketType.QuestRewardRequest instead.")]
        #endif
        public const ushort SB_PacketId = (ushort)SB_PacketType.QuestRewardRequest;
        public ushort QuestId;
        public QuestRewardMultiplier Multiplier;

        public static QuestRewardRequest New() {
            QuestRewardRequest v = default;
            return v;
        }

        public void Recycle() {
        }

        public static QuestRewardRequest Deserialize(ref SimpleBinarySerializer sbs, bool withPacketType = true) {
            if (withPacketType && sbs.ReadU16() != (ushort)SB_PacketType.QuestRewardRequest) { throw new Exception(); }
            var v = New();
            v.QuestId = sbs.ReadU16();
            v.Multiplier = (QuestRewardMultiplier)sbs.ReadU8();
            return v;
        }

        public void Serialize(ref SimpleBinarySerializer sbs, bool withPacketType = true) {
            if (withPacketType) { sbs.WriteU16((ushort)SB_PacketType.QuestRewardRequest); }
            sbs.WriteU16(QuestId);
            sbs.WriteU8((byte)Multiplier);
        }
    }

    public struct QuestsRewardResponse {
        #if DEBUG
        [Obsolete("Use SB_PacketType.QuestsRewardResponse instead.")]
        #endif
        public const ushort SB_PacketId = (ushort)SB_PacketType.QuestsRewardResponse;
        public UserQuest Quest;

        public static QuestsRewardResponse New() {
            QuestsRewardResponse v = default;
            return v;
        }

        public void Recycle() {
        }

        public static QuestsRewardResponse Deserialize(ref SimpleBinarySerializer sbs, bool withPacketType = true) {
            if (withPacketType && sbs.ReadU16() != (ushort)SB_PacketType.QuestsRewardResponse) { throw new Exception(); }
            var v = New();
            v.Quest = UserQuest.Deserialize(ref sbs, false);
            return v;
        }

        public void Serialize(ref SimpleBinarySerializer sbs, bool withPacketType = true) {
            if (withPacketType) { sbs.WriteU16((ushort)SB_PacketType.QuestsRewardResponse); }
            Quest.Serialize(ref sbs, false);
        }
    }

    public struct ServerTimeRequest {
        #if DEBUG
        [Obsolete("Use SB_PacketType.ServerTimeRequest instead.")]
        #endif
        public const ushort SB_PacketId = (ushort)SB_PacketType.ServerTimeRequest;
        public static ServerTimeRequest New() {
            ServerTimeRequest v = default;
            return v;
        }

        public void Recycle() {
        }

        public static ServerTimeRequest Deserialize(ref SimpleBinarySerializer sbs, bool withPacketType = true) {
            if (withPacketType && sbs.ReadU16() != (ushort)SB_PacketType.ServerTimeRequest) { throw new Exception(); }
            var v = New();
            return v;
        }

        public void Serialize(ref SimpleBinarySerializer sbs, bool withPacketType = true) {
            if (withPacketType) { sbs.WriteU16((ushort)SB_PacketType.ServerTimeRequest); }
        }
    }

    public struct ServerTimeResponse {
        #if DEBUG
        [Obsolete("Use SB_PacketType.ServerTimeResponse instead.")]
        #endif
        public const ushort SB_PacketId = (ushort)SB_PacketType.ServerTimeResponse;
        public string Unix;

        public static ServerTimeResponse New() {
            ServerTimeResponse v = default;
            v.Unix = "";
            return v;
        }

        public void Recycle() {
        }

        public static ServerTimeResponse Deserialize(ref SimpleBinarySerializer sbs, bool withPacketType = true) {
            if (withPacketType && sbs.ReadU16() != (ushort)SB_PacketType.ServerTimeResponse) { throw new Exception(); }
            var v = New();
            v.Unix = sbs.ReadS16();
            return v;
        }

        public void Serialize(ref SimpleBinarySerializer sbs, bool withPacketType = true) {
            if (withPacketType) { sbs.WriteU16((ushort)SB_PacketType.ServerTimeResponse); }
            sbs.WriteS16(Unix);
        }
    }

    public enum Platform : byte {
        Unknown,
        IOS,
        Android,
    }

    public struct FeedbackRequest {
        #if DEBUG
        [Obsolete("Use SB_PacketType.FeedbackRequest instead.")]
        #endif
        public const ushort SB_PacketId = (ushort)SB_PacketType.FeedbackRequest;
        public string Comment;
        public byte Rating;
        public Platform Platform;
        public string Created;

        public static FeedbackRequest New() {
            FeedbackRequest v = default;
            v.Comment = "";
            v.Created = "";
            return v;
        }

        public void Recycle() {
        }

        public static FeedbackRequest Deserialize(ref SimpleBinarySerializer sbs, bool withPacketType = true) {
            if (withPacketType && sbs.ReadU16() != (ushort)SB_PacketType.FeedbackRequest) { throw new Exception(); }
            var v = New();
            v.Comment = sbs.ReadS16();
            v.Rating = sbs.ReadU8();
            v.Platform = (Platform)sbs.ReadU8();
            v.Created = sbs.ReadS16();
            return v;
        }

        public void Serialize(ref SimpleBinarySerializer sbs, bool withPacketType = true) {
            if (withPacketType) { sbs.WriteU16((ushort)SB_PacketType.FeedbackRequest); }
            sbs.WriteS16(Comment);
            sbs.WriteU8(Rating);
            sbs.WriteU8((byte)Platform);
            sbs.WriteS16(Created);
        }
    }

    public struct FeedbackResponse {
        #if DEBUG
        [Obsolete("Use SB_PacketType.FeedbackResponse instead.")]
        #endif
        public const ushort SB_PacketId = (ushort)SB_PacketType.FeedbackResponse;
        public static FeedbackResponse New() {
            FeedbackResponse v = default;
            return v;
        }

        public void Recycle() {
        }

        public static FeedbackResponse Deserialize(ref SimpleBinarySerializer sbs, bool withPacketType = true) {
            if (withPacketType && sbs.ReadU16() != (ushort)SB_PacketType.FeedbackResponse) { throw new Exception(); }
            var v = New();
            return v;
        }

        public void Serialize(ref SimpleBinarySerializer sbs, bool withPacketType = true) {
            if (withPacketType) { sbs.WriteU16((ushort)SB_PacketType.FeedbackResponse); }
        }
    }

    public struct HealRequest {
        #if DEBUG
        [Obsolete("Use SB_PacketType.HealRequest instead.")]
        #endif
        public const ushort SB_PacketId = (ushort)SB_PacketType.HealRequest;
        public BodyPart Part;

        public static HealRequest New() {
            HealRequest v = default;
            return v;
        }

        public void Recycle() {
        }

        public static HealRequest Deserialize(ref SimpleBinarySerializer sbs, bool withPacketType = true) {
            if (withPacketType && sbs.ReadU16() != (ushort)SB_PacketType.HealRequest) { throw new Exception(); }
            var v = New();
            v.Part = (BodyPart)sbs.ReadU8();
            return v;
        }

        public void Serialize(ref SimpleBinarySerializer sbs, bool withPacketType = true) {
            if (withPacketType) { sbs.WriteU16((ushort)SB_PacketType.HealRequest); }
            sbs.WriteU8((byte)Part);
        }
    }

    public struct HealResponse {
        #if DEBUG
        [Obsolete("Use SB_PacketType.HealResponse instead.")]
        #endif
        public const ushort SB_PacketId = (ushort)SB_PacketType.HealResponse;
        public ClientTimerItem Timer;

        public static HealResponse New() {
            HealResponse v = default;
            return v;
        }

        public void Recycle() {
        }

        public static HealResponse Deserialize(ref SimpleBinarySerializer sbs, bool withPacketType = true) {
            if (withPacketType && sbs.ReadU16() != (ushort)SB_PacketType.HealResponse) { throw new Exception(); }
            var v = New();
            v.Timer = ClientTimerItem.Deserialize(ref sbs, false);
            return v;
        }

        public void Serialize(ref SimpleBinarySerializer sbs, bool withPacketType = true) {
            if (withPacketType) { sbs.WriteU16((ushort)SB_PacketType.HealResponse); }
            Timer.Serialize(ref sbs, false);
        }
    }

    public struct LootRequest {
        #if DEBUG
        [Obsolete("Use SB_PacketType.LootRequest instead.")]
        #endif
        public const ushort SB_PacketId = (ushort)SB_PacketType.LootRequest;
        public ushort LootId;

        public static LootRequest New() {
            LootRequest v = default;
            return v;
        }

        public void Recycle() {
        }

        public static LootRequest Deserialize(ref SimpleBinarySerializer sbs, bool withPacketType = true) {
            if (withPacketType && sbs.ReadU16() != (ushort)SB_PacketType.LootRequest) { throw new Exception(); }
            var v = New();
            v.LootId = sbs.ReadU16();
            return v;
        }

        public void Serialize(ref SimpleBinarySerializer sbs, bool withPacketType = true) {
            if (withPacketType) { sbs.WriteU16((ushort)SB_PacketType.LootRequest); }
            sbs.WriteU16(LootId);
        }
    }

    public struct LootResponse {
        #if DEBUG
        [Obsolete("Use SB_PacketType.LootResponse instead.")]
        #endif
        public const ushort SB_PacketId = (ushort)SB_PacketType.LootResponse;
        public List<ItemQuantity> Items;
        static ListPool<ItemQuantity> _poolOfItems = new ListPool<ItemQuantity>();

        public static LootResponse New() {
            LootResponse v = default;
            v.Items = _poolOfItems.Get();
            return v;
        }

        public void Recycle() {
            if (Items != null) {
                for (int i = 0, iMax = Items.Count; i < iMax; i++) {
                    Items[i].Recycle();
                }
                _poolOfItems.Recycle(Items);
                Items = null;
            }
        }

        public static LootResponse Deserialize(ref SimpleBinarySerializer sbs, bool withPacketType = true) {
            if (withPacketType && sbs.ReadU16() != (ushort)SB_PacketType.LootResponse) { throw new Exception(); }
            var v = New();
            for (int i = 0, iMax = sbs.ReadU16(); i < iMax; i++) {
                v.Items.Add(ItemQuantity.Deserialize(ref sbs, false));
            }
            return v;
        }

        public void Serialize(ref SimpleBinarySerializer sbs, bool withPacketType = true) {
            if (withPacketType) { sbs.WriteU16((ushort)SB_PacketType.LootResponse); }
            var ItemsCount = Items.Count;
            sbs.WriteU16((ushort)ItemsCount);
            for (var i = 0; i < ItemsCount; i++) {
                Items[i].Serialize(ref sbs, false);
            }
        }
    }

    public struct BattleProgressRequest {
        #if DEBUG
        [Obsolete("Use SB_PacketType.BattleProgressRequest instead.")]
        #endif
        public const ushort SB_PacketId = (ushort)SB_PacketType.BattleProgressRequest;
        public static BattleProgressRequest New() {
            BattleProgressRequest v = default;
            return v;
        }

        public void Recycle() {
        }

        public static BattleProgressRequest Deserialize(ref SimpleBinarySerializer sbs, bool withPacketType = true) {
            if (withPacketType && sbs.ReadU16() != (ushort)SB_PacketType.BattleProgressRequest) { throw new Exception(); }
            var v = New();
            return v;
        }

        public void Serialize(ref SimpleBinarySerializer sbs, bool withPacketType = true) {
            if (withPacketType) { sbs.WriteU16((ushort)SB_PacketType.BattleProgressRequest); }
        }
    }

    public struct BattleProgressResponse {
        #if DEBUG
        [Obsolete("Use SB_PacketType.BattleProgressResponse instead.")]
        #endif
        public const ushort SB_PacketId = (ushort)SB_PacketType.BattleProgressResponse;
        public string UserId;
        public ushort Points;
        public ushort Win;
        public ushort Lose;

        public static BattleProgressResponse New() {
            BattleProgressResponse v = default;
            v.UserId = "";
            return v;
        }

        public void Recycle() {
        }

        public static BattleProgressResponse Deserialize(ref SimpleBinarySerializer sbs, bool withPacketType = true) {
            if (withPacketType && sbs.ReadU16() != (ushort)SB_PacketType.BattleProgressResponse) { throw new Exception(); }
            var v = New();
            v.UserId = sbs.ReadS16();
            v.Points = sbs.ReadU16();
            v.Win = sbs.ReadU16();
            v.Lose = sbs.ReadU16();
            return v;
        }

        public void Serialize(ref SimpleBinarySerializer sbs, bool withPacketType = true) {
            if (withPacketType) { sbs.WriteU16((ushort)SB_PacketType.BattleProgressResponse); }
            sbs.WriteS16(UserId);
            sbs.WriteU16(Points);
            sbs.WriteU16(Win);
            sbs.WriteU16(Lose);
        }
    }

    public enum AdminProgressAction : byte {
        Unknown,
        Add,
        Remove,
    }

    public struct AdminProgressRequest {
        #if DEBUG
        [Obsolete("Use SB_PacketType.AdminProgressRequest instead.")]
        #endif
        public const ushort SB_PacketId = (ushort)SB_PacketType.AdminProgressRequest;
        public ItemQuantity Item;
        public AdminProgressAction Action;

        public static AdminProgressRequest New() {
            AdminProgressRequest v = default;
            return v;
        }

        public void Recycle() {
        }

        public static AdminProgressRequest Deserialize(ref SimpleBinarySerializer sbs, bool withPacketType = true) {
            if (withPacketType && sbs.ReadU16() != (ushort)SB_PacketType.AdminProgressRequest) { throw new Exception(); }
            var v = New();
            v.Item = ItemQuantity.Deserialize(ref sbs, false);
            v.Action = (AdminProgressAction)sbs.ReadU8();
            return v;
        }

        public void Serialize(ref SimpleBinarySerializer sbs, bool withPacketType = true) {
            if (withPacketType) { sbs.WriteU16((ushort)SB_PacketType.AdminProgressRequest); }
            Item.Serialize(ref sbs, false);
            sbs.WriteU8((byte)Action);
        }
    }

    public struct StatusResponse {
        #if DEBUG
        [Obsolete("Use SB_PacketType.StatusResponse instead.")]
        #endif
        public const ushort SB_PacketId = (ushort)SB_PacketType.StatusResponse;
        public ushort Code;
        public string Message;

        public static StatusResponse New() {
            StatusResponse v = default;
            v.Message = "";
            return v;
        }

        public void Recycle() {
        }

        public static StatusResponse Deserialize(ref SimpleBinarySerializer sbs, bool withPacketType = true) {
            if (withPacketType && sbs.ReadU16() != (ushort)SB_PacketType.StatusResponse) { throw new Exception(); }
            var v = New();
            v.Code = sbs.ReadU16();
            v.Message = sbs.ReadS16();
            return v;
        }

        public void Serialize(ref SimpleBinarySerializer sbs, bool withPacketType = true) {
            if (withPacketType) { sbs.WriteU16((ushort)SB_PacketType.StatusResponse); }
            sbs.WriteU16(Code);
            sbs.WriteS16(Message);
        }
    }

    public struct DrainBloodRequest {
        #if DEBUG
        [Obsolete("Use SB_PacketType.DrainBloodRequest instead.")]
        #endif
        public const ushort SB_PacketId = (ushort)SB_PacketType.DrainBloodRequest;
        public static DrainBloodRequest New() {
            DrainBloodRequest v = default;
            return v;
        }

        public void Recycle() {
        }

        public static DrainBloodRequest Deserialize(ref SimpleBinarySerializer sbs, bool withPacketType = true) {
            if (withPacketType && sbs.ReadU16() != (ushort)SB_PacketType.DrainBloodRequest) { throw new Exception(); }
            var v = New();
            return v;
        }

        public void Serialize(ref SimpleBinarySerializer sbs, bool withPacketType = true) {
            if (withPacketType) { sbs.WriteU16((ushort)SB_PacketType.DrainBloodRequest); }
        }
    }

    public struct DrainBloodResponse {
        #if DEBUG
        [Obsolete("Use SB_PacketType.DrainBloodResponse instead.")]
        #endif
        public const ushort SB_PacketId = (ushort)SB_PacketType.DrainBloodResponse;
        public List<int> Unlocks;
        public int BloodCurrency;
        static ListPool<int> _poolOfUnlocks = new ListPool<int>();

        public static DrainBloodResponse New() {
            DrainBloodResponse v = default;
            v.Unlocks = _poolOfUnlocks.Get();
            return v;
        }

        public void Recycle() {
            _poolOfUnlocks.Recycle(Unlocks);
        }

        public static DrainBloodResponse Deserialize(ref SimpleBinarySerializer sbs, bool withPacketType = true) {
            if (withPacketType && sbs.ReadU16() != (ushort)SB_PacketType.DrainBloodResponse) { throw new Exception(); }
            var v = New();
            for (int i = 0, iMax = sbs.ReadU16(); i < iMax; i++) {
                v.Unlocks.Add(sbs.ReadI32());
            }
            v.BloodCurrency = sbs.ReadI32();
            return v;
        }

        public void Serialize(ref SimpleBinarySerializer sbs, bool withPacketType = true) {
            if (withPacketType) { sbs.WriteU16((ushort)SB_PacketType.DrainBloodResponse); }
            var UnlocksCount = Unlocks.Count;
            sbs.WriteU16((ushort)UnlocksCount);
            for (var i = 0; i < UnlocksCount; i++) {
                sbs.WriteI32(Unlocks[i]);
            }
            sbs.WriteI32(BloodCurrency);
        }
    }

    public struct CollectBloodRequest {
        #if DEBUG
        [Obsolete("Use SB_PacketType.CollectBloodRequest instead.")]
        #endif
        public const ushort SB_PacketId = (ushort)SB_PacketType.CollectBloodRequest;
        public int ItemId;

        public static CollectBloodRequest New() {
            CollectBloodRequest v = default;
            return v;
        }

        public void Recycle() {
        }

        public static CollectBloodRequest Deserialize(ref SimpleBinarySerializer sbs, bool withPacketType = true) {
            if (withPacketType && sbs.ReadU16() != (ushort)SB_PacketType.CollectBloodRequest) { throw new Exception(); }
            var v = New();
            v.ItemId = sbs.ReadI32();
            return v;
        }

        public void Serialize(ref SimpleBinarySerializer sbs, bool withPacketType = true) {
            if (withPacketType) { sbs.WriteU16((ushort)SB_PacketType.CollectBloodRequest); }
            sbs.WriteI32(ItemId);
        }
    }

    public struct CollectBloodResponse {
        #if DEBUG
        [Obsolete("Use SB_PacketType.CollectBloodResponse instead.")]
        #endif
        public const ushort SB_PacketId = (ushort)SB_PacketType.CollectBloodResponse;
        public uint Count;

        public static CollectBloodResponse New() {
            CollectBloodResponse v = default;
            return v;
        }

        public void Recycle() {
        }

        public static CollectBloodResponse Deserialize(ref SimpleBinarySerializer sbs, bool withPacketType = true) {
            if (withPacketType && sbs.ReadU16() != (ushort)SB_PacketType.CollectBloodResponse) { throw new Exception(); }
            var v = New();
            v.Count = sbs.ReadU32();
            return v;
        }

        public void Serialize(ref SimpleBinarySerializer sbs, bool withPacketType = true) {
            if (withPacketType) { sbs.WriteU16((ushort)SB_PacketType.CollectBloodResponse); }
            sbs.WriteU32(Count);
        }
    }

    public struct StageProgressRequest {
        #if DEBUG
        [Obsolete("Use SB_PacketType.StageProgressRequest instead.")]
        #endif
        public const ushort SB_PacketId = (ushort)SB_PacketType.StageProgressRequest;
        public string Progress;

        public static StageProgressRequest New() {
            StageProgressRequest v = default;
            v.Progress = "";
            return v;
        }

        public void Recycle() {
        }

        public static StageProgressRequest Deserialize(ref SimpleBinarySerializer sbs, bool withPacketType = true) {
            if (withPacketType && sbs.ReadU16() != (ushort)SB_PacketType.StageProgressRequest) { throw new Exception(); }
            var v = New();
            v.Progress = sbs.ReadS16();
            return v;
        }

        public void Serialize(ref SimpleBinarySerializer sbs, bool withPacketType = true) {
            if (withPacketType) { sbs.WriteU16((ushort)SB_PacketType.StageProgressRequest); }
            sbs.WriteS16(Progress);
        }
    }

    public struct AdminChangeRankRequest {
        #if DEBUG
        [Obsolete("Use SB_PacketType.AdminChangeRankRequest instead.")]
        #endif
        public const ushort SB_PacketId = (ushort)SB_PacketType.AdminChangeRankRequest;
        public byte Rank;

        public static AdminChangeRankRequest New() {
            AdminChangeRankRequest v = default;
            return v;
        }

        public void Recycle() {
        }

        public static AdminChangeRankRequest Deserialize(ref SimpleBinarySerializer sbs, bool withPacketType = true) {
            if (withPacketType && sbs.ReadU16() != (ushort)SB_PacketType.AdminChangeRankRequest) { throw new Exception(); }
            var v = New();
            v.Rank = sbs.ReadU8();
            return v;
        }

        public void Serialize(ref SimpleBinarySerializer sbs, bool withPacketType = true) {
            if (withPacketType) { sbs.WriteU16((ushort)SB_PacketType.AdminChangeRankRequest); }
            sbs.WriteU8(Rank);
        }
    }

    public struct SetBloodBottleMaxSizeRequest {
        #if DEBUG
        [Obsolete("Use SB_PacketType.SetBloodBottleMaxSizeRequest instead.")]
        #endif
        public const ushort SB_PacketId = (ushort)SB_PacketType.SetBloodBottleMaxSizeRequest;
        public ushort MaxSize;

        public static SetBloodBottleMaxSizeRequest New() {
            SetBloodBottleMaxSizeRequest v = default;
            return v;
        }

        public void Recycle() {
        }

        public static SetBloodBottleMaxSizeRequest Deserialize(ref SimpleBinarySerializer sbs, bool withPacketType = true) {
            if (withPacketType && sbs.ReadU16() != (ushort)SB_PacketType.SetBloodBottleMaxSizeRequest) { throw new Exception(); }
            var v = New();
            v.MaxSize = sbs.ReadU16();
            return v;
        }

        public void Serialize(ref SimpleBinarySerializer sbs, bool withPacketType = true) {
            if (withPacketType) { sbs.WriteU16((ushort)SB_PacketType.SetBloodBottleMaxSizeRequest); }
            sbs.WriteU16(MaxSize);
        }
    }

    public struct ResetBloodBottleMaxSizeRequest {
        #if DEBUG
        [Obsolete("Use SB_PacketType.ResetBloodBottleMaxSizeRequest instead.")]
        #endif
        public const ushort SB_PacketId = (ushort)SB_PacketType.ResetBloodBottleMaxSizeRequest;
        public static ResetBloodBottleMaxSizeRequest New() {
            ResetBloodBottleMaxSizeRequest v = default;
            return v;
        }

        public void Recycle() {
        }

        public static ResetBloodBottleMaxSizeRequest Deserialize(ref SimpleBinarySerializer sbs, bool withPacketType = true) {
            if (withPacketType && sbs.ReadU16() != (ushort)SB_PacketType.ResetBloodBottleMaxSizeRequest) { throw new Exception(); }
            var v = New();
            return v;
        }

        public void Serialize(ref SimpleBinarySerializer sbs, bool withPacketType = true) {
            if (withPacketType) { sbs.WriteU16((ushort)SB_PacketType.ResetBloodBottleMaxSizeRequest); }
        }
    }

    public struct SetStartingRankRequest {
        #if DEBUG
        [Obsolete("Use SB_PacketType.SetStartingRankRequest instead.")]
        #endif
        public const ushort SB_PacketId = (ushort)SB_PacketType.SetStartingRankRequest;
        public static SetStartingRankRequest New() {
            SetStartingRankRequest v = default;
            return v;
        }

        public void Recycle() {
        }

        public static SetStartingRankRequest Deserialize(ref SimpleBinarySerializer sbs, bool withPacketType = true) {
            if (withPacketType && sbs.ReadU16() != (ushort)SB_PacketType.SetStartingRankRequest) { throw new Exception(); }
            var v = New();
            return v;
        }

        public void Serialize(ref SimpleBinarySerializer sbs, bool withPacketType = true) {
            if (withPacketType) { sbs.WriteU16((ushort)SB_PacketType.SetStartingRankRequest); }
        }
    }

    public struct SetStartingRankResponse {
        #if DEBUG
        [Obsolete("Use SB_PacketType.SetStartingRankResponse instead.")]
        #endif
        public const ushort SB_PacketId = (ushort)SB_PacketType.SetStartingRankResponse;
        public BooleanType IsSetStartingRank;
        public StatusResponse Status;

        public static SetStartingRankResponse New() {
            SetStartingRankResponse v = default;
            return v;
        }

        public void Recycle() {
        }

        public static SetStartingRankResponse Deserialize(ref SimpleBinarySerializer sbs, bool withPacketType = true) {
            if (withPacketType && sbs.ReadU16() != (ushort)SB_PacketType.SetStartingRankResponse) { throw new Exception(); }
            var v = New();
            v.IsSetStartingRank = (BooleanType)sbs.ReadU8();
            v.Status = StatusResponse.Deserialize(ref sbs, false);
            return v;
        }

        public void Serialize(ref SimpleBinarySerializer sbs, bool withPacketType = true) {
            if (withPacketType) { sbs.WriteU16((ushort)SB_PacketType.SetStartingRankResponse); }
            sbs.WriteU8((byte)IsSetStartingRank);
            Status.Serialize(ref sbs, false);
        }
    }

    public struct ClientCraftedItemsRequest {
        #if DEBUG
        [Obsolete("Use SB_PacketType.ClientCraftedItemsRequest instead.")]
        #endif
        public const ushort SB_PacketId = (ushort)SB_PacketType.ClientCraftedItemsRequest;
        public static ClientCraftedItemsRequest New() {
            ClientCraftedItemsRequest v = default;
            return v;
        }

        public void Recycle() {
        }

        public static ClientCraftedItemsRequest Deserialize(ref SimpleBinarySerializer sbs, bool withPacketType = true) {
            if (withPacketType && sbs.ReadU16() != (ushort)SB_PacketType.ClientCraftedItemsRequest) { throw new Exception(); }
            var v = New();
            return v;
        }

        public void Serialize(ref SimpleBinarySerializer sbs, bool withPacketType = true) {
            if (withPacketType) { sbs.WriteU16((ushort)SB_PacketType.ClientCraftedItemsRequest); }
        }
    }

    public struct ClientCraftedItemsResponse {
        #if DEBUG
        [Obsolete("Use SB_PacketType.ClientCraftedItemsResponse instead.")]
        #endif
        public const ushort SB_PacketId = (ushort)SB_PacketType.ClientCraftedItemsResponse;
        public List<ItemQuantity> Items;
        static ListPool<ItemQuantity> _poolOfItems = new ListPool<ItemQuantity>();

        public static ClientCraftedItemsResponse New() {
            ClientCraftedItemsResponse v = default;
            v.Items = _poolOfItems.Get();
            return v;
        }

        public void Recycle() {
            if (Items != null) {
                for (int i = 0, iMax = Items.Count; i < iMax; i++) {
                    Items[i].Recycle();
                }
                _poolOfItems.Recycle(Items);
                Items = null;
            }
        }

        public static ClientCraftedItemsResponse Deserialize(ref SimpleBinarySerializer sbs, bool withPacketType = true) {
            if (withPacketType && sbs.ReadU16() != (ushort)SB_PacketType.ClientCraftedItemsResponse) { throw new Exception(); }
            var v = New();
            for (int i = 0, iMax = sbs.ReadU16(); i < iMax; i++) {
                v.Items.Add(ItemQuantity.Deserialize(ref sbs, false));
            }
            return v;
        }

        public void Serialize(ref SimpleBinarySerializer sbs, bool withPacketType = true) {
            if (withPacketType) { sbs.WriteU16((ushort)SB_PacketType.ClientCraftedItemsResponse); }
            var ItemsCount = Items.Count;
            sbs.WriteU16((ushort)ItemsCount);
            for (var i = 0; i < ItemsCount; i++) {
                Items[i].Serialize(ref sbs, false);
            }
        }
    }

    public struct ClientTakeCraftedItemRequest {
        #if DEBUG
        [Obsolete("Use SB_PacketType.ClientTakeCraftedItemRequest instead.")]
        #endif
        public const ushort SB_PacketId = (ushort)SB_PacketType.ClientTakeCraftedItemRequest;
        public int ItemId;

        public static ClientTakeCraftedItemRequest New() {
            ClientTakeCraftedItemRequest v = default;
            return v;
        }

        public void Recycle() {
        }

        public static ClientTakeCraftedItemRequest Deserialize(ref SimpleBinarySerializer sbs, bool withPacketType = true) {
            if (withPacketType && sbs.ReadU16() != (ushort)SB_PacketType.ClientTakeCraftedItemRequest) { throw new Exception(); }
            var v = New();
            v.ItemId = sbs.ReadI32();
            return v;
        }

        public void Serialize(ref SimpleBinarySerializer sbs, bool withPacketType = true) {
            if (withPacketType) { sbs.WriteU16((ushort)SB_PacketType.ClientTakeCraftedItemRequest); }
            sbs.WriteI32(ItemId);
        }
    }

    public struct ClientTakeCraftedItemResponse {
        #if DEBUG
        [Obsolete("Use SB_PacketType.ClientTakeCraftedItemResponse instead.")]
        #endif
        public const ushort SB_PacketId = (ushort)SB_PacketType.ClientTakeCraftedItemResponse;
        public static ClientTakeCraftedItemResponse New() {
            ClientTakeCraftedItemResponse v = default;
            return v;
        }

        public void Recycle() {
        }

        public static ClientTakeCraftedItemResponse Deserialize(ref SimpleBinarySerializer sbs, bool withPacketType = true) {
            if (withPacketType && sbs.ReadU16() != (ushort)SB_PacketType.ClientTakeCraftedItemResponse) { throw new Exception(); }
            var v = New();
            return v;
        }

        public void Serialize(ref SimpleBinarySerializer sbs, bool withPacketType = true) {
            if (withPacketType) { sbs.WriteU16((ushort)SB_PacketType.ClientTakeCraftedItemResponse); }
        }
    }

    public struct OpenMultiLootBoxRequest {
        #if DEBUG
        [Obsolete("Use SB_PacketType.OpenMultiLootBoxRequest instead.")]
        #endif
        public const ushort SB_PacketId = (ushort)SB_PacketType.OpenMultiLootBoxRequest;
        public ushort Item;

        public static OpenMultiLootBoxRequest New() {
            OpenMultiLootBoxRequest v = default;
            return v;
        }

        public void Recycle() {
        }

        public static OpenMultiLootBoxRequest Deserialize(ref SimpleBinarySerializer sbs, bool withPacketType = true) {
            if (withPacketType && sbs.ReadU16() != (ushort)SB_PacketType.OpenMultiLootBoxRequest) { throw new Exception(); }
            var v = New();
            v.Item = sbs.ReadU16();
            return v;
        }

        public void Serialize(ref SimpleBinarySerializer sbs, bool withPacketType = true) {
            if (withPacketType) { sbs.WriteU16((ushort)SB_PacketType.OpenMultiLootBoxRequest); }
            sbs.WriteU16(Item);
        }
    }

    public struct OpenMultiLootBoxResponse {
        #if DEBUG
        [Obsolete("Use SB_PacketType.OpenMultiLootBoxResponse instead.")]
        #endif
        public const ushort SB_PacketId = (ushort)SB_PacketType.OpenMultiLootBoxResponse;
        public List<ItemQuantity> Items;
        public StatusResponse Status;
        static ListPool<ItemQuantity> _poolOfItems = new ListPool<ItemQuantity>();

        public static OpenMultiLootBoxResponse New() {
            OpenMultiLootBoxResponse v = default;
            v.Items = _poolOfItems.Get();
            return v;
        }

        public void Recycle() {
            if (Items != null) {
                for (int i = 0, iMax = Items.Count; i < iMax; i++) {
                    Items[i].Recycle();
                }
                _poolOfItems.Recycle(Items);
                Items = null;
            }
        }

        public static OpenMultiLootBoxResponse Deserialize(ref SimpleBinarySerializer sbs, bool withPacketType = true) {
            if (withPacketType && sbs.ReadU16() != (ushort)SB_PacketType.OpenMultiLootBoxResponse) { throw new Exception(); }
            var v = New();
            for (int i = 0, iMax = sbs.ReadU16(); i < iMax; i++) {
                v.Items.Add(ItemQuantity.Deserialize(ref sbs, false));
            }
            v.Status = StatusResponse.Deserialize(ref sbs, false);
            return v;
        }

        public void Serialize(ref SimpleBinarySerializer sbs, bool withPacketType = true) {
            if (withPacketType) { sbs.WriteU16((ushort)SB_PacketType.OpenMultiLootBoxResponse); }
            var ItemsCount = Items.Count;
            sbs.WriteU16((ushort)ItemsCount);
            for (var i = 0; i < ItemsCount; i++) {
                Items[i].Serialize(ref sbs, false);
            }
            Status.Serialize(ref sbs, false);
        }
    }

    public struct ChangeTimerRequest {
        #if DEBUG
        [Obsolete("Use SB_PacketType.ChangeTimerRequest instead.")]
        #endif
        public const ushort SB_PacketId = (ushort)SB_PacketType.ChangeTimerRequest;
        public int Item;
        public TimerType Type;
        public int Diff;

        public static ChangeTimerRequest New() {
            ChangeTimerRequest v = default;
            return v;
        }

        public void Recycle() {
        }

        public static ChangeTimerRequest Deserialize(ref SimpleBinarySerializer sbs, bool withPacketType = true) {
            if (withPacketType && sbs.ReadU16() != (ushort)SB_PacketType.ChangeTimerRequest) { throw new Exception(); }
            var v = New();
            v.Item = sbs.ReadI32();
            v.Type = (TimerType)sbs.ReadU8();
            v.Diff = sbs.ReadI32();
            return v;
        }

        public void Serialize(ref SimpleBinarySerializer sbs, bool withPacketType = true) {
            if (withPacketType) { sbs.WriteU16((ushort)SB_PacketType.ChangeTimerRequest); }
            sbs.WriteI32(Item);
            sbs.WriteU8((byte)Type);
            sbs.WriteI32(Diff);
        }
    }

    public struct ChangeTimerResponse {
        #if DEBUG
        [Obsolete("Use SB_PacketType.ChangeTimerResponse instead.")]
        #endif
        public const ushort SB_PacketId = (ushort)SB_PacketType.ChangeTimerResponse;
        public ClientTimerItem Timer;
        public StatusResponse Status;

        public static ChangeTimerResponse New() {
            ChangeTimerResponse v = default;
            return v;
        }

        public void Recycle() {
        }

        public static ChangeTimerResponse Deserialize(ref SimpleBinarySerializer sbs, bool withPacketType = true) {
            if (withPacketType && sbs.ReadU16() != (ushort)SB_PacketType.ChangeTimerResponse) { throw new Exception(); }
            var v = New();
            v.Timer = ClientTimerItem.Deserialize(ref sbs, false);
            v.Status = StatusResponse.Deserialize(ref sbs, false);
            return v;
        }

        public void Serialize(ref SimpleBinarySerializer sbs, bool withPacketType = true) {
            if (withPacketType) { sbs.WriteU16((ushort)SB_PacketType.ChangeTimerResponse); }
            Timer.Serialize(ref sbs, false);
            Status.Serialize(ref sbs, false);
        }
    }

    public struct NotificationSettings {
        #if DEBUG
        [Obsolete("Use SB_PacketType.NotificationSettings instead.")]
        #endif
        public const ushort SB_PacketId = (ushort)SB_PacketType.NotificationSettings;
        public string NotificationType;
        public byte Enabled;

        public static NotificationSettings New() {
            NotificationSettings v = default;
            v.NotificationType = "";
            return v;
        }

        public void Recycle() {
        }

        public static NotificationSettings Deserialize(ref SimpleBinarySerializer sbs, bool withPacketType = true) {
            if (withPacketType && sbs.ReadU16() != (ushort)SB_PacketType.NotificationSettings) { throw new Exception(); }
            var v = New();
            v.NotificationType = sbs.ReadS16();
            v.Enabled = sbs.ReadU8();
            return v;
        }

        public void Serialize(ref SimpleBinarySerializer sbs, bool withPacketType = true) {
            if (withPacketType) { sbs.WriteU16((ushort)SB_PacketType.NotificationSettings); }
            sbs.WriteS16(NotificationType);
            sbs.WriteU8(Enabled);
        }
    }

    public struct ChangeSettingsRequest {
        #if DEBUG
        [Obsolete("Use SB_PacketType.ChangeSettingsRequest instead.")]
        #endif
        public const ushort SB_PacketId = (ushort)SB_PacketType.ChangeSettingsRequest;
        public byte Sound;
        public byte Music;
        public byte Vibration;
        public string Language;
        public string Country;
        public byte RightHandedControl;
        public List<NotificationSettings> Notifications;
        static ListPool<NotificationSettings> _poolOfNotifications = new ListPool<NotificationSettings>();

        public static ChangeSettingsRequest New() {
            ChangeSettingsRequest v = default;
            v.Language = "";
            v.Country = "";
            v.Notifications = _poolOfNotifications.Get();
            return v;
        }

        public void Recycle() {
            if (Notifications != null) {
                for (int i = 0, iMax = Notifications.Count; i < iMax; i++) {
                    Notifications[i].Recycle();
                }
                _poolOfNotifications.Recycle(Notifications);
                Notifications = null;
            }
        }

        public static ChangeSettingsRequest Deserialize(ref SimpleBinarySerializer sbs, bool withPacketType = true) {
            if (withPacketType && sbs.ReadU16() != (ushort)SB_PacketType.ChangeSettingsRequest) { throw new Exception(); }
            var v = New();
            v.Sound = sbs.ReadU8();
            v.Music = sbs.ReadU8();
            v.Vibration = sbs.ReadU8();
            v.Language = sbs.ReadS16();
            v.Country = sbs.ReadS16();
            v.RightHandedControl = sbs.ReadU8();
            for (int i = 0, iMax = sbs.ReadU16(); i < iMax; i++) {
                v.Notifications.Add(NotificationSettings.Deserialize(ref sbs, false));
            }
            return v;
        }

        public void Serialize(ref SimpleBinarySerializer sbs, bool withPacketType = true) {
            if (withPacketType) { sbs.WriteU16((ushort)SB_PacketType.ChangeSettingsRequest); }
            sbs.WriteU8(Sound);
            sbs.WriteU8(Music);
            sbs.WriteU8(Vibration);
            sbs.WriteS16(Language);
            sbs.WriteS16(Country);
            sbs.WriteU8(RightHandedControl);
            var NotificationsCount = Notifications.Count;
            sbs.WriteU16((ushort)NotificationsCount);
            for (var i = 0; i < NotificationsCount; i++) {
                Notifications[i].Serialize(ref sbs, false);
            }
        }
    }

    public struct ChangeSettingsResponse {
        #if DEBUG
        [Obsolete("Use SB_PacketType.ChangeSettingsResponse instead.")]
        #endif
        public const ushort SB_PacketId = (ushort)SB_PacketType.ChangeSettingsResponse;
        public StatusResponse Status;

        public static ChangeSettingsResponse New() {
            ChangeSettingsResponse v = default;
            return v;
        }

        public void Recycle() {
        }

        public static ChangeSettingsResponse Deserialize(ref SimpleBinarySerializer sbs, bool withPacketType = true) {
            if (withPacketType && sbs.ReadU16() != (ushort)SB_PacketType.ChangeSettingsResponse) { throw new Exception(); }
            var v = New();
            v.Status = StatusResponse.Deserialize(ref sbs, false);
            return v;
        }

        public void Serialize(ref SimpleBinarySerializer sbs, bool withPacketType = true) {
            if (withPacketType) { sbs.WriteU16((ushort)SB_PacketType.ChangeSettingsResponse); }
            Status.Serialize(ref sbs, false);
        }
    }

    public struct BloodChunkRequest {
        #if DEBUG
        [Obsolete("Use SB_PacketType.BloodChunkRequest instead.")]
        #endif
        public const ushort SB_PacketId = (ushort)SB_PacketType.BloodChunkRequest;
        public static BloodChunkRequest New() {
            BloodChunkRequest v = default;
            return v;
        }

        public void Recycle() {
        }

        public static BloodChunkRequest Deserialize(ref SimpleBinarySerializer sbs, bool withPacketType = true) {
            if (withPacketType && sbs.ReadU16() != (ushort)SB_PacketType.BloodChunkRequest) { throw new Exception(); }
            var v = New();
            return v;
        }

        public void Serialize(ref SimpleBinarySerializer sbs, bool withPacketType = true) {
            if (withPacketType) { sbs.WriteU16((ushort)SB_PacketType.BloodChunkRequest); }
        }
    }

    public struct BloodChunkResponse {
        #if DEBUG
        [Obsolete("Use SB_PacketType.BloodChunkResponse instead.")]
        #endif
        public const ushort SB_PacketId = (ushort)SB_PacketType.BloodChunkResponse;
        public ushort Count;

        public static BloodChunkResponse New() {
            BloodChunkResponse v = default;
            return v;
        }

        public void Recycle() {
        }

        public static BloodChunkResponse Deserialize(ref SimpleBinarySerializer sbs, bool withPacketType = true) {
            if (withPacketType && sbs.ReadU16() != (ushort)SB_PacketType.BloodChunkResponse) { throw new Exception(); }
            var v = New();
            v.Count = sbs.ReadU16();
            return v;
        }

        public void Serialize(ref SimpleBinarySerializer sbs, bool withPacketType = true) {
            if (withPacketType) { sbs.WriteU16((ushort)SB_PacketType.BloodChunkResponse); }
            sbs.WriteU16(Count);
        }
    }

    public struct SkinDurability {
        #if DEBUG
        [Obsolete("Use SB_PacketType.SkinDurability instead.")]
        #endif
        public const ushort SB_PacketId = (ushort)SB_PacketType.SkinDurability;
        public int Id;
        public uint Durability;

        public static SkinDurability New() {
            SkinDurability v = default;
            return v;
        }

        public void Recycle() {
        }

        public static SkinDurability Deserialize(ref SimpleBinarySerializer sbs, bool withPacketType = true) {
            if (withPacketType && sbs.ReadU16() != (ushort)SB_PacketType.SkinDurability) { throw new Exception(); }
            var v = New();
            v.Id = sbs.ReadI32();
            v.Durability = sbs.ReadU32();
            return v;
        }

        public void Serialize(ref SimpleBinarySerializer sbs, bool withPacketType = true) {
            if (withPacketType) { sbs.WriteU16((ushort)SB_PacketType.SkinDurability); }
            sbs.WriteI32(Id);
            sbs.WriteU32(Durability);
        }
    }

    public struct AdminSkinDurabilitiesRequest {
        #if DEBUG
        [Obsolete("Use SB_PacketType.AdminSkinDurabilitiesRequest instead.")]
        #endif
        public const ushort SB_PacketId = (ushort)SB_PacketType.AdminSkinDurabilitiesRequest;
        public List<SkinDurability> SkinDurabilities;
        static ListPool<SkinDurability> _poolOfSkinDurabilities = new ListPool<SkinDurability>();

        public static AdminSkinDurabilitiesRequest New() {
            AdminSkinDurabilitiesRequest v = default;
            v.SkinDurabilities = _poolOfSkinDurabilities.Get();
            return v;
        }

        public void Recycle() {
            if (SkinDurabilities != null) {
                for (int i = 0, iMax = SkinDurabilities.Count; i < iMax; i++) {
                    SkinDurabilities[i].Recycle();
                }
                _poolOfSkinDurabilities.Recycle(SkinDurabilities);
                SkinDurabilities = null;
            }
        }

        public static AdminSkinDurabilitiesRequest Deserialize(ref SimpleBinarySerializer sbs, bool withPacketType = true) {
            if (withPacketType && sbs.ReadU16() != (ushort)SB_PacketType.AdminSkinDurabilitiesRequest) { throw new Exception(); }
            var v = New();
            for (int i = 0, iMax = sbs.ReadU16(); i < iMax; i++) {
                v.SkinDurabilities.Add(SkinDurability.Deserialize(ref sbs, false));
            }
            return v;
        }

        public void Serialize(ref SimpleBinarySerializer sbs, bool withPacketType = true) {
            if (withPacketType) { sbs.WriteU16((ushort)SB_PacketType.AdminSkinDurabilitiesRequest); }
            var SkinDurabilitiesCount = SkinDurabilities.Count;
            sbs.WriteU16((ushort)SkinDurabilitiesCount);
            for (var i = 0; i < SkinDurabilitiesCount; i++) {
                SkinDurabilities[i].Serialize(ref sbs, false);
            }
        }
    }

    public struct ClientStartFacebookRequest {
        #if DEBUG
        [Obsolete("Use SB_PacketType.ClientStartFacebookRequest instead.")]
        #endif
        public const ushort SB_PacketId = (ushort)SB_PacketType.ClientStartFacebookRequest;
        public static ClientStartFacebookRequest New() {
            ClientStartFacebookRequest v = default;
            return v;
        }

        public void Recycle() {
        }

        public static ClientStartFacebookRequest Deserialize(ref SimpleBinarySerializer sbs, bool withPacketType = true) {
            if (withPacketType && sbs.ReadU16() != (ushort)SB_PacketType.ClientStartFacebookRequest) { throw new Exception(); }
            var v = New();
            return v;
        }

        public void Serialize(ref SimpleBinarySerializer sbs, bool withPacketType = true) {
            if (withPacketType) { sbs.WriteU16((ushort)SB_PacketType.ClientStartFacebookRequest); }
        }
    }

    public struct ClientStartFacebookResponse {
        #if DEBUG
        [Obsolete("Use SB_PacketType.ClientStartFacebookResponse instead.")]
        #endif
        public const ushort SB_PacketId = (ushort)SB_PacketType.ClientStartFacebookResponse;
        public static ClientStartFacebookResponse New() {
            ClientStartFacebookResponse v = default;
            return v;
        }

        public void Recycle() {
        }

        public static ClientStartFacebookResponse Deserialize(ref SimpleBinarySerializer sbs, bool withPacketType = true) {
            if (withPacketType && sbs.ReadU16() != (ushort)SB_PacketType.ClientStartFacebookResponse) { throw new Exception(); }
            var v = New();
            return v;
        }

        public void Serialize(ref SimpleBinarySerializer sbs, bool withPacketType = true) {
            if (withPacketType) { sbs.WriteU16((ushort)SB_PacketType.ClientStartFacebookResponse); }
        }
    }
}