using System.Collections.Generic;
using Client.Common.Analytics.Infrastructure.Events;
using Client.Common.Analytics.Trackers.Abstract;
using Client.Common.Configs;
using Client.Common.Configs.Components;
using Client.Common.Network.ExternalEvents.EventReceivers;
using Client.Utils.ECS.LocalWorld;
using External;
using Leopotam.Ecs;

namespace Client.Common.Network.ExternalEvents.Analytics
{
    internal class BloodRewardAnalyticTracker : IEventAnalyticTracker
    {
        private readonly EcsWorld _world;
        private readonly ConfigService _configService;
        private readonly IExternalEventReceiver<ChangeBloodInfo> _progressUpdateEvent;

        public BloodRewardAnalyticTracker(
            EcsWorld world,
            ConfigService configService,
            IExternalEventReceiver<ChangeBloodInfo> progressUpdateEvent)
        {
            _world = world;
            _configService = configService;
            _progressUpdateEvent = progressUpdateEvent;

            _progressUpdateEvent.Received += TrackEvent;
        }

        private void TrackEvent(ChangeBloodInfo info)
        {
            uint newStep = info.BloodStep;
            string rewardKey = _configService.GetBloodReward(newStep).Get<ConfigComponent>().Key;
            FireEvent(rewardKey);
        }

        private void FireEvent(string reward)
        {
            EcsEntity analyticsEvent = _world.NewEntity();
            analyticsEvent.Get<AnalyticsCustomEvent>().Id = "bloody_altar_unlock";

            analyticsEvent.Get<AnalyticsParameters>().Parameters = new Dictionary<string, string>()
            {
                ["Item"] = reward
            };
        }

        public void Dispose()
        {
            _progressUpdateEvent.Received -= TrackEvent;
        }
    }
}