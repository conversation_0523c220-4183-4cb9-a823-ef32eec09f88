// <auto-generated>
//     Generated by the protocol buffer compiler.  DO NOT EDIT!
//     source: external/external-set-weapon-skins.proto
// </auto-generated>
#pragma warning disable 1591, 0612, 3021, 8981
#region Designer generated code

using pb = global::Google.Protobuf;
using pbc = global::Google.Protobuf.Collections;
using pbr = global::Google.Protobuf.Reflection;
using scg = global::System.Collections.Generic;
namespace External {

  /// <summary>Holder for reflection information generated from external/external-set-weapon-skins.proto</summary>
  public static partial class ExternalSetWeaponSkinsReflection {

    #region Descriptor
    /// <summary>File descriptor for external/external-set-weapon-skins.proto</summary>
    public static pbr::FileDescriptor Descriptor {
      get { return descriptor; }
    }
    private static pbr::FileDescriptor descriptor;

    static ExternalSetWeaponSkinsReflection() {
      byte[] descriptorData = global::System.Convert.FromBase64String(
          string.Concat(
            "CihleHRlcm5hbC9leHRlcm5hbC1zZXQtd2VhcG9uLXNraW5zLnByb3RvEghl",
            "eHRlcm5hbCJECgpXZWFwb25Ta2luEhEKCXdlYXBvbl9pZBgBIAEoAxIPCgdz",
            "a2luX2lkGAIgASgDEhIKCmR1cmFiaWxpdHkYAyABKAMiSwodRXh0ZXJuYWxT",
            "ZXRXZWFwb25Ta2luc1JlcXVlc3QSKgoMd2VhcG9uX3NraW5zGAEgAygLMhQu",
            "ZXh0ZXJuYWwuV2VhcG9uU2tpbmIGcHJvdG8z"));
      descriptor = pbr::FileDescriptor.FromGeneratedCode(descriptorData,
          new pbr::FileDescriptor[] { },
          new pbr::GeneratedClrTypeInfo(null, null, new pbr::GeneratedClrTypeInfo[] {
            new pbr::GeneratedClrTypeInfo(typeof(global::External.WeaponSkin), global::External.WeaponSkin.Parser, new[]{ "WeaponId", "SkinId", "Durability" }, null, null, null, null),
            new pbr::GeneratedClrTypeInfo(typeof(global::External.ExternalSetWeaponSkinsRequest), global::External.ExternalSetWeaponSkinsRequest.Parser, new[]{ "WeaponSkins" }, null, null, null, null)
          }));
    }
    #endregion

  }
  #region Messages
  [global::System.Diagnostics.DebuggerDisplayAttribute("{ToString(),nq}")]
  public sealed partial class WeaponSkin : pb::IMessage<WeaponSkin>
  #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      , pb::IBufferMessage
  #endif
  {
    private static readonly pb::MessageParser<WeaponSkin> _parser = new pb::MessageParser<WeaponSkin>(() => new WeaponSkin());
    private pb::UnknownFieldSet _unknownFields;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public static pb::MessageParser<WeaponSkin> Parser { get { return _parser; } }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public static pbr::MessageDescriptor Descriptor {
      get { return global::External.ExternalSetWeaponSkinsReflection.Descriptor.MessageTypes[0]; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    pbr::MessageDescriptor pb::IMessage.Descriptor {
      get { return Descriptor; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public WeaponSkin() {
      OnConstruction();
    }

    partial void OnConstruction();

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public WeaponSkin(WeaponSkin other) : this() {
      weaponId_ = other.weaponId_;
      skinId_ = other.skinId_;
      durability_ = other.durability_;
      _unknownFields = pb::UnknownFieldSet.Clone(other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public WeaponSkin Clone() {
      return new WeaponSkin(this);
    }

    /// <summary>Field number for the "weapon_id" field.</summary>
    public const int WeaponIdFieldNumber = 1;
    private long weaponId_;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public long WeaponId {
      get { return weaponId_; }
      set {
        weaponId_ = value;
      }
    }

    /// <summary>Field number for the "skin_id" field.</summary>
    public const int SkinIdFieldNumber = 2;
    private long skinId_;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public long SkinId {
      get { return skinId_; }
      set {
        skinId_ = value;
      }
    }

    /// <summary>Field number for the "durability" field.</summary>
    public const int DurabilityFieldNumber = 3;
    private long durability_;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public long Durability {
      get { return durability_; }
      set {
        durability_ = value;
      }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override bool Equals(object other) {
      return Equals(other as WeaponSkin);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public bool Equals(WeaponSkin other) {
      if (ReferenceEquals(other, null)) {
        return false;
      }
      if (ReferenceEquals(other, this)) {
        return true;
      }
      if (WeaponId != other.WeaponId) return false;
      if (SkinId != other.SkinId) return false;
      if (Durability != other.Durability) return false;
      return Equals(_unknownFields, other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override int GetHashCode() {
      int hash = 1;
      if (WeaponId != 0L) hash ^= WeaponId.GetHashCode();
      if (SkinId != 0L) hash ^= SkinId.GetHashCode();
      if (Durability != 0L) hash ^= Durability.GetHashCode();
      if (_unknownFields != null) {
        hash ^= _unknownFields.GetHashCode();
      }
      return hash;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override string ToString() {
      return pb::JsonFormatter.ToDiagnosticString(this);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void WriteTo(pb::CodedOutputStream output) {
    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      output.WriteRawMessage(this);
    #else
      if (WeaponId != 0L) {
        output.WriteRawTag(8);
        output.WriteInt64(WeaponId);
      }
      if (SkinId != 0L) {
        output.WriteRawTag(16);
        output.WriteInt64(SkinId);
      }
      if (Durability != 0L) {
        output.WriteRawTag(24);
        output.WriteInt64(Durability);
      }
      if (_unknownFields != null) {
        _unknownFields.WriteTo(output);
      }
    #endif
    }

    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    void pb::IBufferMessage.InternalWriteTo(ref pb::WriteContext output) {
      if (WeaponId != 0L) {
        output.WriteRawTag(8);
        output.WriteInt64(WeaponId);
      }
      if (SkinId != 0L) {
        output.WriteRawTag(16);
        output.WriteInt64(SkinId);
      }
      if (Durability != 0L) {
        output.WriteRawTag(24);
        output.WriteInt64(Durability);
      }
      if (_unknownFields != null) {
        _unknownFields.WriteTo(ref output);
      }
    }
    #endif

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public int CalculateSize() {
      int size = 0;
      if (WeaponId != 0L) {
        size += 1 + pb::CodedOutputStream.ComputeInt64Size(WeaponId);
      }
      if (SkinId != 0L) {
        size += 1 + pb::CodedOutputStream.ComputeInt64Size(SkinId);
      }
      if (Durability != 0L) {
        size += 1 + pb::CodedOutputStream.ComputeInt64Size(Durability);
      }
      if (_unknownFields != null) {
        size += _unknownFields.CalculateSize();
      }
      return size;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void MergeFrom(WeaponSkin other) {
      if (other == null) {
        return;
      }
      if (other.WeaponId != 0L) {
        WeaponId = other.WeaponId;
      }
      if (other.SkinId != 0L) {
        SkinId = other.SkinId;
      }
      if (other.Durability != 0L) {
        Durability = other.Durability;
      }
      _unknownFields = pb::UnknownFieldSet.MergeFrom(_unknownFields, other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void MergeFrom(pb::CodedInputStream input) {
    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      input.ReadRawMessage(this);
    #else
      uint tag;
      while ((tag = input.ReadTag()) != 0) {
        switch(tag) {
          default:
            _unknownFields = pb::UnknownFieldSet.MergeFieldFrom(_unknownFields, input);
            break;
          case 8: {
            WeaponId = input.ReadInt64();
            break;
          }
          case 16: {
            SkinId = input.ReadInt64();
            break;
          }
          case 24: {
            Durability = input.ReadInt64();
            break;
          }
        }
      }
    #endif
    }

    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    void pb::IBufferMessage.InternalMergeFrom(ref pb::ParseContext input) {
      uint tag;
      while ((tag = input.ReadTag()) != 0) {
        switch(tag) {
          default:
            _unknownFields = pb::UnknownFieldSet.MergeFieldFrom(_unknownFields, ref input);
            break;
          case 8: {
            WeaponId = input.ReadInt64();
            break;
          }
          case 16: {
            SkinId = input.ReadInt64();
            break;
          }
          case 24: {
            Durability = input.ReadInt64();
            break;
          }
        }
      }
    }
    #endif

  }

  [global::System.Diagnostics.DebuggerDisplayAttribute("{ToString(),nq}")]
  public sealed partial class ExternalSetWeaponSkinsRequest : pb::IMessage<ExternalSetWeaponSkinsRequest>
  #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      , pb::IBufferMessage
  #endif
  {
    private static readonly pb::MessageParser<ExternalSetWeaponSkinsRequest> _parser = new pb::MessageParser<ExternalSetWeaponSkinsRequest>(() => new ExternalSetWeaponSkinsRequest());
    private pb::UnknownFieldSet _unknownFields;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public static pb::MessageParser<ExternalSetWeaponSkinsRequest> Parser { get { return _parser; } }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public static pbr::MessageDescriptor Descriptor {
      get { return global::External.ExternalSetWeaponSkinsReflection.Descriptor.MessageTypes[1]; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    pbr::MessageDescriptor pb::IMessage.Descriptor {
      get { return Descriptor; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public ExternalSetWeaponSkinsRequest() {
      OnConstruction();
    }

    partial void OnConstruction();

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public ExternalSetWeaponSkinsRequest(ExternalSetWeaponSkinsRequest other) : this() {
      weaponSkins_ = other.weaponSkins_.Clone();
      _unknownFields = pb::UnknownFieldSet.Clone(other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public ExternalSetWeaponSkinsRequest Clone() {
      return new ExternalSetWeaponSkinsRequest(this);
    }

    /// <summary>Field number for the "weapon_skins" field.</summary>
    public const int WeaponSkinsFieldNumber = 1;
    private static readonly pb::FieldCodec<global::External.WeaponSkin> _repeated_weaponSkins_codec
        = pb::FieldCodec.ForMessage(10, global::External.WeaponSkin.Parser);
    private readonly pbc::RepeatedField<global::External.WeaponSkin> weaponSkins_ = new pbc::RepeatedField<global::External.WeaponSkin>();
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public pbc::RepeatedField<global::External.WeaponSkin> WeaponSkins {
      get { return weaponSkins_; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override bool Equals(object other) {
      return Equals(other as ExternalSetWeaponSkinsRequest);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public bool Equals(ExternalSetWeaponSkinsRequest other) {
      if (ReferenceEquals(other, null)) {
        return false;
      }
      if (ReferenceEquals(other, this)) {
        return true;
      }
      if(!weaponSkins_.Equals(other.weaponSkins_)) return false;
      return Equals(_unknownFields, other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override int GetHashCode() {
      int hash = 1;
      hash ^= weaponSkins_.GetHashCode();
      if (_unknownFields != null) {
        hash ^= _unknownFields.GetHashCode();
      }
      return hash;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override string ToString() {
      return pb::JsonFormatter.ToDiagnosticString(this);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void WriteTo(pb::CodedOutputStream output) {
    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      output.WriteRawMessage(this);
    #else
      weaponSkins_.WriteTo(output, _repeated_weaponSkins_codec);
      if (_unknownFields != null) {
        _unknownFields.WriteTo(output);
      }
    #endif
    }

    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    void pb::IBufferMessage.InternalWriteTo(ref pb::WriteContext output) {
      weaponSkins_.WriteTo(ref output, _repeated_weaponSkins_codec);
      if (_unknownFields != null) {
        _unknownFields.WriteTo(ref output);
      }
    }
    #endif

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public int CalculateSize() {
      int size = 0;
      size += weaponSkins_.CalculateSize(_repeated_weaponSkins_codec);
      if (_unknownFields != null) {
        size += _unknownFields.CalculateSize();
      }
      return size;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void MergeFrom(ExternalSetWeaponSkinsRequest other) {
      if (other == null) {
        return;
      }
      weaponSkins_.Add(other.weaponSkins_);
      _unknownFields = pb::UnknownFieldSet.MergeFrom(_unknownFields, other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void MergeFrom(pb::CodedInputStream input) {
    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      input.ReadRawMessage(this);
    #else
      uint tag;
      while ((tag = input.ReadTag()) != 0) {
        switch(tag) {
          default:
            _unknownFields = pb::UnknownFieldSet.MergeFieldFrom(_unknownFields, input);
            break;
          case 10: {
            weaponSkins_.AddEntriesFrom(input, _repeated_weaponSkins_codec);
            break;
          }
        }
      }
    #endif
    }

    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    void pb::IBufferMessage.InternalMergeFrom(ref pb::ParseContext input) {
      uint tag;
      while ((tag = input.ReadTag()) != 0) {
        switch(tag) {
          default:
            _unknownFields = pb::UnknownFieldSet.MergeFieldFrom(_unknownFields, ref input);
            break;
          case 10: {
            weaponSkins_.AddEntriesFrom(ref input, _repeated_weaponSkins_codec);
            break;
          }
        }
      }
    }
    #endif

  }

  #endregion

}

#endregion Designer generated code
