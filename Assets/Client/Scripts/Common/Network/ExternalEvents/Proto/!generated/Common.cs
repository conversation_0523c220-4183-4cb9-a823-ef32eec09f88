// <auto-generated>
//     Generated by the protocol buffer compiler.  DO NOT EDIT!
//     source: common.proto
// </auto-generated>
#pragma warning disable 1591, 0612, 3021, 8981
#region Designer generated code

using pb = global::Google.Protobuf;
using pbc = global::Google.Protobuf.Collections;
using pbr = global::Google.Protobuf.Reflection;
using scg = global::System.Collections.Generic;
namespace Common {

  /// <summary>Holder for reflection information generated from common.proto</summary>
  public static partial class CommonReflection {

    #region Descriptor
    /// <summary>File descriptor for common.proto</summary>
    public static pbr::FileDescriptor Descriptor {
      get { return descriptor; }
    }
    private static pbr::FileDescriptor descriptor;

    static CommonReflection() {
      byte[] descriptorData = global::System.Convert.FromBase64String(
          string.Concat(
            "Cgxjb21tb24ucHJvdG8SBmNvbW1vbhocZ29vZ2xlL3Byb3RvYnVmL3N0cnVj",
            "dC5wcm90byIvCg5TdGF0dXNSZXNwb25zZRIMCgRjb2RlGAEgASgDEg8KB21l",
            "c3NhZ2UYAiABKAkiLgoMUGFydFByb2dyZXNzEg0KBXBhcnRzGAEgAygDEg8K",
            "B2N1cnJlbnQYAiABKAMiTgoSUGFydFByb2dyZXNzUmV3YXJkEiUKBnJld2Fy",
            "ZBgBIAEoCzIVLmNvbW1vbi5JdGVtQ29tcG9uZW50EhEKCWF2YWlsYWJsZRgC",
            "IAEoCCIqCg1JdGVtQ29tcG9uZW50EgoKAmlkGAEgASgDEg0KBWNvdW50GAIg",
            "ASgDIl4KC0FjY291bnRJbmZvEg8KB3VzZXJfaWQYASABKAkSEAoIcG9zaXRp",
            "b24YAiABKAMSDgoGb3V0Zml0GAMgASgDEgwKBHJhbmsYBCABKAUSDgoGcG9p",
            "bnRzGAUgASgDIoEBCgtBY2NvdW50RGF0YRIPCgd1c2VyX2lkGAEgASgJEhAK",
            "CHBvc2l0aW9uGAIgASgDEg4KBm91dGZpdBgDIAEoAxIMCgRyYW5rGAQgASgF",
            "Eg4KBnBvaW50cxgFIAEoAxIQCghuaWNrbmFtZRgGIAEoCRIPCgdjb3VudHJ5",
            "GAcgASgJIisKDkFjY291bnRBYmlsaXR5EgoKAmlkGAEgASgFEg0KBWdyYWRl",
            "GAIgASgFIjEKC1RvcFJlc3BvbnNlEiIKBWl0ZW1zGAEgAygLMhMuY29tbW9u",
            "LkFjY291bnREYXRhIh4KC1VzZXJSZXF1ZXN0Eg8KB3VzZXJfaWQYASABKAki",
            "TgoNT3B0aW9uYWxGaWVsZBIQCgZudW1iZXIYASABKANIABIRCgdib29sZWFu",
            "GAIgASgISAASEAoGc3RyaW5nGAMgASgJSABCBgoEa2luZCJiCg9Vc2VyRmlu",
            "Z2VycHJpbnQSFAoMY291bnRyeV9jb2RlGAEgASgJEhQKDGNvdW50cnlfbmFt",
            "ZRgCIAEoCRIRCgljaXR5X25hbWUYAyABKAkSEAoIdGltZXpvbmUYBCABKAki",
            "bQoLQmxvb2RSZXdhcmQSCgoCaWQYASABKAMSDAoEaXRlbRgCIAMoAxITCgtk",
            "ZXNjcmlwdGlvbhgDIAEoCRINCgVwcmljZRgEIAEoAxIRCglyZWdpb25faWQY",
            "BSABKAUSDQoFcGFydHMYBiABKAUiTwoGUG9ja2V0EiAKBHR5cGUYASABKA4y",
            "Ei5jb21tb24uUG9ja2V0VHlwZRIjCgRpdGVtGAIgASgLMhUuY29tbW9uLkl0",
            "ZW1Db21wb25lbnQiagoSVXNlckRhaWx5UXVlc3RJbmZvEhIKCnVzZXJfZ3Jh",
            "ZGUYASABKA0SFQoNY3VycmVudF9xdWVzdBgCIAMoDRIWCg5yZXJvbGxlZF9x",
            "dWVzdBgDIAMoDRIRCglyZWxldmFuY2UYBCABKAMiKQoMQWJpbGl0eUdyYWRl",
            "EgoKAmlkGAEgASgFEg0KBXByaWNlGAIgASgDIk0KEkd1aWRlQWJpbGl0eUdy",
            "YWRlcxISCgphYmlsaXR5X2lkGAEgASgDEiMKBWdyYWRlGAIgAygLMhQuY29t",
            "bW9uLkFiaWxpdHlHcmFkZSIhCgVSYW5nZRILCgNtaW4YASABKAUSCwoDbWF4",
            "GAIgASgFImMKDU51bGxhYmxlUmFuZ2USKgoEbnVsbBgBIAEoDjIaLmdvb2ds",
            "ZS5wcm90b2J1Zi5OdWxsVmFsdWVIABIeCgV2YWx1ZRgCIAEoCzINLmNvbW1v",
            "bi5SYW5nZUgAQgYKBEtpbmQi3AEKC0d1aWRlTGVhZ3VlEgoKAmlkGAEgASgF",
            "EgwKBG5hbWUYAiABKAkSKgoLcGxhY2VfdG9fdXAYAyABKAsyFS5jb21tb24u",
            "TnVsbGFibGVSYW5nZRIsCg1wbGFjZV90b19zdGF5GAQgASgLMhUuY29tbW9u",
            "Lk51bGxhYmxlUmFuZ2USLAoNcGxhY2VfdG9fZG93bhgFIAEoCzIVLmNvbW1v",
            "bi5OdWxsYWJsZVJhbmdlEisKDGRhaWx5X3Jld2FyZBgGIAMoCzIVLmNvbW1v",
            "bi5JdGVtQ29tcG9uZW50ImMKD0tpbGxMb2NhbFJld2FyZBIKCgJpZBgBIAEo",
            "BRIOCgZyZWdpb24YAiABKAUSDQoFa2lsbHMYAyABKAMSJQoGcmV3YXJkGAQg",
            "AygLMhUuY29tbW9uLkl0ZW1Db21wb25lbnQiVAoNTnVsbGFibGVJbnQ2NBIq",
            "CgRudWxsGAEgASgOMhouZ29vZ2xlLnByb3RvYnVmLk51bGxWYWx1ZUgAEg8K",
            "BXZhbHVlGAIgASgDSABCBgoES2luZCJUCg1OdWxsYWJsZUZsb2F0EioKBG51",
            "bGwYASABKA4yGi5nb29nbGUucHJvdG9idWYuTnVsbFZhbHVlSAASDwoFdmFs",
            "dWUYAiABKAJIAEIGCgRLaW5kIuUCChBBY2NvdW50U3RhdGlzdGljEg8KB3Vz",
            "ZXJfaWQYASABKAkSJQoGZmlnaHRzGAIgASgLMhUuY29tbW9uLk51bGxhYmxl",
            "SW50NjQSJwoId2luX3JhdGUYAyABKAsyFS5jb21tb24uTnVsbGFibGVGbG9h",
            "dBInCghoYXJwb29ucxgEIAEoCzIVLmNvbW1vbi5OdWxsYWJsZUludDY0EigK",
            "CWhlYWRzaG90cxgFIAEoCzIVLmNvbW1vbi5OdWxsYWJsZUludDY0EiUKBmxv",
            "b3RlZBgGIAEoCzIVLmNvbW1vbi5OdWxsYWJsZUludDY0EiUKBmhlYWxlchgH",
            "IAEoCzIVLmNvbW1vbi5OdWxsYWJsZUludDY0EicKCHVubG9ja2VyGAggASgL",
            "MhUuY29tbW9uLk51bGxhYmxlSW50NjQSJgoHY3JhZnRlchgJIAEoCzIVLmNv",
            "bW1vbi5OdWxsYWJsZUludDY0Ij8KClVzZXJTZWFzb24SIQoEdXNlchgBIAEo",
            "CzITLmNvbW1vbi5BY2NvdW50RGF0YRIOCgZsZWFndWUYAyABKA0iPwoKV2Vh",
            "cG9uU2tpbhIKCgJpZBgBIAEoAxIRCgl3ZWFwb25faWQYAiABKAMSEgoKZHVy",
            "YWJpbGl0eRgDIAEoAyIwCg5Ta2luRHVyYWJpbGl0eRIKCgJpZBgBIAEoAxIS",
            "CgpkdXJhYmlsaXR5GAIgASgDIvsCCghQcm9ncmVzcxIMCgRyYW5rGAEgASgD",
            "Eg0KBWtpbGxzGAIgASgDEhEKCXdlYXBvbl9pZBgFIAEoAxIRCglvdXRmaXRf",
            "aWQYBiABKAMSEQoJYW11bGV0X2lkGAcgASgDEigKDHdlYXBvbl9za2lucxgJ",
            "IAMoCzISLmNvbW1vbi5XZWFwb25Ta2luEhIKCmJsb29kX3N0ZXAYCyABKAMS",
            "DgoGc3RhZ2VzGAwgAygJEhwKFGlzX3NldF9zdGFydGluZ19yYW5rGA0gASgI",
            "EjEKEXNraW5fZHVyYWJpbGl0aWVzGA4gAygLMhYuY29tbW9uLlNraW5EdXJh",
            "YmlsaXR5Eh0KFWJsb29kX2JvdHRsZV9tYXhfc2l6ZRgPIAEoAxIWCg5pc19w",
            "YWlkX3Jldml2ZRgQIAEoCBIiCgVzdGVwcxgRIAMoDjITLmNvbW1vbi5BY2Nv",
            "dW50U3RlcBIfCgdwb2NrZXRzGBIgAygLMg4uY29tbW9uLlBvY2tldCJ9CgVU",
            "aW1lchIKCgJpZBgBIAEoCRIPCgd1c2VyX2lkGAIgASgJEg8KB2l0ZW1faWQY",
            "AyABKAMSHwoEdHlwZRgEIAEoDjIRLmNvbW1vbi5UaW1lclR5cGUSEQoJZGVh",
            "ZF90aW1lGAUgASgJEhIKCnN0YXJ0X3RpbWUYBiABKAkiVQoNVGltZXJSZXNw",
            "b25zZRIcCgV0aW1lchgBIAEoCzINLmNvbW1vbi5UaW1lchImCgZzdGF0dXMY",
            "AiABKAsyFi5jb21tb24uU3RhdHVzUmVzcG9uc2UqcgoMQ3VycmVuY3lUeXBl",
            "EhQKEFVOS05PV05fQ1VSUkVOQ1kQABIICgRTT0ZUEAESCAoESEFSRBACEgkK",
            "BVBST01PEAMSCAoEUkVBTBAEEgkKBUJMT09EEAUSBgoCUlAQBhIQCgxMZWFn",
            "dWVUb2tlbnMQByotCghQbGF0Zm9ybRILCgdVTktOT1dOEAASBwoDSU9TEAES",
            "CwoHQU5EUk9JRBACKkcKCEJvZHlQYXJ0EgsKB1Vua25vd24QABIICgRIZWFk",
            "EAESCAoEQm9keRACEgcKA0FybRADEgcKA0xlZxAEEggKBEhpcHMQBSrxAgoI",
            "SXRlbVR5cGUSCgoGb3V0Zml0EAASEAoMb3V0Zml0X2Nsb3RoEAESDAoIc2h1",
            "cmlrZW4QAhISCg5zaHVyaWtlbl9zaGFyZBADEhEKDXNodXJpa2VuX3NraW4Q",
            "BBIXChNzaHVyaWtlbl9za2luX3BhaW50EAUSCgoGYW11bGV0EAYSEAoMYW11",
            "bGV0X3NoYXJkEAcSCgoGYm90dGxlEAgSCAoEaGVyYhAJEgwKCGxvY2twaWNr",
            "EAoSCAoEc2FjaxALEhYKEm91dGZpdF9jbG90aF9jaGVzdBAMEh0KGXNodXJp",
            "a2VuX3NraW5fcGFpbnRfY2hlc3QQDRILCgdzcGVjaWFsEA4SDAoIdHJlYXN1",
            "cmUQDxIUChBiYXR0bGVfY29uZGl0aW9uEBASEgoObXVsdGlfbG9vdF9ib3gQ",
            "ERIMCghjdXJyZW5jeRASEg8KC3Bvd2VyX3N0b25lEBMSCAoEaG9vaxAUEggK",
            "BGJvbWIQFSpQCghVc2VyUm9sZRIVChF1c2VyX3JvbGVfdW5rbm93bhAAEhQK",
            "EHVzZXJfcm9sZV9wbGF5ZXIQARIXChN1c2VyX3JvbGVfZGV2ZWxvcGVyEAIq",
            "oQgKC0FjY291bnRTdGVwEhgKFGFjY291bnRfc3RlcF9pbml0aWFsEAASKgom",
            "YWNjb3VudF9zdGVwX2V4aXRfZnJvbV90dXRvcmlhbF9yZWdpb24QARIqCiZh",
            "Y2NvdW50X3N0ZXBfZmlyc3RfZW50ZXJfYmF0dGxlX3JlZ2lvbhACEjEKLWFj",
            "Y291bnRfc3RlcF9zaHVyaWtlbl9jcmFmdF90dXRvcmlhbF9maW5pc2hlZBAD",
            "EiMKH2FjY291bnRfc3RlcF9hbGNoZW1pc3RfdW5sb2NrZWQQBBIyCi5hY2Nv",
            "dW50X3N0ZXBfYWxjaGVtaXN0X2NyYWZ0X3R1dG9yaWFsX2ZpbmlzaGVkEAUS",
            "MwovYWNjb3VudF9zdGVwX2FsY2hlbWlzdF90cmF1bWFfdHV0b3JpYWxfZmlu",
            "aXNoZWQQBhIkCiBhY2NvdW50X3N0ZXBfYW11bGV0X3RhYl91bmxvY2tlZBAH",
            "Ei8KK2FjY291bnRfc3RlcF9hbXVsZXRfY3JhZnRfdHV0b3JpYWxfZmluaXNo",
            "ZWQQCBIrCidhY2NvdW50X3N0ZXBfc2h1cmlrZW5fc2tpbl90YWJfdW5sb2Nr",
            "ZWQQCRIwCixhY2NvdW50X3N0ZXBfc2h1cmlrZW5fc2tpbl90dXRvcmlhbF9m",
            "aW5pc2hlZBAKEiQKIGFjY291bnRfc3RlcF9vdXRmaXRfdGFiX3VubG9ja2Vk",
            "EAsSKQolYWNjb3VudF9zdGVwX291dGZpdF90dXRvcmlhbF9maW5pc2hlZBAM",
            "EikKJWFjY291bnRfc3RlcF9jaGVzdHNfdHV0b3JpYWxfZmluaXNoZWQQDRIq",
            "CiZhY2NvdW50X3N0ZXBfdG91cm5hbWVudF9hbHRhcl91bmxvY2tlZBAOEiUK",
            "IWFjY291bnRfc3RlcF9kYWlseV9ib251c191bmxvY2tlZBAPEiYKImFjY291",
            "bnRfc3RlcF9kYWlseV9xdWVzdHNfdW5sb2NrZWQQEBI0CjBhY2NvdW50X3N0",
            "ZXBfZGFpbHlfcXVlc3RzX3N3YXBfdHV0b3JpYWxfZmluaXNoZWQQERImCiJh",
            "Y2NvdW50X3N0ZXBfcXRlX3R1dG9yaWFsX2ZpbmlzaGVkEBISKAokYWNjb3Vu",
            "dF9zdGVwX3BhcnJ5X3R1dG9yaWFsX2ZpbmlzaGVkEBMSLworYWNjb3VudF9z",
            "dGVwX3Bvd2VyX3N0b25lc190dXRvcmlhbF9maW5pc2hlZBAUEigKJGFjY291",
            "bnRfc3RlcF9ob29rc190dXRvcmlhbF9maW5pc2hlZBAVEigKJGFjY291bnRf",
            "c3RlcF9ib21ic190dXRvcmlhbF9maW5pc2hlZBAWEiUKIWFjY291bnRfc3Rl",
            "cF9ycF90dXRvcmlhbF9maW5pc2hlZBAXKlUKClBvY2tldFR5cGUSGwoXcG9j",
            "a2V0X3R5cGVfcG93ZXJfc3RvbmUQABIUChBwb2NrZXRfdHlwZV9ob29rEAES",
            "FAoQcG9ja2V0X3R5cGVfYm9tYhACKoUKCglHdWlkZVR5cGUSFAoQZ3VpZGVf",
            "dHlwZV9pdGVtcxAAEhoKFmd1aWRlX3R5cGVfa2lsbF9sb2NhbHMQARIhCh1n",
            "dWlkZV90eXBlX3NlcnZlcl9wcmVmZXJlbmNlcxACEiEKHWd1aWRlX3R5cGVf",
            "dG91cm5hbWVudF9yZXdhcmRzEAMSHAoYZ3VpZGVfdHlwZV9ibG9vZF9yZXdh",
            "cmRzEAQSIAocZ3VpZGVfdHlwZV9mcm9udF9wcmVmZXJlbmNlcxAFEiEKHWd1",
            "aWRlX3R5cGVfZnJvbnRfbG9jYWxpemF0aW9uEAYSHgoaZ3VpZGVfdHlwZV9m",
            "cm9udF9jb3VudHJpZXMQBxIeChpndWlkZV90eXBlX2Zyb250X2JvdF9uYW1l",
            "cxAIEh4KGmd1aWRlX3R5cGVfZnJvbnRfYm90X3R5cGVzEAkSIwofZ3VpZGVf",
            "dHlwZV9mcm9udF9ib3RfZGlmZmljdWx0eRAKEhUKEWd1aWRlX3R5cGVfcXVl",
            "c3RzEAsSGQoVZ3VpZGVfdHlwZV93aW5fc3RyZWFrEAwSHAoYZ3VpZGVfdHlw",
            "ZV93aW5fc3RyZWFrX2hwEA0SIAocZ3VpZGVfdHlwZV9mcm9udF9zZXJ2ZXJf",
            "aW5mbxAOEhgKFGd1aWRlX3R5cGVfcmFua19pbmZvEA8SHgoaZ3VpZGVfdHlw",
            "ZV9mcm9udF9yYW5rX2luZm8QEBIbChdndWlkZV90eXBlX2xvY2FsaXphdGlv",
            "bhAREhwKGGd1aWRlX3R5cGVfZnJvbnRfYWJpbGl0eRASEiQKIGd1aWRlX3R5",
            "cGVfZnJvbnRfYWJpbGl0aWVzX2dyYWRlEBMSIwofZ3VpZGVfdHlwZV9mcm9u",
            "dF9pdGVtX2FiaWxpdGllcxAUEi4KKmd1aWRlX3R5cGVfZnJvbnRfZWZmZWN0",
            "X21vZGlmaWVyc19ieV9pdGVtcxAVEhgKFGd1aWRlX3R5cGVfcHVyY2hhc2Vz",
            "EBYSHwobZ3VpZGVfdHlwZV9mcm9udF9pdGVtc19pY29uEBcSJAogZ3VpZGVf",
            "dHlwZV9sb2NrX3BpY2tfcHJvYmFiaWxpdHkQGBIcChhndWlkZV90eXBlX2Zy",
            "b250X2VmZmVjdHMQGRIhCh1ndWlkZV90eXBlX2Zyb250X2l0ZW1fZWZmZWN0",
            "cxAaEi4KKmd1aWRlX3R5cGVfZnJvbnRfYm90X2RpZmZpY3VsdHlfd2luX3N0",
            "cmVhaxAbEigKJGd1aWRlX3R5cGVfZnJvbnRfcmVnaW9uX3Jlc3VsdF93aGVl",
            "bBAcEhgKFGd1aWRlX3R5cGVfZnJvbnRfbW1yEB0SHAoYZ3VpZGVfdHlwZV9h",
            "YmlsaXR5X2dyYWRlEB4SHQoZZ3VpZGVfdHlwZV9pdGVtX2FiaWxpdGllcxAf",
            "EhoKFmd1aWRlX3R5cGVfZGFpbHlfYm9udXMQIBIlCiFndWlkZV90eXBlX2Rh",
            "aWx5X2JvbnVzX3JhbmtfY29lZmYQIRIlCiFndWlkZV90eXBlX2RhaWx5X2Jv",
            "bnVzX2RheV9zdHJlYWsQIhIVChFndWlkZV90eXBlX2xlYWd1ZRAjEiAKHGd1",
            "aWRlX3R5cGVfa2lsbF9sb2NhbF9yZXdhcmQQJBIhCh1ndWlkZV90eXBlX2Zy",
            "b250X3RnYm90X2V2ZW50cxAlEh0KGWd1aWRlX3R5cGVfcmFua19jb2VmX2lu",
            "Zm8QJipiCglUaW1lclR5cGUSEgoOdGltZXJfdHlwZV91c2UQABIUChB0aW1l",
            "cl90eXBlX2NyYWZ0EAESFQoRdGltZXJfdHlwZV90cmF1bWEQAhIUChB0aW1l",
            "cl90eXBlX2Jvb3N0EANiBnByb3RvMw=="));
      descriptor = pbr::FileDescriptor.FromGeneratedCode(descriptorData,
          new pbr::FileDescriptor[] { global::Google.Protobuf.WellKnownTypes.StructReflection.Descriptor, },
          new pbr::GeneratedClrTypeInfo(new[] {typeof(global::Common.CurrencyType), typeof(global::Common.Platform), typeof(global::Common.BodyPart), typeof(global::Common.ItemType), typeof(global::Common.UserRole), typeof(global::Common.AccountStep), typeof(global::Common.PocketType), typeof(global::Common.GuideType), typeof(global::Common.TimerType), }, null, new pbr::GeneratedClrTypeInfo[] {
            new pbr::GeneratedClrTypeInfo(typeof(global::Common.StatusResponse), global::Common.StatusResponse.Parser, new[]{ "Code", "Message" }, null, null, null, null),
            new pbr::GeneratedClrTypeInfo(typeof(global::Common.PartProgress), global::Common.PartProgress.Parser, new[]{ "Parts", "Current" }, null, null, null, null),
            new pbr::GeneratedClrTypeInfo(typeof(global::Common.PartProgressReward), global::Common.PartProgressReward.Parser, new[]{ "Reward", "Available" }, null, null, null, null),
            new pbr::GeneratedClrTypeInfo(typeof(global::Common.ItemComponent), global::Common.ItemComponent.Parser, new[]{ "Id", "Count" }, null, null, null, null),
            new pbr::GeneratedClrTypeInfo(typeof(global::Common.AccountInfo), global::Common.AccountInfo.Parser, new[]{ "UserId", "Position", "Outfit", "Rank", "Points" }, null, null, null, null),
            new pbr::GeneratedClrTypeInfo(typeof(global::Common.AccountData), global::Common.AccountData.Parser, new[]{ "UserId", "Position", "Outfit", "Rank", "Points", "Nickname", "Country" }, null, null, null, null),
            new pbr::GeneratedClrTypeInfo(typeof(global::Common.AccountAbility), global::Common.AccountAbility.Parser, new[]{ "Id", "Grade" }, null, null, null, null),
            new pbr::GeneratedClrTypeInfo(typeof(global::Common.TopResponse), global::Common.TopResponse.Parser, new[]{ "Items" }, null, null, null, null),
            new pbr::GeneratedClrTypeInfo(typeof(global::Common.UserRequest), global::Common.UserRequest.Parser, new[]{ "UserId" }, null, null, null, null),
            new pbr::GeneratedClrTypeInfo(typeof(global::Common.OptionalField), global::Common.OptionalField.Parser, new[]{ "Number", "Boolean", "String" }, new[]{ "Kind" }, null, null, null),
            new pbr::GeneratedClrTypeInfo(typeof(global::Common.UserFingerprint), global::Common.UserFingerprint.Parser, new[]{ "CountryCode", "CountryName", "CityName", "Timezone" }, null, null, null, null),
            new pbr::GeneratedClrTypeInfo(typeof(global::Common.BloodReward), global::Common.BloodReward.Parser, new[]{ "Id", "Item", "Description", "Price", "RegionId", "Parts" }, null, null, null, null),
            new pbr::GeneratedClrTypeInfo(typeof(global::Common.Pocket), global::Common.Pocket.Parser, new[]{ "Type", "Item" }, null, null, null, null),
            new pbr::GeneratedClrTypeInfo(typeof(global::Common.UserDailyQuestInfo), global::Common.UserDailyQuestInfo.Parser, new[]{ "UserGrade", "CurrentQuest", "RerolledQuest", "Relevance" }, null, null, null, null),
            new pbr::GeneratedClrTypeInfo(typeof(global::Common.AbilityGrade), global::Common.AbilityGrade.Parser, new[]{ "Id", "Price" }, null, null, null, null),
            new pbr::GeneratedClrTypeInfo(typeof(global::Common.GuideAbilityGrades), global::Common.GuideAbilityGrades.Parser, new[]{ "AbilityId", "Grade" }, null, null, null, null),
            new pbr::GeneratedClrTypeInfo(typeof(global::Common.Range), global::Common.Range.Parser, new[]{ "Min", "Max" }, null, null, null, null),
            new pbr::GeneratedClrTypeInfo(typeof(global::Common.NullableRange), global::Common.NullableRange.Parser, new[]{ "Null", "Value" }, new[]{ "Kind" }, null, null, null),
            new pbr::GeneratedClrTypeInfo(typeof(global::Common.GuideLeague), global::Common.GuideLeague.Parser, new[]{ "Id", "Name", "PlaceToUp", "PlaceToStay", "PlaceToDown", "DailyReward" }, null, null, null, null),
            new pbr::GeneratedClrTypeInfo(typeof(global::Common.KillLocalReward), global::Common.KillLocalReward.Parser, new[]{ "Id", "Region", "Kills", "Reward" }, null, null, null, null),
            new pbr::GeneratedClrTypeInfo(typeof(global::Common.NullableInt64), global::Common.NullableInt64.Parser, new[]{ "Null", "Value" }, new[]{ "Kind" }, null, null, null),
            new pbr::GeneratedClrTypeInfo(typeof(global::Common.NullableFloat), global::Common.NullableFloat.Parser, new[]{ "Null", "Value" }, new[]{ "Kind" }, null, null, null),
            new pbr::GeneratedClrTypeInfo(typeof(global::Common.AccountStatistic), global::Common.AccountStatistic.Parser, new[]{ "UserId", "Fights", "WinRate", "Harpoons", "Headshots", "Looted", "Healer", "Unlocker", "Crafter" }, null, null, null, null),
            new pbr::GeneratedClrTypeInfo(typeof(global::Common.UserSeason), global::Common.UserSeason.Parser, new[]{ "User", "League" }, null, null, null, null),
            new pbr::GeneratedClrTypeInfo(typeof(global::Common.WeaponSkin), global::Common.WeaponSkin.Parser, new[]{ "Id", "WeaponId", "Durability" }, null, null, null, null),
            new pbr::GeneratedClrTypeInfo(typeof(global::Common.SkinDurability), global::Common.SkinDurability.Parser, new[]{ "Id", "Durability" }, null, null, null, null),
            new pbr::GeneratedClrTypeInfo(typeof(global::Common.Progress), global::Common.Progress.Parser, new[]{ "Rank", "Kills", "WeaponId", "OutfitId", "AmuletId", "WeaponSkins", "BloodStep", "Stages", "IsSetStartingRank", "SkinDurabilities", "BloodBottleMaxSize", "IsPaidRevive", "Steps", "Pockets" }, null, null, null, null),
            new pbr::GeneratedClrTypeInfo(typeof(global::Common.Timer), global::Common.Timer.Parser, new[]{ "Id", "UserId", "ItemId", "Type", "DeadTime", "StartTime" }, null, null, null, null),
            new pbr::GeneratedClrTypeInfo(typeof(global::Common.TimerResponse), global::Common.TimerResponse.Parser, new[]{ "Timer", "Status" }, null, null, null, null)
          }));
    }
    #endregion

  }
  #region Enums
  public enum CurrencyType {
    [pbr::OriginalName("UNKNOWN_CURRENCY")] UnknownCurrency = 0,
    [pbr::OriginalName("SOFT")] Soft = 1,
    [pbr::OriginalName("HARD")] Hard = 2,
    [pbr::OriginalName("PROMO")] Promo = 3,
    [pbr::OriginalName("REAL")] Real = 4,
    [pbr::OriginalName("BLOOD")] Blood = 5,
    [pbr::OriginalName("RP")] Rp = 6,
    [pbr::OriginalName("LeagueTokens")] LeagueTokens = 7,
  }

  public enum Platform {
    [pbr::OriginalName("UNKNOWN")] Unknown = 0,
    [pbr::OriginalName("IOS")] Ios = 1,
    [pbr::OriginalName("ANDROID")] Android = 2,
  }

  public enum BodyPart {
    [pbr::OriginalName("Unknown")] Unknown = 0,
    [pbr::OriginalName("Head")] Head = 1,
    [pbr::OriginalName("Body")] Body = 2,
    [pbr::OriginalName("Arm")] Arm = 3,
    [pbr::OriginalName("Leg")] Leg = 4,
    [pbr::OriginalName("Hips")] Hips = 5,
  }

  public enum ItemType {
    [pbr::OriginalName("outfit")] Outfit = 0,
    [pbr::OriginalName("outfit_cloth")] OutfitCloth = 1,
    [pbr::OriginalName("shuriken")] Shuriken = 2,
    [pbr::OriginalName("shuriken_shard")] ShurikenShard = 3,
    [pbr::OriginalName("shuriken_skin")] ShurikenSkin = 4,
    [pbr::OriginalName("shuriken_skin_paint")] ShurikenSkinPaint = 5,
    [pbr::OriginalName("amulet")] Amulet = 6,
    [pbr::OriginalName("amulet_shard")] AmuletShard = 7,
    [pbr::OriginalName("bottle")] Bottle = 8,
    [pbr::OriginalName("herb")] Herb = 9,
    [pbr::OriginalName("lockpick")] Lockpick = 10,
    [pbr::OriginalName("sack")] Sack = 11,
    [pbr::OriginalName("outfit_cloth_chest")] OutfitClothChest = 12,
    [pbr::OriginalName("shuriken_skin_paint_chest")] ShurikenSkinPaintChest = 13,
    [pbr::OriginalName("special")] Special = 14,
    [pbr::OriginalName("treasure")] Treasure = 15,
    [pbr::OriginalName("battle_condition")] BattleCondition = 16,
    [pbr::OriginalName("multi_loot_box")] MultiLootBox = 17,
    [pbr::OriginalName("currency")] Currency = 18,
    [pbr::OriginalName("power_stone")] PowerStone = 19,
    [pbr::OriginalName("hook")] Hook = 20,
    [pbr::OriginalName("bomb")] Bomb = 21,
  }

  public enum UserRole {
    [pbr::OriginalName("user_role_unknown")] Unknown = 0,
    [pbr::OriginalName("user_role_player")] Player = 1,
    [pbr::OriginalName("user_role_developer")] Developer = 2,
  }

  public enum AccountStep {
    [pbr::OriginalName("account_step_initial")] Initial = 0,
    [pbr::OriginalName("account_step_exit_from_tutorial_region")] ExitFromTutorialRegion = 1,
    [pbr::OriginalName("account_step_first_enter_battle_region")] FirstEnterBattleRegion = 2,
    [pbr::OriginalName("account_step_shuriken_craft_tutorial_finished")] ShurikenCraftTutorialFinished = 3,
    [pbr::OriginalName("account_step_alchemist_unlocked")] AlchemistUnlocked = 4,
    [pbr::OriginalName("account_step_alchemist_craft_tutorial_finished")] AlchemistCraftTutorialFinished = 5,
    [pbr::OriginalName("account_step_alchemist_trauma_tutorial_finished")] AlchemistTraumaTutorialFinished = 6,
    [pbr::OriginalName("account_step_amulet_tab_unlocked")] AmuletTabUnlocked = 7,
    [pbr::OriginalName("account_step_amulet_craft_tutorial_finished")] AmuletCraftTutorialFinished = 8,
    [pbr::OriginalName("account_step_shuriken_skin_tab_unlocked")] ShurikenSkinTabUnlocked = 9,
    [pbr::OriginalName("account_step_shuriken_skin_tutorial_finished")] ShurikenSkinTutorialFinished = 10,
    [pbr::OriginalName("account_step_outfit_tab_unlocked")] OutfitTabUnlocked = 11,
    [pbr::OriginalName("account_step_outfit_tutorial_finished")] OutfitTutorialFinished = 12,
    [pbr::OriginalName("account_step_chests_tutorial_finished")] ChestsTutorialFinished = 13,
    [pbr::OriginalName("account_step_tournament_altar_unlocked")] TournamentAltarUnlocked = 14,
    [pbr::OriginalName("account_step_daily_bonus_unlocked")] DailyBonusUnlocked = 15,
    [pbr::OriginalName("account_step_daily_quests_unlocked")] DailyQuestsUnlocked = 16,
    [pbr::OriginalName("account_step_daily_quests_swap_tutorial_finished")] DailyQuestsSwapTutorialFinished = 17,
    [pbr::OriginalName("account_step_qte_tutorial_finished")] QteTutorialFinished = 18,
    [pbr::OriginalName("account_step_parry_tutorial_finished")] ParryTutorialFinished = 19,
    [pbr::OriginalName("account_step_power_stones_tutorial_finished")] PowerStonesTutorialFinished = 20,
    [pbr::OriginalName("account_step_hooks_tutorial_finished")] HooksTutorialFinished = 21,
    [pbr::OriginalName("account_step_bombs_tutorial_finished")] BombsTutorialFinished = 22,
    [pbr::OriginalName("account_step_rp_tutorial_finished")] RpTutorialFinished = 23,
  }

  public enum PocketType {
    [pbr::OriginalName("pocket_type_power_stone")] PowerStone = 0,
    [pbr::OriginalName("pocket_type_hook")] Hook = 1,
    [pbr::OriginalName("pocket_type_bomb")] Bomb = 2,
  }

  public enum GuideType {
    [pbr::OriginalName("guide_type_items")] Items = 0,
    [pbr::OriginalName("guide_type_kill_locals")] KillLocals = 1,
    [pbr::OriginalName("guide_type_server_preferences")] ServerPreferences = 2,
    [pbr::OriginalName("guide_type_tournament_rewards")] TournamentRewards = 3,
    [pbr::OriginalName("guide_type_blood_rewards")] BloodRewards = 4,
    [pbr::OriginalName("guide_type_front_preferences")] FrontPreferences = 5,
    [pbr::OriginalName("guide_type_front_localization")] FrontLocalization = 6,
    [pbr::OriginalName("guide_type_front_countries")] FrontCountries = 7,
    [pbr::OriginalName("guide_type_front_bot_names")] FrontBotNames = 8,
    [pbr::OriginalName("guide_type_front_bot_types")] FrontBotTypes = 9,
    [pbr::OriginalName("guide_type_front_bot_difficulty")] FrontBotDifficulty = 10,
    [pbr::OriginalName("guide_type_quests")] Quests = 11,
    [pbr::OriginalName("guide_type_win_streak")] WinStreak = 12,
    [pbr::OriginalName("guide_type_win_streak_hp")] WinStreakHp = 13,
    [pbr::OriginalName("guide_type_front_server_info")] FrontServerInfo = 14,
    [pbr::OriginalName("guide_type_rank_info")] RankInfo = 15,
    [pbr::OriginalName("guide_type_front_rank_info")] FrontRankInfo = 16,
    [pbr::OriginalName("guide_type_localization")] Localization = 17,
    [pbr::OriginalName("guide_type_front_ability")] FrontAbility = 18,
    [pbr::OriginalName("guide_type_front_abilities_grade")] FrontAbilitiesGrade = 19,
    [pbr::OriginalName("guide_type_front_item_abilities")] FrontItemAbilities = 20,
    [pbr::OriginalName("guide_type_front_effect_modifiers_by_items")] FrontEffectModifiersByItems = 21,
    [pbr::OriginalName("guide_type_purchases")] Purchases = 22,
    [pbr::OriginalName("guide_type_front_items_icon")] FrontItemsIcon = 23,
    [pbr::OriginalName("guide_type_lock_pick_probability")] LockPickProbability = 24,
    [pbr::OriginalName("guide_type_front_effects")] FrontEffects = 25,
    [pbr::OriginalName("guide_type_front_item_effects")] FrontItemEffects = 26,
    [pbr::OriginalName("guide_type_front_bot_difficulty_win_streak")] FrontBotDifficultyWinStreak = 27,
    [pbr::OriginalName("guide_type_front_region_result_wheel")] FrontRegionResultWheel = 28,
    [pbr::OriginalName("guide_type_front_mmr")] FrontMmr = 29,
    [pbr::OriginalName("guide_type_ability_grade")] AbilityGrade = 30,
    [pbr::OriginalName("guide_type_item_abilities")] ItemAbilities = 31,
    [pbr::OriginalName("guide_type_daily_bonus")] DailyBonus = 32,
    [pbr::OriginalName("guide_type_daily_bonus_rank_coeff")] DailyBonusRankCoeff = 33,
    [pbr::OriginalName("guide_type_daily_bonus_day_streak")] DailyBonusDayStreak = 34,
    [pbr::OriginalName("guide_type_league")] League = 35,
    [pbr::OriginalName("guide_type_kill_local_reward")] KillLocalReward = 36,
    [pbr::OriginalName("guide_type_front_tgbot_events")] FrontTgbotEvents = 37,
    [pbr::OriginalName("guide_type_rank_coef_info")] RankCoefInfo = 38,
  }

  public enum TimerType {
    [pbr::OriginalName("timer_type_use")] Use = 0,
    [pbr::OriginalName("timer_type_craft")] Craft = 1,
    [pbr::OriginalName("timer_type_trauma")] Trauma = 2,
    [pbr::OriginalName("timer_type_boost")] Boost = 3,
  }

  #endregion

  #region Messages
  [global::System.Diagnostics.DebuggerDisplayAttribute("{ToString(),nq}")]
  public sealed partial class StatusResponse : pb::IMessage<StatusResponse>
  #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      , pb::IBufferMessage
  #endif
  {
    private static readonly pb::MessageParser<StatusResponse> _parser = new pb::MessageParser<StatusResponse>(() => new StatusResponse());
    private pb::UnknownFieldSet _unknownFields;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public static pb::MessageParser<StatusResponse> Parser { get { return _parser; } }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public static pbr::MessageDescriptor Descriptor {
      get { return global::Common.CommonReflection.Descriptor.MessageTypes[0]; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    pbr::MessageDescriptor pb::IMessage.Descriptor {
      get { return Descriptor; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public StatusResponse() {
      OnConstruction();
    }

    partial void OnConstruction();

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public StatusResponse(StatusResponse other) : this() {
      code_ = other.code_;
      message_ = other.message_;
      _unknownFields = pb::UnknownFieldSet.Clone(other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public StatusResponse Clone() {
      return new StatusResponse(this);
    }

    /// <summary>Field number for the "code" field.</summary>
    public const int CodeFieldNumber = 1;
    private long code_;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public long Code {
      get { return code_; }
      set {
        code_ = value;
      }
    }

    /// <summary>Field number for the "message" field.</summary>
    public const int MessageFieldNumber = 2;
    private string message_ = "";
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public string Message {
      get { return message_; }
      set {
        message_ = pb::ProtoPreconditions.CheckNotNull(value, "value");
      }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override bool Equals(object other) {
      return Equals(other as StatusResponse);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public bool Equals(StatusResponse other) {
      if (ReferenceEquals(other, null)) {
        return false;
      }
      if (ReferenceEquals(other, this)) {
        return true;
      }
      if (Code != other.Code) return false;
      if (Message != other.Message) return false;
      return Equals(_unknownFields, other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override int GetHashCode() {
      int hash = 1;
      if (Code != 0L) hash ^= Code.GetHashCode();
      if (Message.Length != 0) hash ^= Message.GetHashCode();
      if (_unknownFields != null) {
        hash ^= _unknownFields.GetHashCode();
      }
      return hash;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override string ToString() {
      return pb::JsonFormatter.ToDiagnosticString(this);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void WriteTo(pb::CodedOutputStream output) {
    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      output.WriteRawMessage(this);
    #else
      if (Code != 0L) {
        output.WriteRawTag(8);
        output.WriteInt64(Code);
      }
      if (Message.Length != 0) {
        output.WriteRawTag(18);
        output.WriteString(Message);
      }
      if (_unknownFields != null) {
        _unknownFields.WriteTo(output);
      }
    #endif
    }

    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    void pb::IBufferMessage.InternalWriteTo(ref pb::WriteContext output) {
      if (Code != 0L) {
        output.WriteRawTag(8);
        output.WriteInt64(Code);
      }
      if (Message.Length != 0) {
        output.WriteRawTag(18);
        output.WriteString(Message);
      }
      if (_unknownFields != null) {
        _unknownFields.WriteTo(ref output);
      }
    }
    #endif

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public int CalculateSize() {
      int size = 0;
      if (Code != 0L) {
        size += 1 + pb::CodedOutputStream.ComputeInt64Size(Code);
      }
      if (Message.Length != 0) {
        size += 1 + pb::CodedOutputStream.ComputeStringSize(Message);
      }
      if (_unknownFields != null) {
        size += _unknownFields.CalculateSize();
      }
      return size;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void MergeFrom(StatusResponse other) {
      if (other == null) {
        return;
      }
      if (other.Code != 0L) {
        Code = other.Code;
      }
      if (other.Message.Length != 0) {
        Message = other.Message;
      }
      _unknownFields = pb::UnknownFieldSet.MergeFrom(_unknownFields, other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void MergeFrom(pb::CodedInputStream input) {
    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      input.ReadRawMessage(this);
    #else
      uint tag;
      while ((tag = input.ReadTag()) != 0) {
        switch(tag) {
          default:
            _unknownFields = pb::UnknownFieldSet.MergeFieldFrom(_unknownFields, input);
            break;
          case 8: {
            Code = input.ReadInt64();
            break;
          }
          case 18: {
            Message = input.ReadString();
            break;
          }
        }
      }
    #endif
    }

    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    void pb::IBufferMessage.InternalMergeFrom(ref pb::ParseContext input) {
      uint tag;
      while ((tag = input.ReadTag()) != 0) {
        switch(tag) {
          default:
            _unknownFields = pb::UnknownFieldSet.MergeFieldFrom(_unknownFields, ref input);
            break;
          case 8: {
            Code = input.ReadInt64();
            break;
          }
          case 18: {
            Message = input.ReadString();
            break;
          }
        }
      }
    }
    #endif

  }

  [global::System.Diagnostics.DebuggerDisplayAttribute("{ToString(),nq}")]
  public sealed partial class PartProgress : pb::IMessage<PartProgress>
  #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      , pb::IBufferMessage
  #endif
  {
    private static readonly pb::MessageParser<PartProgress> _parser = new pb::MessageParser<PartProgress>(() => new PartProgress());
    private pb::UnknownFieldSet _unknownFields;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public static pb::MessageParser<PartProgress> Parser { get { return _parser; } }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public static pbr::MessageDescriptor Descriptor {
      get { return global::Common.CommonReflection.Descriptor.MessageTypes[1]; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    pbr::MessageDescriptor pb::IMessage.Descriptor {
      get { return Descriptor; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public PartProgress() {
      OnConstruction();
    }

    partial void OnConstruction();

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public PartProgress(PartProgress other) : this() {
      parts_ = other.parts_.Clone();
      current_ = other.current_;
      _unknownFields = pb::UnknownFieldSet.Clone(other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public PartProgress Clone() {
      return new PartProgress(this);
    }

    /// <summary>Field number for the "parts" field.</summary>
    public const int PartsFieldNumber = 1;
    private static readonly pb::FieldCodec<long> _repeated_parts_codec
        = pb::FieldCodec.ForInt64(10);
    private readonly pbc::RepeatedField<long> parts_ = new pbc::RepeatedField<long>();
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public pbc::RepeatedField<long> Parts {
      get { return parts_; }
    }

    /// <summary>Field number for the "current" field.</summary>
    public const int CurrentFieldNumber = 2;
    private long current_;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public long Current {
      get { return current_; }
      set {
        current_ = value;
      }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override bool Equals(object other) {
      return Equals(other as PartProgress);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public bool Equals(PartProgress other) {
      if (ReferenceEquals(other, null)) {
        return false;
      }
      if (ReferenceEquals(other, this)) {
        return true;
      }
      if(!parts_.Equals(other.parts_)) return false;
      if (Current != other.Current) return false;
      return Equals(_unknownFields, other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override int GetHashCode() {
      int hash = 1;
      hash ^= parts_.GetHashCode();
      if (Current != 0L) hash ^= Current.GetHashCode();
      if (_unknownFields != null) {
        hash ^= _unknownFields.GetHashCode();
      }
      return hash;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override string ToString() {
      return pb::JsonFormatter.ToDiagnosticString(this);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void WriteTo(pb::CodedOutputStream output) {
    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      output.WriteRawMessage(this);
    #else
      parts_.WriteTo(output, _repeated_parts_codec);
      if (Current != 0L) {
        output.WriteRawTag(16);
        output.WriteInt64(Current);
      }
      if (_unknownFields != null) {
        _unknownFields.WriteTo(output);
      }
    #endif
    }

    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    void pb::IBufferMessage.InternalWriteTo(ref pb::WriteContext output) {
      parts_.WriteTo(ref output, _repeated_parts_codec);
      if (Current != 0L) {
        output.WriteRawTag(16);
        output.WriteInt64(Current);
      }
      if (_unknownFields != null) {
        _unknownFields.WriteTo(ref output);
      }
    }
    #endif

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public int CalculateSize() {
      int size = 0;
      size += parts_.CalculateSize(_repeated_parts_codec);
      if (Current != 0L) {
        size += 1 + pb::CodedOutputStream.ComputeInt64Size(Current);
      }
      if (_unknownFields != null) {
        size += _unknownFields.CalculateSize();
      }
      return size;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void MergeFrom(PartProgress other) {
      if (other == null) {
        return;
      }
      parts_.Add(other.parts_);
      if (other.Current != 0L) {
        Current = other.Current;
      }
      _unknownFields = pb::UnknownFieldSet.MergeFrom(_unknownFields, other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void MergeFrom(pb::CodedInputStream input) {
    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      input.ReadRawMessage(this);
    #else
      uint tag;
      while ((tag = input.ReadTag()) != 0) {
        switch(tag) {
          default:
            _unknownFields = pb::UnknownFieldSet.MergeFieldFrom(_unknownFields, input);
            break;
          case 10:
          case 8: {
            parts_.AddEntriesFrom(input, _repeated_parts_codec);
            break;
          }
          case 16: {
            Current = input.ReadInt64();
            break;
          }
        }
      }
    #endif
    }

    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    void pb::IBufferMessage.InternalMergeFrom(ref pb::ParseContext input) {
      uint tag;
      while ((tag = input.ReadTag()) != 0) {
        switch(tag) {
          default:
            _unknownFields = pb::UnknownFieldSet.MergeFieldFrom(_unknownFields, ref input);
            break;
          case 10:
          case 8: {
            parts_.AddEntriesFrom(ref input, _repeated_parts_codec);
            break;
          }
          case 16: {
            Current = input.ReadInt64();
            break;
          }
        }
      }
    }
    #endif

  }

  [global::System.Diagnostics.DebuggerDisplayAttribute("{ToString(),nq}")]
  public sealed partial class PartProgressReward : pb::IMessage<PartProgressReward>
  #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      , pb::IBufferMessage
  #endif
  {
    private static readonly pb::MessageParser<PartProgressReward> _parser = new pb::MessageParser<PartProgressReward>(() => new PartProgressReward());
    private pb::UnknownFieldSet _unknownFields;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public static pb::MessageParser<PartProgressReward> Parser { get { return _parser; } }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public static pbr::MessageDescriptor Descriptor {
      get { return global::Common.CommonReflection.Descriptor.MessageTypes[2]; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    pbr::MessageDescriptor pb::IMessage.Descriptor {
      get { return Descriptor; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public PartProgressReward() {
      OnConstruction();
    }

    partial void OnConstruction();

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public PartProgressReward(PartProgressReward other) : this() {
      reward_ = other.reward_ != null ? other.reward_.Clone() : null;
      available_ = other.available_;
      _unknownFields = pb::UnknownFieldSet.Clone(other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public PartProgressReward Clone() {
      return new PartProgressReward(this);
    }

    /// <summary>Field number for the "reward" field.</summary>
    public const int RewardFieldNumber = 1;
    private global::Common.ItemComponent reward_;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public global::Common.ItemComponent Reward {
      get { return reward_; }
      set {
        reward_ = value;
      }
    }

    /// <summary>Field number for the "available" field.</summary>
    public const int AvailableFieldNumber = 2;
    private bool available_;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public bool Available {
      get { return available_; }
      set {
        available_ = value;
      }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override bool Equals(object other) {
      return Equals(other as PartProgressReward);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public bool Equals(PartProgressReward other) {
      if (ReferenceEquals(other, null)) {
        return false;
      }
      if (ReferenceEquals(other, this)) {
        return true;
      }
      if (!object.Equals(Reward, other.Reward)) return false;
      if (Available != other.Available) return false;
      return Equals(_unknownFields, other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override int GetHashCode() {
      int hash = 1;
      if (reward_ != null) hash ^= Reward.GetHashCode();
      if (Available != false) hash ^= Available.GetHashCode();
      if (_unknownFields != null) {
        hash ^= _unknownFields.GetHashCode();
      }
      return hash;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override string ToString() {
      return pb::JsonFormatter.ToDiagnosticString(this);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void WriteTo(pb::CodedOutputStream output) {
    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      output.WriteRawMessage(this);
    #else
      if (reward_ != null) {
        output.WriteRawTag(10);
        output.WriteMessage(Reward);
      }
      if (Available != false) {
        output.WriteRawTag(16);
        output.WriteBool(Available);
      }
      if (_unknownFields != null) {
        _unknownFields.WriteTo(output);
      }
    #endif
    }

    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    void pb::IBufferMessage.InternalWriteTo(ref pb::WriteContext output) {
      if (reward_ != null) {
        output.WriteRawTag(10);
        output.WriteMessage(Reward);
      }
      if (Available != false) {
        output.WriteRawTag(16);
        output.WriteBool(Available);
      }
      if (_unknownFields != null) {
        _unknownFields.WriteTo(ref output);
      }
    }
    #endif

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public int CalculateSize() {
      int size = 0;
      if (reward_ != null) {
        size += 1 + pb::CodedOutputStream.ComputeMessageSize(Reward);
      }
      if (Available != false) {
        size += 1 + 1;
      }
      if (_unknownFields != null) {
        size += _unknownFields.CalculateSize();
      }
      return size;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void MergeFrom(PartProgressReward other) {
      if (other == null) {
        return;
      }
      if (other.reward_ != null) {
        if (reward_ == null) {
          Reward = new global::Common.ItemComponent();
        }
        Reward.MergeFrom(other.Reward);
      }
      if (other.Available != false) {
        Available = other.Available;
      }
      _unknownFields = pb::UnknownFieldSet.MergeFrom(_unknownFields, other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void MergeFrom(pb::CodedInputStream input) {
    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      input.ReadRawMessage(this);
    #else
      uint tag;
      while ((tag = input.ReadTag()) != 0) {
        switch(tag) {
          default:
            _unknownFields = pb::UnknownFieldSet.MergeFieldFrom(_unknownFields, input);
            break;
          case 10: {
            if (reward_ == null) {
              Reward = new global::Common.ItemComponent();
            }
            input.ReadMessage(Reward);
            break;
          }
          case 16: {
            Available = input.ReadBool();
            break;
          }
        }
      }
    #endif
    }

    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    void pb::IBufferMessage.InternalMergeFrom(ref pb::ParseContext input) {
      uint tag;
      while ((tag = input.ReadTag()) != 0) {
        switch(tag) {
          default:
            _unknownFields = pb::UnknownFieldSet.MergeFieldFrom(_unknownFields, ref input);
            break;
          case 10: {
            if (reward_ == null) {
              Reward = new global::Common.ItemComponent();
            }
            input.ReadMessage(Reward);
            break;
          }
          case 16: {
            Available = input.ReadBool();
            break;
          }
        }
      }
    }
    #endif

  }

  [global::System.Diagnostics.DebuggerDisplayAttribute("{ToString(),nq}")]
  public sealed partial class ItemComponent : pb::IMessage<ItemComponent>
  #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      , pb::IBufferMessage
  #endif
  {
    private static readonly pb::MessageParser<ItemComponent> _parser = new pb::MessageParser<ItemComponent>(() => new ItemComponent());
    private pb::UnknownFieldSet _unknownFields;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public static pb::MessageParser<ItemComponent> Parser { get { return _parser; } }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public static pbr::MessageDescriptor Descriptor {
      get { return global::Common.CommonReflection.Descriptor.MessageTypes[3]; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    pbr::MessageDescriptor pb::IMessage.Descriptor {
      get { return Descriptor; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public ItemComponent() {
      OnConstruction();
    }

    partial void OnConstruction();

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public ItemComponent(ItemComponent other) : this() {
      id_ = other.id_;
      count_ = other.count_;
      _unknownFields = pb::UnknownFieldSet.Clone(other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public ItemComponent Clone() {
      return new ItemComponent(this);
    }

    /// <summary>Field number for the "id" field.</summary>
    public const int IdFieldNumber = 1;
    private long id_;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public long Id {
      get { return id_; }
      set {
        id_ = value;
      }
    }

    /// <summary>Field number for the "count" field.</summary>
    public const int CountFieldNumber = 2;
    private long count_;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public long Count {
      get { return count_; }
      set {
        count_ = value;
      }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override bool Equals(object other) {
      return Equals(other as ItemComponent);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public bool Equals(ItemComponent other) {
      if (ReferenceEquals(other, null)) {
        return false;
      }
      if (ReferenceEquals(other, this)) {
        return true;
      }
      if (Id != other.Id) return false;
      if (Count != other.Count) return false;
      return Equals(_unknownFields, other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override int GetHashCode() {
      int hash = 1;
      if (Id != 0L) hash ^= Id.GetHashCode();
      if (Count != 0L) hash ^= Count.GetHashCode();
      if (_unknownFields != null) {
        hash ^= _unknownFields.GetHashCode();
      }
      return hash;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override string ToString() {
      return pb::JsonFormatter.ToDiagnosticString(this);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void WriteTo(pb::CodedOutputStream output) {
    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      output.WriteRawMessage(this);
    #else
      if (Id != 0L) {
        output.WriteRawTag(8);
        output.WriteInt64(Id);
      }
      if (Count != 0L) {
        output.WriteRawTag(16);
        output.WriteInt64(Count);
      }
      if (_unknownFields != null) {
        _unknownFields.WriteTo(output);
      }
    #endif
    }

    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    void pb::IBufferMessage.InternalWriteTo(ref pb::WriteContext output) {
      if (Id != 0L) {
        output.WriteRawTag(8);
        output.WriteInt64(Id);
      }
      if (Count != 0L) {
        output.WriteRawTag(16);
        output.WriteInt64(Count);
      }
      if (_unknownFields != null) {
        _unknownFields.WriteTo(ref output);
      }
    }
    #endif

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public int CalculateSize() {
      int size = 0;
      if (Id != 0L) {
        size += 1 + pb::CodedOutputStream.ComputeInt64Size(Id);
      }
      if (Count != 0L) {
        size += 1 + pb::CodedOutputStream.ComputeInt64Size(Count);
      }
      if (_unknownFields != null) {
        size += _unknownFields.CalculateSize();
      }
      return size;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void MergeFrom(ItemComponent other) {
      if (other == null) {
        return;
      }
      if (other.Id != 0L) {
        Id = other.Id;
      }
      if (other.Count != 0L) {
        Count = other.Count;
      }
      _unknownFields = pb::UnknownFieldSet.MergeFrom(_unknownFields, other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void MergeFrom(pb::CodedInputStream input) {
    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      input.ReadRawMessage(this);
    #else
      uint tag;
      while ((tag = input.ReadTag()) != 0) {
        switch(tag) {
          default:
            _unknownFields = pb::UnknownFieldSet.MergeFieldFrom(_unknownFields, input);
            break;
          case 8: {
            Id = input.ReadInt64();
            break;
          }
          case 16: {
            Count = input.ReadInt64();
            break;
          }
        }
      }
    #endif
    }

    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    void pb::IBufferMessage.InternalMergeFrom(ref pb::ParseContext input) {
      uint tag;
      while ((tag = input.ReadTag()) != 0) {
        switch(tag) {
          default:
            _unknownFields = pb::UnknownFieldSet.MergeFieldFrom(_unknownFields, ref input);
            break;
          case 8: {
            Id = input.ReadInt64();
            break;
          }
          case 16: {
            Count = input.ReadInt64();
            break;
          }
        }
      }
    }
    #endif

  }

  [global::System.Diagnostics.DebuggerDisplayAttribute("{ToString(),nq}")]
  public sealed partial class AccountInfo : pb::IMessage<AccountInfo>
  #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      , pb::IBufferMessage
  #endif
  {
    private static readonly pb::MessageParser<AccountInfo> _parser = new pb::MessageParser<AccountInfo>(() => new AccountInfo());
    private pb::UnknownFieldSet _unknownFields;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public static pb::MessageParser<AccountInfo> Parser { get { return _parser; } }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public static pbr::MessageDescriptor Descriptor {
      get { return global::Common.CommonReflection.Descriptor.MessageTypes[4]; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    pbr::MessageDescriptor pb::IMessage.Descriptor {
      get { return Descriptor; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public AccountInfo() {
      OnConstruction();
    }

    partial void OnConstruction();

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public AccountInfo(AccountInfo other) : this() {
      userId_ = other.userId_;
      position_ = other.position_;
      outfit_ = other.outfit_;
      rank_ = other.rank_;
      points_ = other.points_;
      _unknownFields = pb::UnknownFieldSet.Clone(other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public AccountInfo Clone() {
      return new AccountInfo(this);
    }

    /// <summary>Field number for the "user_id" field.</summary>
    public const int UserIdFieldNumber = 1;
    private string userId_ = "";
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public string UserId {
      get { return userId_; }
      set {
        userId_ = pb::ProtoPreconditions.CheckNotNull(value, "value");
      }
    }

    /// <summary>Field number for the "position" field.</summary>
    public const int PositionFieldNumber = 2;
    private long position_;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public long Position {
      get { return position_; }
      set {
        position_ = value;
      }
    }

    /// <summary>Field number for the "outfit" field.</summary>
    public const int OutfitFieldNumber = 3;
    private long outfit_;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public long Outfit {
      get { return outfit_; }
      set {
        outfit_ = value;
      }
    }

    /// <summary>Field number for the "rank" field.</summary>
    public const int RankFieldNumber = 4;
    private int rank_;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public int Rank {
      get { return rank_; }
      set {
        rank_ = value;
      }
    }

    /// <summary>Field number for the "points" field.</summary>
    public const int PointsFieldNumber = 5;
    private long points_;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public long Points {
      get { return points_; }
      set {
        points_ = value;
      }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override bool Equals(object other) {
      return Equals(other as AccountInfo);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public bool Equals(AccountInfo other) {
      if (ReferenceEquals(other, null)) {
        return false;
      }
      if (ReferenceEquals(other, this)) {
        return true;
      }
      if (UserId != other.UserId) return false;
      if (Position != other.Position) return false;
      if (Outfit != other.Outfit) return false;
      if (Rank != other.Rank) return false;
      if (Points != other.Points) return false;
      return Equals(_unknownFields, other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override int GetHashCode() {
      int hash = 1;
      if (UserId.Length != 0) hash ^= UserId.GetHashCode();
      if (Position != 0L) hash ^= Position.GetHashCode();
      if (Outfit != 0L) hash ^= Outfit.GetHashCode();
      if (Rank != 0) hash ^= Rank.GetHashCode();
      if (Points != 0L) hash ^= Points.GetHashCode();
      if (_unknownFields != null) {
        hash ^= _unknownFields.GetHashCode();
      }
      return hash;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override string ToString() {
      return pb::JsonFormatter.ToDiagnosticString(this);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void WriteTo(pb::CodedOutputStream output) {
    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      output.WriteRawMessage(this);
    #else
      if (UserId.Length != 0) {
        output.WriteRawTag(10);
        output.WriteString(UserId);
      }
      if (Position != 0L) {
        output.WriteRawTag(16);
        output.WriteInt64(Position);
      }
      if (Outfit != 0L) {
        output.WriteRawTag(24);
        output.WriteInt64(Outfit);
      }
      if (Rank != 0) {
        output.WriteRawTag(32);
        output.WriteInt32(Rank);
      }
      if (Points != 0L) {
        output.WriteRawTag(40);
        output.WriteInt64(Points);
      }
      if (_unknownFields != null) {
        _unknownFields.WriteTo(output);
      }
    #endif
    }

    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    void pb::IBufferMessage.InternalWriteTo(ref pb::WriteContext output) {
      if (UserId.Length != 0) {
        output.WriteRawTag(10);
        output.WriteString(UserId);
      }
      if (Position != 0L) {
        output.WriteRawTag(16);
        output.WriteInt64(Position);
      }
      if (Outfit != 0L) {
        output.WriteRawTag(24);
        output.WriteInt64(Outfit);
      }
      if (Rank != 0) {
        output.WriteRawTag(32);
        output.WriteInt32(Rank);
      }
      if (Points != 0L) {
        output.WriteRawTag(40);
        output.WriteInt64(Points);
      }
      if (_unknownFields != null) {
        _unknownFields.WriteTo(ref output);
      }
    }
    #endif

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public int CalculateSize() {
      int size = 0;
      if (UserId.Length != 0) {
        size += 1 + pb::CodedOutputStream.ComputeStringSize(UserId);
      }
      if (Position != 0L) {
        size += 1 + pb::CodedOutputStream.ComputeInt64Size(Position);
      }
      if (Outfit != 0L) {
        size += 1 + pb::CodedOutputStream.ComputeInt64Size(Outfit);
      }
      if (Rank != 0) {
        size += 1 + pb::CodedOutputStream.ComputeInt32Size(Rank);
      }
      if (Points != 0L) {
        size += 1 + pb::CodedOutputStream.ComputeInt64Size(Points);
      }
      if (_unknownFields != null) {
        size += _unknownFields.CalculateSize();
      }
      return size;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void MergeFrom(AccountInfo other) {
      if (other == null) {
        return;
      }
      if (other.UserId.Length != 0) {
        UserId = other.UserId;
      }
      if (other.Position != 0L) {
        Position = other.Position;
      }
      if (other.Outfit != 0L) {
        Outfit = other.Outfit;
      }
      if (other.Rank != 0) {
        Rank = other.Rank;
      }
      if (other.Points != 0L) {
        Points = other.Points;
      }
      _unknownFields = pb::UnknownFieldSet.MergeFrom(_unknownFields, other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void MergeFrom(pb::CodedInputStream input) {
    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      input.ReadRawMessage(this);
    #else
      uint tag;
      while ((tag = input.ReadTag()) != 0) {
        switch(tag) {
          default:
            _unknownFields = pb::UnknownFieldSet.MergeFieldFrom(_unknownFields, input);
            break;
          case 10: {
            UserId = input.ReadString();
            break;
          }
          case 16: {
            Position = input.ReadInt64();
            break;
          }
          case 24: {
            Outfit = input.ReadInt64();
            break;
          }
          case 32: {
            Rank = input.ReadInt32();
            break;
          }
          case 40: {
            Points = input.ReadInt64();
            break;
          }
        }
      }
    #endif
    }

    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    void pb::IBufferMessage.InternalMergeFrom(ref pb::ParseContext input) {
      uint tag;
      while ((tag = input.ReadTag()) != 0) {
        switch(tag) {
          default:
            _unknownFields = pb::UnknownFieldSet.MergeFieldFrom(_unknownFields, ref input);
            break;
          case 10: {
            UserId = input.ReadString();
            break;
          }
          case 16: {
            Position = input.ReadInt64();
            break;
          }
          case 24: {
            Outfit = input.ReadInt64();
            break;
          }
          case 32: {
            Rank = input.ReadInt32();
            break;
          }
          case 40: {
            Points = input.ReadInt64();
            break;
          }
        }
      }
    }
    #endif

  }

  [global::System.Diagnostics.DebuggerDisplayAttribute("{ToString(),nq}")]
  public sealed partial class AccountData : pb::IMessage<AccountData>
  #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      , pb::IBufferMessage
  #endif
  {
    private static readonly pb::MessageParser<AccountData> _parser = new pb::MessageParser<AccountData>(() => new AccountData());
    private pb::UnknownFieldSet _unknownFields;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public static pb::MessageParser<AccountData> Parser { get { return _parser; } }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public static pbr::MessageDescriptor Descriptor {
      get { return global::Common.CommonReflection.Descriptor.MessageTypes[5]; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    pbr::MessageDescriptor pb::IMessage.Descriptor {
      get { return Descriptor; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public AccountData() {
      OnConstruction();
    }

    partial void OnConstruction();

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public AccountData(AccountData other) : this() {
      userId_ = other.userId_;
      position_ = other.position_;
      outfit_ = other.outfit_;
      rank_ = other.rank_;
      points_ = other.points_;
      nickname_ = other.nickname_;
      country_ = other.country_;
      _unknownFields = pb::UnknownFieldSet.Clone(other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public AccountData Clone() {
      return new AccountData(this);
    }

    /// <summary>Field number for the "user_id" field.</summary>
    public const int UserIdFieldNumber = 1;
    private string userId_ = "";
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public string UserId {
      get { return userId_; }
      set {
        userId_ = pb::ProtoPreconditions.CheckNotNull(value, "value");
      }
    }

    /// <summary>Field number for the "position" field.</summary>
    public const int PositionFieldNumber = 2;
    private long position_;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public long Position {
      get { return position_; }
      set {
        position_ = value;
      }
    }

    /// <summary>Field number for the "outfit" field.</summary>
    public const int OutfitFieldNumber = 3;
    private long outfit_;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public long Outfit {
      get { return outfit_; }
      set {
        outfit_ = value;
      }
    }

    /// <summary>Field number for the "rank" field.</summary>
    public const int RankFieldNumber = 4;
    private int rank_;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public int Rank {
      get { return rank_; }
      set {
        rank_ = value;
      }
    }

    /// <summary>Field number for the "points" field.</summary>
    public const int PointsFieldNumber = 5;
    private long points_;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public long Points {
      get { return points_; }
      set {
        points_ = value;
      }
    }

    /// <summary>Field number for the "nickname" field.</summary>
    public const int NicknameFieldNumber = 6;
    private string nickname_ = "";
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public string Nickname {
      get { return nickname_; }
      set {
        nickname_ = pb::ProtoPreconditions.CheckNotNull(value, "value");
      }
    }

    /// <summary>Field number for the "country" field.</summary>
    public const int CountryFieldNumber = 7;
    private string country_ = "";
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public string Country {
      get { return country_; }
      set {
        country_ = pb::ProtoPreconditions.CheckNotNull(value, "value");
      }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override bool Equals(object other) {
      return Equals(other as AccountData);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public bool Equals(AccountData other) {
      if (ReferenceEquals(other, null)) {
        return false;
      }
      if (ReferenceEquals(other, this)) {
        return true;
      }
      if (UserId != other.UserId) return false;
      if (Position != other.Position) return false;
      if (Outfit != other.Outfit) return false;
      if (Rank != other.Rank) return false;
      if (Points != other.Points) return false;
      if (Nickname != other.Nickname) return false;
      if (Country != other.Country) return false;
      return Equals(_unknownFields, other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override int GetHashCode() {
      int hash = 1;
      if (UserId.Length != 0) hash ^= UserId.GetHashCode();
      if (Position != 0L) hash ^= Position.GetHashCode();
      if (Outfit != 0L) hash ^= Outfit.GetHashCode();
      if (Rank != 0) hash ^= Rank.GetHashCode();
      if (Points != 0L) hash ^= Points.GetHashCode();
      if (Nickname.Length != 0) hash ^= Nickname.GetHashCode();
      if (Country.Length != 0) hash ^= Country.GetHashCode();
      if (_unknownFields != null) {
        hash ^= _unknownFields.GetHashCode();
      }
      return hash;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override string ToString() {
      return pb::JsonFormatter.ToDiagnosticString(this);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void WriteTo(pb::CodedOutputStream output) {
    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      output.WriteRawMessage(this);
    #else
      if (UserId.Length != 0) {
        output.WriteRawTag(10);
        output.WriteString(UserId);
      }
      if (Position != 0L) {
        output.WriteRawTag(16);
        output.WriteInt64(Position);
      }
      if (Outfit != 0L) {
        output.WriteRawTag(24);
        output.WriteInt64(Outfit);
      }
      if (Rank != 0) {
        output.WriteRawTag(32);
        output.WriteInt32(Rank);
      }
      if (Points != 0L) {
        output.WriteRawTag(40);
        output.WriteInt64(Points);
      }
      if (Nickname.Length != 0) {
        output.WriteRawTag(50);
        output.WriteString(Nickname);
      }
      if (Country.Length != 0) {
        output.WriteRawTag(58);
        output.WriteString(Country);
      }
      if (_unknownFields != null) {
        _unknownFields.WriteTo(output);
      }
    #endif
    }

    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    void pb::IBufferMessage.InternalWriteTo(ref pb::WriteContext output) {
      if (UserId.Length != 0) {
        output.WriteRawTag(10);
        output.WriteString(UserId);
      }
      if (Position != 0L) {
        output.WriteRawTag(16);
        output.WriteInt64(Position);
      }
      if (Outfit != 0L) {
        output.WriteRawTag(24);
        output.WriteInt64(Outfit);
      }
      if (Rank != 0) {
        output.WriteRawTag(32);
        output.WriteInt32(Rank);
      }
      if (Points != 0L) {
        output.WriteRawTag(40);
        output.WriteInt64(Points);
      }
      if (Nickname.Length != 0) {
        output.WriteRawTag(50);
        output.WriteString(Nickname);
      }
      if (Country.Length != 0) {
        output.WriteRawTag(58);
        output.WriteString(Country);
      }
      if (_unknownFields != null) {
        _unknownFields.WriteTo(ref output);
      }
    }
    #endif

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public int CalculateSize() {
      int size = 0;
      if (UserId.Length != 0) {
        size += 1 + pb::CodedOutputStream.ComputeStringSize(UserId);
      }
      if (Position != 0L) {
        size += 1 + pb::CodedOutputStream.ComputeInt64Size(Position);
      }
      if (Outfit != 0L) {
        size += 1 + pb::CodedOutputStream.ComputeInt64Size(Outfit);
      }
      if (Rank != 0) {
        size += 1 + pb::CodedOutputStream.ComputeInt32Size(Rank);
      }
      if (Points != 0L) {
        size += 1 + pb::CodedOutputStream.ComputeInt64Size(Points);
      }
      if (Nickname.Length != 0) {
        size += 1 + pb::CodedOutputStream.ComputeStringSize(Nickname);
      }
      if (Country.Length != 0) {
        size += 1 + pb::CodedOutputStream.ComputeStringSize(Country);
      }
      if (_unknownFields != null) {
        size += _unknownFields.CalculateSize();
      }
      return size;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void MergeFrom(AccountData other) {
      if (other == null) {
        return;
      }
      if (other.UserId.Length != 0) {
        UserId = other.UserId;
      }
      if (other.Position != 0L) {
        Position = other.Position;
      }
      if (other.Outfit != 0L) {
        Outfit = other.Outfit;
      }
      if (other.Rank != 0) {
        Rank = other.Rank;
      }
      if (other.Points != 0L) {
        Points = other.Points;
      }
      if (other.Nickname.Length != 0) {
        Nickname = other.Nickname;
      }
      if (other.Country.Length != 0) {
        Country = other.Country;
      }
      _unknownFields = pb::UnknownFieldSet.MergeFrom(_unknownFields, other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void MergeFrom(pb::CodedInputStream input) {
    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      input.ReadRawMessage(this);
    #else
      uint tag;
      while ((tag = input.ReadTag()) != 0) {
        switch(tag) {
          default:
            _unknownFields = pb::UnknownFieldSet.MergeFieldFrom(_unknownFields, input);
            break;
          case 10: {
            UserId = input.ReadString();
            break;
          }
          case 16: {
            Position = input.ReadInt64();
            break;
          }
          case 24: {
            Outfit = input.ReadInt64();
            break;
          }
          case 32: {
            Rank = input.ReadInt32();
            break;
          }
          case 40: {
            Points = input.ReadInt64();
            break;
          }
          case 50: {
            Nickname = input.ReadString();
            break;
          }
          case 58: {
            Country = input.ReadString();
            break;
          }
        }
      }
    #endif
    }

    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    void pb::IBufferMessage.InternalMergeFrom(ref pb::ParseContext input) {
      uint tag;
      while ((tag = input.ReadTag()) != 0) {
        switch(tag) {
          default:
            _unknownFields = pb::UnknownFieldSet.MergeFieldFrom(_unknownFields, ref input);
            break;
          case 10: {
            UserId = input.ReadString();
            break;
          }
          case 16: {
            Position = input.ReadInt64();
            break;
          }
          case 24: {
            Outfit = input.ReadInt64();
            break;
          }
          case 32: {
            Rank = input.ReadInt32();
            break;
          }
          case 40: {
            Points = input.ReadInt64();
            break;
          }
          case 50: {
            Nickname = input.ReadString();
            break;
          }
          case 58: {
            Country = input.ReadString();
            break;
          }
        }
      }
    }
    #endif

  }

  [global::System.Diagnostics.DebuggerDisplayAttribute("{ToString(),nq}")]
  public sealed partial class AccountAbility : pb::IMessage<AccountAbility>
  #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      , pb::IBufferMessage
  #endif
  {
    private static readonly pb::MessageParser<AccountAbility> _parser = new pb::MessageParser<AccountAbility>(() => new AccountAbility());
    private pb::UnknownFieldSet _unknownFields;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public static pb::MessageParser<AccountAbility> Parser { get { return _parser; } }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public static pbr::MessageDescriptor Descriptor {
      get { return global::Common.CommonReflection.Descriptor.MessageTypes[6]; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    pbr::MessageDescriptor pb::IMessage.Descriptor {
      get { return Descriptor; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public AccountAbility() {
      OnConstruction();
    }

    partial void OnConstruction();

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public AccountAbility(AccountAbility other) : this() {
      id_ = other.id_;
      grade_ = other.grade_;
      _unknownFields = pb::UnknownFieldSet.Clone(other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public AccountAbility Clone() {
      return new AccountAbility(this);
    }

    /// <summary>Field number for the "id" field.</summary>
    public const int IdFieldNumber = 1;
    private int id_;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public int Id {
      get { return id_; }
      set {
        id_ = value;
      }
    }

    /// <summary>Field number for the "grade" field.</summary>
    public const int GradeFieldNumber = 2;
    private int grade_;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public int Grade {
      get { return grade_; }
      set {
        grade_ = value;
      }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override bool Equals(object other) {
      return Equals(other as AccountAbility);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public bool Equals(AccountAbility other) {
      if (ReferenceEquals(other, null)) {
        return false;
      }
      if (ReferenceEquals(other, this)) {
        return true;
      }
      if (Id != other.Id) return false;
      if (Grade != other.Grade) return false;
      return Equals(_unknownFields, other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override int GetHashCode() {
      int hash = 1;
      if (Id != 0) hash ^= Id.GetHashCode();
      if (Grade != 0) hash ^= Grade.GetHashCode();
      if (_unknownFields != null) {
        hash ^= _unknownFields.GetHashCode();
      }
      return hash;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override string ToString() {
      return pb::JsonFormatter.ToDiagnosticString(this);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void WriteTo(pb::CodedOutputStream output) {
    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      output.WriteRawMessage(this);
    #else
      if (Id != 0) {
        output.WriteRawTag(8);
        output.WriteInt32(Id);
      }
      if (Grade != 0) {
        output.WriteRawTag(16);
        output.WriteInt32(Grade);
      }
      if (_unknownFields != null) {
        _unknownFields.WriteTo(output);
      }
    #endif
    }

    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    void pb::IBufferMessage.InternalWriteTo(ref pb::WriteContext output) {
      if (Id != 0) {
        output.WriteRawTag(8);
        output.WriteInt32(Id);
      }
      if (Grade != 0) {
        output.WriteRawTag(16);
        output.WriteInt32(Grade);
      }
      if (_unknownFields != null) {
        _unknownFields.WriteTo(ref output);
      }
    }
    #endif

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public int CalculateSize() {
      int size = 0;
      if (Id != 0) {
        size += 1 + pb::CodedOutputStream.ComputeInt32Size(Id);
      }
      if (Grade != 0) {
        size += 1 + pb::CodedOutputStream.ComputeInt32Size(Grade);
      }
      if (_unknownFields != null) {
        size += _unknownFields.CalculateSize();
      }
      return size;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void MergeFrom(AccountAbility other) {
      if (other == null) {
        return;
      }
      if (other.Id != 0) {
        Id = other.Id;
      }
      if (other.Grade != 0) {
        Grade = other.Grade;
      }
      _unknownFields = pb::UnknownFieldSet.MergeFrom(_unknownFields, other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void MergeFrom(pb::CodedInputStream input) {
    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      input.ReadRawMessage(this);
    #else
      uint tag;
      while ((tag = input.ReadTag()) != 0) {
        switch(tag) {
          default:
            _unknownFields = pb::UnknownFieldSet.MergeFieldFrom(_unknownFields, input);
            break;
          case 8: {
            Id = input.ReadInt32();
            break;
          }
          case 16: {
            Grade = input.ReadInt32();
            break;
          }
        }
      }
    #endif
    }

    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    void pb::IBufferMessage.InternalMergeFrom(ref pb::ParseContext input) {
      uint tag;
      while ((tag = input.ReadTag()) != 0) {
        switch(tag) {
          default:
            _unknownFields = pb::UnknownFieldSet.MergeFieldFrom(_unknownFields, ref input);
            break;
          case 8: {
            Id = input.ReadInt32();
            break;
          }
          case 16: {
            Grade = input.ReadInt32();
            break;
          }
        }
      }
    }
    #endif

  }

  [global::System.Diagnostics.DebuggerDisplayAttribute("{ToString(),nq}")]
  public sealed partial class TopResponse : pb::IMessage<TopResponse>
  #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      , pb::IBufferMessage
  #endif
  {
    private static readonly pb::MessageParser<TopResponse> _parser = new pb::MessageParser<TopResponse>(() => new TopResponse());
    private pb::UnknownFieldSet _unknownFields;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public static pb::MessageParser<TopResponse> Parser { get { return _parser; } }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public static pbr::MessageDescriptor Descriptor {
      get { return global::Common.CommonReflection.Descriptor.MessageTypes[7]; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    pbr::MessageDescriptor pb::IMessage.Descriptor {
      get { return Descriptor; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public TopResponse() {
      OnConstruction();
    }

    partial void OnConstruction();

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public TopResponse(TopResponse other) : this() {
      items_ = other.items_.Clone();
      _unknownFields = pb::UnknownFieldSet.Clone(other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public TopResponse Clone() {
      return new TopResponse(this);
    }

    /// <summary>Field number for the "items" field.</summary>
    public const int ItemsFieldNumber = 1;
    private static readonly pb::FieldCodec<global::Common.AccountData> _repeated_items_codec
        = pb::FieldCodec.ForMessage(10, global::Common.AccountData.Parser);
    private readonly pbc::RepeatedField<global::Common.AccountData> items_ = new pbc::RepeatedField<global::Common.AccountData>();
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public pbc::RepeatedField<global::Common.AccountData> Items {
      get { return items_; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override bool Equals(object other) {
      return Equals(other as TopResponse);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public bool Equals(TopResponse other) {
      if (ReferenceEquals(other, null)) {
        return false;
      }
      if (ReferenceEquals(other, this)) {
        return true;
      }
      if(!items_.Equals(other.items_)) return false;
      return Equals(_unknownFields, other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override int GetHashCode() {
      int hash = 1;
      hash ^= items_.GetHashCode();
      if (_unknownFields != null) {
        hash ^= _unknownFields.GetHashCode();
      }
      return hash;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override string ToString() {
      return pb::JsonFormatter.ToDiagnosticString(this);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void WriteTo(pb::CodedOutputStream output) {
    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      output.WriteRawMessage(this);
    #else
      items_.WriteTo(output, _repeated_items_codec);
      if (_unknownFields != null) {
        _unknownFields.WriteTo(output);
      }
    #endif
    }

    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    void pb::IBufferMessage.InternalWriteTo(ref pb::WriteContext output) {
      items_.WriteTo(ref output, _repeated_items_codec);
      if (_unknownFields != null) {
        _unknownFields.WriteTo(ref output);
      }
    }
    #endif

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public int CalculateSize() {
      int size = 0;
      size += items_.CalculateSize(_repeated_items_codec);
      if (_unknownFields != null) {
        size += _unknownFields.CalculateSize();
      }
      return size;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void MergeFrom(TopResponse other) {
      if (other == null) {
        return;
      }
      items_.Add(other.items_);
      _unknownFields = pb::UnknownFieldSet.MergeFrom(_unknownFields, other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void MergeFrom(pb::CodedInputStream input) {
    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      input.ReadRawMessage(this);
    #else
      uint tag;
      while ((tag = input.ReadTag()) != 0) {
        switch(tag) {
          default:
            _unknownFields = pb::UnknownFieldSet.MergeFieldFrom(_unknownFields, input);
            break;
          case 10: {
            items_.AddEntriesFrom(input, _repeated_items_codec);
            break;
          }
        }
      }
    #endif
    }

    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    void pb::IBufferMessage.InternalMergeFrom(ref pb::ParseContext input) {
      uint tag;
      while ((tag = input.ReadTag()) != 0) {
        switch(tag) {
          default:
            _unknownFields = pb::UnknownFieldSet.MergeFieldFrom(_unknownFields, ref input);
            break;
          case 10: {
            items_.AddEntriesFrom(ref input, _repeated_items_codec);
            break;
          }
        }
      }
    }
    #endif

  }

  [global::System.Diagnostics.DebuggerDisplayAttribute("{ToString(),nq}")]
  public sealed partial class UserRequest : pb::IMessage<UserRequest>
  #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      , pb::IBufferMessage
  #endif
  {
    private static readonly pb::MessageParser<UserRequest> _parser = new pb::MessageParser<UserRequest>(() => new UserRequest());
    private pb::UnknownFieldSet _unknownFields;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public static pb::MessageParser<UserRequest> Parser { get { return _parser; } }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public static pbr::MessageDescriptor Descriptor {
      get { return global::Common.CommonReflection.Descriptor.MessageTypes[8]; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    pbr::MessageDescriptor pb::IMessage.Descriptor {
      get { return Descriptor; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public UserRequest() {
      OnConstruction();
    }

    partial void OnConstruction();

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public UserRequest(UserRequest other) : this() {
      userId_ = other.userId_;
      _unknownFields = pb::UnknownFieldSet.Clone(other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public UserRequest Clone() {
      return new UserRequest(this);
    }

    /// <summary>Field number for the "user_id" field.</summary>
    public const int UserIdFieldNumber = 1;
    private string userId_ = "";
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public string UserId {
      get { return userId_; }
      set {
        userId_ = pb::ProtoPreconditions.CheckNotNull(value, "value");
      }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override bool Equals(object other) {
      return Equals(other as UserRequest);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public bool Equals(UserRequest other) {
      if (ReferenceEquals(other, null)) {
        return false;
      }
      if (ReferenceEquals(other, this)) {
        return true;
      }
      if (UserId != other.UserId) return false;
      return Equals(_unknownFields, other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override int GetHashCode() {
      int hash = 1;
      if (UserId.Length != 0) hash ^= UserId.GetHashCode();
      if (_unknownFields != null) {
        hash ^= _unknownFields.GetHashCode();
      }
      return hash;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override string ToString() {
      return pb::JsonFormatter.ToDiagnosticString(this);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void WriteTo(pb::CodedOutputStream output) {
    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      output.WriteRawMessage(this);
    #else
      if (UserId.Length != 0) {
        output.WriteRawTag(10);
        output.WriteString(UserId);
      }
      if (_unknownFields != null) {
        _unknownFields.WriteTo(output);
      }
    #endif
    }

    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    void pb::IBufferMessage.InternalWriteTo(ref pb::WriteContext output) {
      if (UserId.Length != 0) {
        output.WriteRawTag(10);
        output.WriteString(UserId);
      }
      if (_unknownFields != null) {
        _unknownFields.WriteTo(ref output);
      }
    }
    #endif

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public int CalculateSize() {
      int size = 0;
      if (UserId.Length != 0) {
        size += 1 + pb::CodedOutputStream.ComputeStringSize(UserId);
      }
      if (_unknownFields != null) {
        size += _unknownFields.CalculateSize();
      }
      return size;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void MergeFrom(UserRequest other) {
      if (other == null) {
        return;
      }
      if (other.UserId.Length != 0) {
        UserId = other.UserId;
      }
      _unknownFields = pb::UnknownFieldSet.MergeFrom(_unknownFields, other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void MergeFrom(pb::CodedInputStream input) {
    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      input.ReadRawMessage(this);
    #else
      uint tag;
      while ((tag = input.ReadTag()) != 0) {
        switch(tag) {
          default:
            _unknownFields = pb::UnknownFieldSet.MergeFieldFrom(_unknownFields, input);
            break;
          case 10: {
            UserId = input.ReadString();
            break;
          }
        }
      }
    #endif
    }

    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    void pb::IBufferMessage.InternalMergeFrom(ref pb::ParseContext input) {
      uint tag;
      while ((tag = input.ReadTag()) != 0) {
        switch(tag) {
          default:
            _unknownFields = pb::UnknownFieldSet.MergeFieldFrom(_unknownFields, ref input);
            break;
          case 10: {
            UserId = input.ReadString();
            break;
          }
        }
      }
    }
    #endif

  }

  [global::System.Diagnostics.DebuggerDisplayAttribute("{ToString(),nq}")]
  public sealed partial class OptionalField : pb::IMessage<OptionalField>
  #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      , pb::IBufferMessage
  #endif
  {
    private static readonly pb::MessageParser<OptionalField> _parser = new pb::MessageParser<OptionalField>(() => new OptionalField());
    private pb::UnknownFieldSet _unknownFields;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public static pb::MessageParser<OptionalField> Parser { get { return _parser; } }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public static pbr::MessageDescriptor Descriptor {
      get { return global::Common.CommonReflection.Descriptor.MessageTypes[9]; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    pbr::MessageDescriptor pb::IMessage.Descriptor {
      get { return Descriptor; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public OptionalField() {
      OnConstruction();
    }

    partial void OnConstruction();

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public OptionalField(OptionalField other) : this() {
      switch (other.KindCase) {
        case KindOneofCase.Number:
          Number = other.Number;
          break;
        case KindOneofCase.Boolean:
          Boolean = other.Boolean;
          break;
        case KindOneofCase.String:
          String = other.String;
          break;
      }

      _unknownFields = pb::UnknownFieldSet.Clone(other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public OptionalField Clone() {
      return new OptionalField(this);
    }

    /// <summary>Field number for the "number" field.</summary>
    public const int NumberFieldNumber = 1;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public long Number {
      get { return HasNumber ? (long) kind_ : 0L; }
      set {
        kind_ = value;
        kindCase_ = KindOneofCase.Number;
      }
    }
    /// <summary>Gets whether the "number" field is set</summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public bool HasNumber {
      get { return kindCase_ == KindOneofCase.Number; }
    }
    /// <summary> Clears the value of the oneof if it's currently set to "number" </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void ClearNumber() {
      if (HasNumber) {
        ClearKind();
      }
    }

    /// <summary>Field number for the "boolean" field.</summary>
    public const int BooleanFieldNumber = 2;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public bool Boolean {
      get { return HasBoolean ? (bool) kind_ : false; }
      set {
        kind_ = value;
        kindCase_ = KindOneofCase.Boolean;
      }
    }
    /// <summary>Gets whether the "boolean" field is set</summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public bool HasBoolean {
      get { return kindCase_ == KindOneofCase.Boolean; }
    }
    /// <summary> Clears the value of the oneof if it's currently set to "boolean" </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void ClearBoolean() {
      if (HasBoolean) {
        ClearKind();
      }
    }

    /// <summary>Field number for the "string" field.</summary>
    public const int StringFieldNumber = 3;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public string String {
      get { return HasString ? (string) kind_ : ""; }
      set {
        kind_ = pb::ProtoPreconditions.CheckNotNull(value, "value");
        kindCase_ = KindOneofCase.String;
      }
    }
    /// <summary>Gets whether the "string" field is set</summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public bool HasString {
      get { return kindCase_ == KindOneofCase.String; }
    }
    /// <summary> Clears the value of the oneof if it's currently set to "string" </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void ClearString() {
      if (HasString) {
        ClearKind();
      }
    }

    private object kind_;
    /// <summary>Enum of possible cases for the "kind" oneof.</summary>
    public enum KindOneofCase {
      None = 0,
      Number = 1,
      Boolean = 2,
      String = 3,
    }
    private KindOneofCase kindCase_ = KindOneofCase.None;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public KindOneofCase KindCase {
      get { return kindCase_; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void ClearKind() {
      kindCase_ = KindOneofCase.None;
      kind_ = null;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override bool Equals(object other) {
      return Equals(other as OptionalField);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public bool Equals(OptionalField other) {
      if (ReferenceEquals(other, null)) {
        return false;
      }
      if (ReferenceEquals(other, this)) {
        return true;
      }
      if (Number != other.Number) return false;
      if (Boolean != other.Boolean) return false;
      if (String != other.String) return false;
      if (KindCase != other.KindCase) return false;
      return Equals(_unknownFields, other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override int GetHashCode() {
      int hash = 1;
      if (HasNumber) hash ^= Number.GetHashCode();
      if (HasBoolean) hash ^= Boolean.GetHashCode();
      if (HasString) hash ^= String.GetHashCode();
      hash ^= (int) kindCase_;
      if (_unknownFields != null) {
        hash ^= _unknownFields.GetHashCode();
      }
      return hash;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override string ToString() {
      return pb::JsonFormatter.ToDiagnosticString(this);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void WriteTo(pb::CodedOutputStream output) {
    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      output.WriteRawMessage(this);
    #else
      if (HasNumber) {
        output.WriteRawTag(8);
        output.WriteInt64(Number);
      }
      if (HasBoolean) {
        output.WriteRawTag(16);
        output.WriteBool(Boolean);
      }
      if (HasString) {
        output.WriteRawTag(26);
        output.WriteString(String);
      }
      if (_unknownFields != null) {
        _unknownFields.WriteTo(output);
      }
    #endif
    }

    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    void pb::IBufferMessage.InternalWriteTo(ref pb::WriteContext output) {
      if (HasNumber) {
        output.WriteRawTag(8);
        output.WriteInt64(Number);
      }
      if (HasBoolean) {
        output.WriteRawTag(16);
        output.WriteBool(Boolean);
      }
      if (HasString) {
        output.WriteRawTag(26);
        output.WriteString(String);
      }
      if (_unknownFields != null) {
        _unknownFields.WriteTo(ref output);
      }
    }
    #endif

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public int CalculateSize() {
      int size = 0;
      if (HasNumber) {
        size += 1 + pb::CodedOutputStream.ComputeInt64Size(Number);
      }
      if (HasBoolean) {
        size += 1 + 1;
      }
      if (HasString) {
        size += 1 + pb::CodedOutputStream.ComputeStringSize(String);
      }
      if (_unknownFields != null) {
        size += _unknownFields.CalculateSize();
      }
      return size;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void MergeFrom(OptionalField other) {
      if (other == null) {
        return;
      }
      switch (other.KindCase) {
        case KindOneofCase.Number:
          Number = other.Number;
          break;
        case KindOneofCase.Boolean:
          Boolean = other.Boolean;
          break;
        case KindOneofCase.String:
          String = other.String;
          break;
      }

      _unknownFields = pb::UnknownFieldSet.MergeFrom(_unknownFields, other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void MergeFrom(pb::CodedInputStream input) {
    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      input.ReadRawMessage(this);
    #else
      uint tag;
      while ((tag = input.ReadTag()) != 0) {
        switch(tag) {
          default:
            _unknownFields = pb::UnknownFieldSet.MergeFieldFrom(_unknownFields, input);
            break;
          case 8: {
            Number = input.ReadInt64();
            break;
          }
          case 16: {
            Boolean = input.ReadBool();
            break;
          }
          case 26: {
            String = input.ReadString();
            break;
          }
        }
      }
    #endif
    }

    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    void pb::IBufferMessage.InternalMergeFrom(ref pb::ParseContext input) {
      uint tag;
      while ((tag = input.ReadTag()) != 0) {
        switch(tag) {
          default:
            _unknownFields = pb::UnknownFieldSet.MergeFieldFrom(_unknownFields, ref input);
            break;
          case 8: {
            Number = input.ReadInt64();
            break;
          }
          case 16: {
            Boolean = input.ReadBool();
            break;
          }
          case 26: {
            String = input.ReadString();
            break;
          }
        }
      }
    }
    #endif

  }

  [global::System.Diagnostics.DebuggerDisplayAttribute("{ToString(),nq}")]
  public sealed partial class UserFingerprint : pb::IMessage<UserFingerprint>
  #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      , pb::IBufferMessage
  #endif
  {
    private static readonly pb::MessageParser<UserFingerprint> _parser = new pb::MessageParser<UserFingerprint>(() => new UserFingerprint());
    private pb::UnknownFieldSet _unknownFields;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public static pb::MessageParser<UserFingerprint> Parser { get { return _parser; } }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public static pbr::MessageDescriptor Descriptor {
      get { return global::Common.CommonReflection.Descriptor.MessageTypes[10]; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    pbr::MessageDescriptor pb::IMessage.Descriptor {
      get { return Descriptor; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public UserFingerprint() {
      OnConstruction();
    }

    partial void OnConstruction();

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public UserFingerprint(UserFingerprint other) : this() {
      countryCode_ = other.countryCode_;
      countryName_ = other.countryName_;
      cityName_ = other.cityName_;
      timezone_ = other.timezone_;
      _unknownFields = pb::UnknownFieldSet.Clone(other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public UserFingerprint Clone() {
      return new UserFingerprint(this);
    }

    /// <summary>Field number for the "country_code" field.</summary>
    public const int CountryCodeFieldNumber = 1;
    private string countryCode_ = "";
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public string CountryCode {
      get { return countryCode_; }
      set {
        countryCode_ = pb::ProtoPreconditions.CheckNotNull(value, "value");
      }
    }

    /// <summary>Field number for the "country_name" field.</summary>
    public const int CountryNameFieldNumber = 2;
    private string countryName_ = "";
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public string CountryName {
      get { return countryName_; }
      set {
        countryName_ = pb::ProtoPreconditions.CheckNotNull(value, "value");
      }
    }

    /// <summary>Field number for the "city_name" field.</summary>
    public const int CityNameFieldNumber = 3;
    private string cityName_ = "";
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public string CityName {
      get { return cityName_; }
      set {
        cityName_ = pb::ProtoPreconditions.CheckNotNull(value, "value");
      }
    }

    /// <summary>Field number for the "timezone" field.</summary>
    public const int TimezoneFieldNumber = 4;
    private string timezone_ = "";
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public string Timezone {
      get { return timezone_; }
      set {
        timezone_ = pb::ProtoPreconditions.CheckNotNull(value, "value");
      }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override bool Equals(object other) {
      return Equals(other as UserFingerprint);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public bool Equals(UserFingerprint other) {
      if (ReferenceEquals(other, null)) {
        return false;
      }
      if (ReferenceEquals(other, this)) {
        return true;
      }
      if (CountryCode != other.CountryCode) return false;
      if (CountryName != other.CountryName) return false;
      if (CityName != other.CityName) return false;
      if (Timezone != other.Timezone) return false;
      return Equals(_unknownFields, other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override int GetHashCode() {
      int hash = 1;
      if (CountryCode.Length != 0) hash ^= CountryCode.GetHashCode();
      if (CountryName.Length != 0) hash ^= CountryName.GetHashCode();
      if (CityName.Length != 0) hash ^= CityName.GetHashCode();
      if (Timezone.Length != 0) hash ^= Timezone.GetHashCode();
      if (_unknownFields != null) {
        hash ^= _unknownFields.GetHashCode();
      }
      return hash;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override string ToString() {
      return pb::JsonFormatter.ToDiagnosticString(this);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void WriteTo(pb::CodedOutputStream output) {
    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      output.WriteRawMessage(this);
    #else
      if (CountryCode.Length != 0) {
        output.WriteRawTag(10);
        output.WriteString(CountryCode);
      }
      if (CountryName.Length != 0) {
        output.WriteRawTag(18);
        output.WriteString(CountryName);
      }
      if (CityName.Length != 0) {
        output.WriteRawTag(26);
        output.WriteString(CityName);
      }
      if (Timezone.Length != 0) {
        output.WriteRawTag(34);
        output.WriteString(Timezone);
      }
      if (_unknownFields != null) {
        _unknownFields.WriteTo(output);
      }
    #endif
    }

    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    void pb::IBufferMessage.InternalWriteTo(ref pb::WriteContext output) {
      if (CountryCode.Length != 0) {
        output.WriteRawTag(10);
        output.WriteString(CountryCode);
      }
      if (CountryName.Length != 0) {
        output.WriteRawTag(18);
        output.WriteString(CountryName);
      }
      if (CityName.Length != 0) {
        output.WriteRawTag(26);
        output.WriteString(CityName);
      }
      if (Timezone.Length != 0) {
        output.WriteRawTag(34);
        output.WriteString(Timezone);
      }
      if (_unknownFields != null) {
        _unknownFields.WriteTo(ref output);
      }
    }
    #endif

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public int CalculateSize() {
      int size = 0;
      if (CountryCode.Length != 0) {
        size += 1 + pb::CodedOutputStream.ComputeStringSize(CountryCode);
      }
      if (CountryName.Length != 0) {
        size += 1 + pb::CodedOutputStream.ComputeStringSize(CountryName);
      }
      if (CityName.Length != 0) {
        size += 1 + pb::CodedOutputStream.ComputeStringSize(CityName);
      }
      if (Timezone.Length != 0) {
        size += 1 + pb::CodedOutputStream.ComputeStringSize(Timezone);
      }
      if (_unknownFields != null) {
        size += _unknownFields.CalculateSize();
      }
      return size;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void MergeFrom(UserFingerprint other) {
      if (other == null) {
        return;
      }
      if (other.CountryCode.Length != 0) {
        CountryCode = other.CountryCode;
      }
      if (other.CountryName.Length != 0) {
        CountryName = other.CountryName;
      }
      if (other.CityName.Length != 0) {
        CityName = other.CityName;
      }
      if (other.Timezone.Length != 0) {
        Timezone = other.Timezone;
      }
      _unknownFields = pb::UnknownFieldSet.MergeFrom(_unknownFields, other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void MergeFrom(pb::CodedInputStream input) {
    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      input.ReadRawMessage(this);
    #else
      uint tag;
      while ((tag = input.ReadTag()) != 0) {
        switch(tag) {
          default:
            _unknownFields = pb::UnknownFieldSet.MergeFieldFrom(_unknownFields, input);
            break;
          case 10: {
            CountryCode = input.ReadString();
            break;
          }
          case 18: {
            CountryName = input.ReadString();
            break;
          }
          case 26: {
            CityName = input.ReadString();
            break;
          }
          case 34: {
            Timezone = input.ReadString();
            break;
          }
        }
      }
    #endif
    }

    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    void pb::IBufferMessage.InternalMergeFrom(ref pb::ParseContext input) {
      uint tag;
      while ((tag = input.ReadTag()) != 0) {
        switch(tag) {
          default:
            _unknownFields = pb::UnknownFieldSet.MergeFieldFrom(_unknownFields, ref input);
            break;
          case 10: {
            CountryCode = input.ReadString();
            break;
          }
          case 18: {
            CountryName = input.ReadString();
            break;
          }
          case 26: {
            CityName = input.ReadString();
            break;
          }
          case 34: {
            Timezone = input.ReadString();
            break;
          }
        }
      }
    }
    #endif

  }

  [global::System.Diagnostics.DebuggerDisplayAttribute("{ToString(),nq}")]
  public sealed partial class BloodReward : pb::IMessage<BloodReward>
  #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      , pb::IBufferMessage
  #endif
  {
    private static readonly pb::MessageParser<BloodReward> _parser = new pb::MessageParser<BloodReward>(() => new BloodReward());
    private pb::UnknownFieldSet _unknownFields;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public static pb::MessageParser<BloodReward> Parser { get { return _parser; } }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public static pbr::MessageDescriptor Descriptor {
      get { return global::Common.CommonReflection.Descriptor.MessageTypes[11]; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    pbr::MessageDescriptor pb::IMessage.Descriptor {
      get { return Descriptor; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public BloodReward() {
      OnConstruction();
    }

    partial void OnConstruction();

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public BloodReward(BloodReward other) : this() {
      id_ = other.id_;
      item_ = other.item_.Clone();
      description_ = other.description_;
      price_ = other.price_;
      regionId_ = other.regionId_;
      parts_ = other.parts_;
      _unknownFields = pb::UnknownFieldSet.Clone(other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public BloodReward Clone() {
      return new BloodReward(this);
    }

    /// <summary>Field number for the "id" field.</summary>
    public const int IdFieldNumber = 1;
    private long id_;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public long Id {
      get { return id_; }
      set {
        id_ = value;
      }
    }

    /// <summary>Field number for the "item" field.</summary>
    public const int ItemFieldNumber = 2;
    private static readonly pb::FieldCodec<long> _repeated_item_codec
        = pb::FieldCodec.ForInt64(18);
    private readonly pbc::RepeatedField<long> item_ = new pbc::RepeatedField<long>();
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public pbc::RepeatedField<long> Item {
      get { return item_; }
    }

    /// <summary>Field number for the "description" field.</summary>
    public const int DescriptionFieldNumber = 3;
    private string description_ = "";
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public string Description {
      get { return description_; }
      set {
        description_ = pb::ProtoPreconditions.CheckNotNull(value, "value");
      }
    }

    /// <summary>Field number for the "price" field.</summary>
    public const int PriceFieldNumber = 4;
    private long price_;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public long Price {
      get { return price_; }
      set {
        price_ = value;
      }
    }

    /// <summary>Field number for the "region_id" field.</summary>
    public const int RegionIdFieldNumber = 5;
    private int regionId_;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public int RegionId {
      get { return regionId_; }
      set {
        regionId_ = value;
      }
    }

    /// <summary>Field number for the "parts" field.</summary>
    public const int PartsFieldNumber = 6;
    private int parts_;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public int Parts {
      get { return parts_; }
      set {
        parts_ = value;
      }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override bool Equals(object other) {
      return Equals(other as BloodReward);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public bool Equals(BloodReward other) {
      if (ReferenceEquals(other, null)) {
        return false;
      }
      if (ReferenceEquals(other, this)) {
        return true;
      }
      if (Id != other.Id) return false;
      if(!item_.Equals(other.item_)) return false;
      if (Description != other.Description) return false;
      if (Price != other.Price) return false;
      if (RegionId != other.RegionId) return false;
      if (Parts != other.Parts) return false;
      return Equals(_unknownFields, other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override int GetHashCode() {
      int hash = 1;
      if (Id != 0L) hash ^= Id.GetHashCode();
      hash ^= item_.GetHashCode();
      if (Description.Length != 0) hash ^= Description.GetHashCode();
      if (Price != 0L) hash ^= Price.GetHashCode();
      if (RegionId != 0) hash ^= RegionId.GetHashCode();
      if (Parts != 0) hash ^= Parts.GetHashCode();
      if (_unknownFields != null) {
        hash ^= _unknownFields.GetHashCode();
      }
      return hash;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override string ToString() {
      return pb::JsonFormatter.ToDiagnosticString(this);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void WriteTo(pb::CodedOutputStream output) {
    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      output.WriteRawMessage(this);
    #else
      if (Id != 0L) {
        output.WriteRawTag(8);
        output.WriteInt64(Id);
      }
      item_.WriteTo(output, _repeated_item_codec);
      if (Description.Length != 0) {
        output.WriteRawTag(26);
        output.WriteString(Description);
      }
      if (Price != 0L) {
        output.WriteRawTag(32);
        output.WriteInt64(Price);
      }
      if (RegionId != 0) {
        output.WriteRawTag(40);
        output.WriteInt32(RegionId);
      }
      if (Parts != 0) {
        output.WriteRawTag(48);
        output.WriteInt32(Parts);
      }
      if (_unknownFields != null) {
        _unknownFields.WriteTo(output);
      }
    #endif
    }

    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    void pb::IBufferMessage.InternalWriteTo(ref pb::WriteContext output) {
      if (Id != 0L) {
        output.WriteRawTag(8);
        output.WriteInt64(Id);
      }
      item_.WriteTo(ref output, _repeated_item_codec);
      if (Description.Length != 0) {
        output.WriteRawTag(26);
        output.WriteString(Description);
      }
      if (Price != 0L) {
        output.WriteRawTag(32);
        output.WriteInt64(Price);
      }
      if (RegionId != 0) {
        output.WriteRawTag(40);
        output.WriteInt32(RegionId);
      }
      if (Parts != 0) {
        output.WriteRawTag(48);
        output.WriteInt32(Parts);
      }
      if (_unknownFields != null) {
        _unknownFields.WriteTo(ref output);
      }
    }
    #endif

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public int CalculateSize() {
      int size = 0;
      if (Id != 0L) {
        size += 1 + pb::CodedOutputStream.ComputeInt64Size(Id);
      }
      size += item_.CalculateSize(_repeated_item_codec);
      if (Description.Length != 0) {
        size += 1 + pb::CodedOutputStream.ComputeStringSize(Description);
      }
      if (Price != 0L) {
        size += 1 + pb::CodedOutputStream.ComputeInt64Size(Price);
      }
      if (RegionId != 0) {
        size += 1 + pb::CodedOutputStream.ComputeInt32Size(RegionId);
      }
      if (Parts != 0) {
        size += 1 + pb::CodedOutputStream.ComputeInt32Size(Parts);
      }
      if (_unknownFields != null) {
        size += _unknownFields.CalculateSize();
      }
      return size;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void MergeFrom(BloodReward other) {
      if (other == null) {
        return;
      }
      if (other.Id != 0L) {
        Id = other.Id;
      }
      item_.Add(other.item_);
      if (other.Description.Length != 0) {
        Description = other.Description;
      }
      if (other.Price != 0L) {
        Price = other.Price;
      }
      if (other.RegionId != 0) {
        RegionId = other.RegionId;
      }
      if (other.Parts != 0) {
        Parts = other.Parts;
      }
      _unknownFields = pb::UnknownFieldSet.MergeFrom(_unknownFields, other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void MergeFrom(pb::CodedInputStream input) {
    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      input.ReadRawMessage(this);
    #else
      uint tag;
      while ((tag = input.ReadTag()) != 0) {
        switch(tag) {
          default:
            _unknownFields = pb::UnknownFieldSet.MergeFieldFrom(_unknownFields, input);
            break;
          case 8: {
            Id = input.ReadInt64();
            break;
          }
          case 18:
          case 16: {
            item_.AddEntriesFrom(input, _repeated_item_codec);
            break;
          }
          case 26: {
            Description = input.ReadString();
            break;
          }
          case 32: {
            Price = input.ReadInt64();
            break;
          }
          case 40: {
            RegionId = input.ReadInt32();
            break;
          }
          case 48: {
            Parts = input.ReadInt32();
            break;
          }
        }
      }
    #endif
    }

    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    void pb::IBufferMessage.InternalMergeFrom(ref pb::ParseContext input) {
      uint tag;
      while ((tag = input.ReadTag()) != 0) {
        switch(tag) {
          default:
            _unknownFields = pb::UnknownFieldSet.MergeFieldFrom(_unknownFields, ref input);
            break;
          case 8: {
            Id = input.ReadInt64();
            break;
          }
          case 18:
          case 16: {
            item_.AddEntriesFrom(ref input, _repeated_item_codec);
            break;
          }
          case 26: {
            Description = input.ReadString();
            break;
          }
          case 32: {
            Price = input.ReadInt64();
            break;
          }
          case 40: {
            RegionId = input.ReadInt32();
            break;
          }
          case 48: {
            Parts = input.ReadInt32();
            break;
          }
        }
      }
    }
    #endif

  }

  [global::System.Diagnostics.DebuggerDisplayAttribute("{ToString(),nq}")]
  public sealed partial class Pocket : pb::IMessage<Pocket>
  #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      , pb::IBufferMessage
  #endif
  {
    private static readonly pb::MessageParser<Pocket> _parser = new pb::MessageParser<Pocket>(() => new Pocket());
    private pb::UnknownFieldSet _unknownFields;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public static pb::MessageParser<Pocket> Parser { get { return _parser; } }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public static pbr::MessageDescriptor Descriptor {
      get { return global::Common.CommonReflection.Descriptor.MessageTypes[12]; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    pbr::MessageDescriptor pb::IMessage.Descriptor {
      get { return Descriptor; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public Pocket() {
      OnConstruction();
    }

    partial void OnConstruction();

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public Pocket(Pocket other) : this() {
      type_ = other.type_;
      item_ = other.item_ != null ? other.item_.Clone() : null;
      _unknownFields = pb::UnknownFieldSet.Clone(other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public Pocket Clone() {
      return new Pocket(this);
    }

    /// <summary>Field number for the "type" field.</summary>
    public const int TypeFieldNumber = 1;
    private global::Common.PocketType type_ = global::Common.PocketType.PowerStone;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public global::Common.PocketType Type {
      get { return type_; }
      set {
        type_ = value;
      }
    }

    /// <summary>Field number for the "item" field.</summary>
    public const int ItemFieldNumber = 2;
    private global::Common.ItemComponent item_;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public global::Common.ItemComponent Item {
      get { return item_; }
      set {
        item_ = value;
      }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override bool Equals(object other) {
      return Equals(other as Pocket);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public bool Equals(Pocket other) {
      if (ReferenceEquals(other, null)) {
        return false;
      }
      if (ReferenceEquals(other, this)) {
        return true;
      }
      if (Type != other.Type) return false;
      if (!object.Equals(Item, other.Item)) return false;
      return Equals(_unknownFields, other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override int GetHashCode() {
      int hash = 1;
      if (Type != global::Common.PocketType.PowerStone) hash ^= Type.GetHashCode();
      if (item_ != null) hash ^= Item.GetHashCode();
      if (_unknownFields != null) {
        hash ^= _unknownFields.GetHashCode();
      }
      return hash;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override string ToString() {
      return pb::JsonFormatter.ToDiagnosticString(this);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void WriteTo(pb::CodedOutputStream output) {
    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      output.WriteRawMessage(this);
    #else
      if (Type != global::Common.PocketType.PowerStone) {
        output.WriteRawTag(8);
        output.WriteEnum((int) Type);
      }
      if (item_ != null) {
        output.WriteRawTag(18);
        output.WriteMessage(Item);
      }
      if (_unknownFields != null) {
        _unknownFields.WriteTo(output);
      }
    #endif
    }

    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    void pb::IBufferMessage.InternalWriteTo(ref pb::WriteContext output) {
      if (Type != global::Common.PocketType.PowerStone) {
        output.WriteRawTag(8);
        output.WriteEnum((int) Type);
      }
      if (item_ != null) {
        output.WriteRawTag(18);
        output.WriteMessage(Item);
      }
      if (_unknownFields != null) {
        _unknownFields.WriteTo(ref output);
      }
    }
    #endif

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public int CalculateSize() {
      int size = 0;
      if (Type != global::Common.PocketType.PowerStone) {
        size += 1 + pb::CodedOutputStream.ComputeEnumSize((int) Type);
      }
      if (item_ != null) {
        size += 1 + pb::CodedOutputStream.ComputeMessageSize(Item);
      }
      if (_unknownFields != null) {
        size += _unknownFields.CalculateSize();
      }
      return size;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void MergeFrom(Pocket other) {
      if (other == null) {
        return;
      }
      if (other.Type != global::Common.PocketType.PowerStone) {
        Type = other.Type;
      }
      if (other.item_ != null) {
        if (item_ == null) {
          Item = new global::Common.ItemComponent();
        }
        Item.MergeFrom(other.Item);
      }
      _unknownFields = pb::UnknownFieldSet.MergeFrom(_unknownFields, other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void MergeFrom(pb::CodedInputStream input) {
    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      input.ReadRawMessage(this);
    #else
      uint tag;
      while ((tag = input.ReadTag()) != 0) {
        switch(tag) {
          default:
            _unknownFields = pb::UnknownFieldSet.MergeFieldFrom(_unknownFields, input);
            break;
          case 8: {
            Type = (global::Common.PocketType) input.ReadEnum();
            break;
          }
          case 18: {
            if (item_ == null) {
              Item = new global::Common.ItemComponent();
            }
            input.ReadMessage(Item);
            break;
          }
        }
      }
    #endif
    }

    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    void pb::IBufferMessage.InternalMergeFrom(ref pb::ParseContext input) {
      uint tag;
      while ((tag = input.ReadTag()) != 0) {
        switch(tag) {
          default:
            _unknownFields = pb::UnknownFieldSet.MergeFieldFrom(_unknownFields, ref input);
            break;
          case 8: {
            Type = (global::Common.PocketType) input.ReadEnum();
            break;
          }
          case 18: {
            if (item_ == null) {
              Item = new global::Common.ItemComponent();
            }
            input.ReadMessage(Item);
            break;
          }
        }
      }
    }
    #endif

  }

  [global::System.Diagnostics.DebuggerDisplayAttribute("{ToString(),nq}")]
  public sealed partial class UserDailyQuestInfo : pb::IMessage<UserDailyQuestInfo>
  #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      , pb::IBufferMessage
  #endif
  {
    private static readonly pb::MessageParser<UserDailyQuestInfo> _parser = new pb::MessageParser<UserDailyQuestInfo>(() => new UserDailyQuestInfo());
    private pb::UnknownFieldSet _unknownFields;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public static pb::MessageParser<UserDailyQuestInfo> Parser { get { return _parser; } }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public static pbr::MessageDescriptor Descriptor {
      get { return global::Common.CommonReflection.Descriptor.MessageTypes[13]; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    pbr::MessageDescriptor pb::IMessage.Descriptor {
      get { return Descriptor; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public UserDailyQuestInfo() {
      OnConstruction();
    }

    partial void OnConstruction();

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public UserDailyQuestInfo(UserDailyQuestInfo other) : this() {
      userGrade_ = other.userGrade_;
      currentQuest_ = other.currentQuest_.Clone();
      rerolledQuest_ = other.rerolledQuest_.Clone();
      relevance_ = other.relevance_;
      _unknownFields = pb::UnknownFieldSet.Clone(other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public UserDailyQuestInfo Clone() {
      return new UserDailyQuestInfo(this);
    }

    /// <summary>Field number for the "user_grade" field.</summary>
    public const int UserGradeFieldNumber = 1;
    private uint userGrade_;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public uint UserGrade {
      get { return userGrade_; }
      set {
        userGrade_ = value;
      }
    }

    /// <summary>Field number for the "current_quest" field.</summary>
    public const int CurrentQuestFieldNumber = 2;
    private static readonly pb::FieldCodec<uint> _repeated_currentQuest_codec
        = pb::FieldCodec.ForUInt32(18);
    private readonly pbc::RepeatedField<uint> currentQuest_ = new pbc::RepeatedField<uint>();
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public pbc::RepeatedField<uint> CurrentQuest {
      get { return currentQuest_; }
    }

    /// <summary>Field number for the "rerolled_quest" field.</summary>
    public const int RerolledQuestFieldNumber = 3;
    private static readonly pb::FieldCodec<uint> _repeated_rerolledQuest_codec
        = pb::FieldCodec.ForUInt32(26);
    private readonly pbc::RepeatedField<uint> rerolledQuest_ = new pbc::RepeatedField<uint>();
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public pbc::RepeatedField<uint> RerolledQuest {
      get { return rerolledQuest_; }
    }

    /// <summary>Field number for the "relevance" field.</summary>
    public const int RelevanceFieldNumber = 4;
    private long relevance_;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public long Relevance {
      get { return relevance_; }
      set {
        relevance_ = value;
      }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override bool Equals(object other) {
      return Equals(other as UserDailyQuestInfo);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public bool Equals(UserDailyQuestInfo other) {
      if (ReferenceEquals(other, null)) {
        return false;
      }
      if (ReferenceEquals(other, this)) {
        return true;
      }
      if (UserGrade != other.UserGrade) return false;
      if(!currentQuest_.Equals(other.currentQuest_)) return false;
      if(!rerolledQuest_.Equals(other.rerolledQuest_)) return false;
      if (Relevance != other.Relevance) return false;
      return Equals(_unknownFields, other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override int GetHashCode() {
      int hash = 1;
      if (UserGrade != 0) hash ^= UserGrade.GetHashCode();
      hash ^= currentQuest_.GetHashCode();
      hash ^= rerolledQuest_.GetHashCode();
      if (Relevance != 0L) hash ^= Relevance.GetHashCode();
      if (_unknownFields != null) {
        hash ^= _unknownFields.GetHashCode();
      }
      return hash;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override string ToString() {
      return pb::JsonFormatter.ToDiagnosticString(this);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void WriteTo(pb::CodedOutputStream output) {
    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      output.WriteRawMessage(this);
    #else
      if (UserGrade != 0) {
        output.WriteRawTag(8);
        output.WriteUInt32(UserGrade);
      }
      currentQuest_.WriteTo(output, _repeated_currentQuest_codec);
      rerolledQuest_.WriteTo(output, _repeated_rerolledQuest_codec);
      if (Relevance != 0L) {
        output.WriteRawTag(32);
        output.WriteInt64(Relevance);
      }
      if (_unknownFields != null) {
        _unknownFields.WriteTo(output);
      }
    #endif
    }

    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    void pb::IBufferMessage.InternalWriteTo(ref pb::WriteContext output) {
      if (UserGrade != 0) {
        output.WriteRawTag(8);
        output.WriteUInt32(UserGrade);
      }
      currentQuest_.WriteTo(ref output, _repeated_currentQuest_codec);
      rerolledQuest_.WriteTo(ref output, _repeated_rerolledQuest_codec);
      if (Relevance != 0L) {
        output.WriteRawTag(32);
        output.WriteInt64(Relevance);
      }
      if (_unknownFields != null) {
        _unknownFields.WriteTo(ref output);
      }
    }
    #endif

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public int CalculateSize() {
      int size = 0;
      if (UserGrade != 0) {
        size += 1 + pb::CodedOutputStream.ComputeUInt32Size(UserGrade);
      }
      size += currentQuest_.CalculateSize(_repeated_currentQuest_codec);
      size += rerolledQuest_.CalculateSize(_repeated_rerolledQuest_codec);
      if (Relevance != 0L) {
        size += 1 + pb::CodedOutputStream.ComputeInt64Size(Relevance);
      }
      if (_unknownFields != null) {
        size += _unknownFields.CalculateSize();
      }
      return size;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void MergeFrom(UserDailyQuestInfo other) {
      if (other == null) {
        return;
      }
      if (other.UserGrade != 0) {
        UserGrade = other.UserGrade;
      }
      currentQuest_.Add(other.currentQuest_);
      rerolledQuest_.Add(other.rerolledQuest_);
      if (other.Relevance != 0L) {
        Relevance = other.Relevance;
      }
      _unknownFields = pb::UnknownFieldSet.MergeFrom(_unknownFields, other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void MergeFrom(pb::CodedInputStream input) {
    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      input.ReadRawMessage(this);
    #else
      uint tag;
      while ((tag = input.ReadTag()) != 0) {
        switch(tag) {
          default:
            _unknownFields = pb::UnknownFieldSet.MergeFieldFrom(_unknownFields, input);
            break;
          case 8: {
            UserGrade = input.ReadUInt32();
            break;
          }
          case 18:
          case 16: {
            currentQuest_.AddEntriesFrom(input, _repeated_currentQuest_codec);
            break;
          }
          case 26:
          case 24: {
            rerolledQuest_.AddEntriesFrom(input, _repeated_rerolledQuest_codec);
            break;
          }
          case 32: {
            Relevance = input.ReadInt64();
            break;
          }
        }
      }
    #endif
    }

    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    void pb::IBufferMessage.InternalMergeFrom(ref pb::ParseContext input) {
      uint tag;
      while ((tag = input.ReadTag()) != 0) {
        switch(tag) {
          default:
            _unknownFields = pb::UnknownFieldSet.MergeFieldFrom(_unknownFields, ref input);
            break;
          case 8: {
            UserGrade = input.ReadUInt32();
            break;
          }
          case 18:
          case 16: {
            currentQuest_.AddEntriesFrom(ref input, _repeated_currentQuest_codec);
            break;
          }
          case 26:
          case 24: {
            rerolledQuest_.AddEntriesFrom(ref input, _repeated_rerolledQuest_codec);
            break;
          }
          case 32: {
            Relevance = input.ReadInt64();
            break;
          }
        }
      }
    }
    #endif

  }

  [global::System.Diagnostics.DebuggerDisplayAttribute("{ToString(),nq}")]
  public sealed partial class AbilityGrade : pb::IMessage<AbilityGrade>
  #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      , pb::IBufferMessage
  #endif
  {
    private static readonly pb::MessageParser<AbilityGrade> _parser = new pb::MessageParser<AbilityGrade>(() => new AbilityGrade());
    private pb::UnknownFieldSet _unknownFields;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public static pb::MessageParser<AbilityGrade> Parser { get { return _parser; } }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public static pbr::MessageDescriptor Descriptor {
      get { return global::Common.CommonReflection.Descriptor.MessageTypes[14]; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    pbr::MessageDescriptor pb::IMessage.Descriptor {
      get { return Descriptor; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public AbilityGrade() {
      OnConstruction();
    }

    partial void OnConstruction();

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public AbilityGrade(AbilityGrade other) : this() {
      id_ = other.id_;
      price_ = other.price_;
      _unknownFields = pb::UnknownFieldSet.Clone(other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public AbilityGrade Clone() {
      return new AbilityGrade(this);
    }

    /// <summary>Field number for the "id" field.</summary>
    public const int IdFieldNumber = 1;
    private int id_;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public int Id {
      get { return id_; }
      set {
        id_ = value;
      }
    }

    /// <summary>Field number for the "price" field.</summary>
    public const int PriceFieldNumber = 2;
    private long price_;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public long Price {
      get { return price_; }
      set {
        price_ = value;
      }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override bool Equals(object other) {
      return Equals(other as AbilityGrade);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public bool Equals(AbilityGrade other) {
      if (ReferenceEquals(other, null)) {
        return false;
      }
      if (ReferenceEquals(other, this)) {
        return true;
      }
      if (Id != other.Id) return false;
      if (Price != other.Price) return false;
      return Equals(_unknownFields, other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override int GetHashCode() {
      int hash = 1;
      if (Id != 0) hash ^= Id.GetHashCode();
      if (Price != 0L) hash ^= Price.GetHashCode();
      if (_unknownFields != null) {
        hash ^= _unknownFields.GetHashCode();
      }
      return hash;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override string ToString() {
      return pb::JsonFormatter.ToDiagnosticString(this);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void WriteTo(pb::CodedOutputStream output) {
    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      output.WriteRawMessage(this);
    #else
      if (Id != 0) {
        output.WriteRawTag(8);
        output.WriteInt32(Id);
      }
      if (Price != 0L) {
        output.WriteRawTag(16);
        output.WriteInt64(Price);
      }
      if (_unknownFields != null) {
        _unknownFields.WriteTo(output);
      }
    #endif
    }

    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    void pb::IBufferMessage.InternalWriteTo(ref pb::WriteContext output) {
      if (Id != 0) {
        output.WriteRawTag(8);
        output.WriteInt32(Id);
      }
      if (Price != 0L) {
        output.WriteRawTag(16);
        output.WriteInt64(Price);
      }
      if (_unknownFields != null) {
        _unknownFields.WriteTo(ref output);
      }
    }
    #endif

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public int CalculateSize() {
      int size = 0;
      if (Id != 0) {
        size += 1 + pb::CodedOutputStream.ComputeInt32Size(Id);
      }
      if (Price != 0L) {
        size += 1 + pb::CodedOutputStream.ComputeInt64Size(Price);
      }
      if (_unknownFields != null) {
        size += _unknownFields.CalculateSize();
      }
      return size;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void MergeFrom(AbilityGrade other) {
      if (other == null) {
        return;
      }
      if (other.Id != 0) {
        Id = other.Id;
      }
      if (other.Price != 0L) {
        Price = other.Price;
      }
      _unknownFields = pb::UnknownFieldSet.MergeFrom(_unknownFields, other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void MergeFrom(pb::CodedInputStream input) {
    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      input.ReadRawMessage(this);
    #else
      uint tag;
      while ((tag = input.ReadTag()) != 0) {
        switch(tag) {
          default:
            _unknownFields = pb::UnknownFieldSet.MergeFieldFrom(_unknownFields, input);
            break;
          case 8: {
            Id = input.ReadInt32();
            break;
          }
          case 16: {
            Price = input.ReadInt64();
            break;
          }
        }
      }
    #endif
    }

    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    void pb::IBufferMessage.InternalMergeFrom(ref pb::ParseContext input) {
      uint tag;
      while ((tag = input.ReadTag()) != 0) {
        switch(tag) {
          default:
            _unknownFields = pb::UnknownFieldSet.MergeFieldFrom(_unknownFields, ref input);
            break;
          case 8: {
            Id = input.ReadInt32();
            break;
          }
          case 16: {
            Price = input.ReadInt64();
            break;
          }
        }
      }
    }
    #endif

  }

  [global::System.Diagnostics.DebuggerDisplayAttribute("{ToString(),nq}")]
  public sealed partial class GuideAbilityGrades : pb::IMessage<GuideAbilityGrades>
  #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      , pb::IBufferMessage
  #endif
  {
    private static readonly pb::MessageParser<GuideAbilityGrades> _parser = new pb::MessageParser<GuideAbilityGrades>(() => new GuideAbilityGrades());
    private pb::UnknownFieldSet _unknownFields;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public static pb::MessageParser<GuideAbilityGrades> Parser { get { return _parser; } }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public static pbr::MessageDescriptor Descriptor {
      get { return global::Common.CommonReflection.Descriptor.MessageTypes[15]; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    pbr::MessageDescriptor pb::IMessage.Descriptor {
      get { return Descriptor; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public GuideAbilityGrades() {
      OnConstruction();
    }

    partial void OnConstruction();

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public GuideAbilityGrades(GuideAbilityGrades other) : this() {
      abilityId_ = other.abilityId_;
      grade_ = other.grade_.Clone();
      _unknownFields = pb::UnknownFieldSet.Clone(other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public GuideAbilityGrades Clone() {
      return new GuideAbilityGrades(this);
    }

    /// <summary>Field number for the "ability_id" field.</summary>
    public const int AbilityIdFieldNumber = 1;
    private long abilityId_;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public long AbilityId {
      get { return abilityId_; }
      set {
        abilityId_ = value;
      }
    }

    /// <summary>Field number for the "grade" field.</summary>
    public const int GradeFieldNumber = 2;
    private static readonly pb::FieldCodec<global::Common.AbilityGrade> _repeated_grade_codec
        = pb::FieldCodec.ForMessage(18, global::Common.AbilityGrade.Parser);
    private readonly pbc::RepeatedField<global::Common.AbilityGrade> grade_ = new pbc::RepeatedField<global::Common.AbilityGrade>();
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public pbc::RepeatedField<global::Common.AbilityGrade> Grade {
      get { return grade_; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override bool Equals(object other) {
      return Equals(other as GuideAbilityGrades);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public bool Equals(GuideAbilityGrades other) {
      if (ReferenceEquals(other, null)) {
        return false;
      }
      if (ReferenceEquals(other, this)) {
        return true;
      }
      if (AbilityId != other.AbilityId) return false;
      if(!grade_.Equals(other.grade_)) return false;
      return Equals(_unknownFields, other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override int GetHashCode() {
      int hash = 1;
      if (AbilityId != 0L) hash ^= AbilityId.GetHashCode();
      hash ^= grade_.GetHashCode();
      if (_unknownFields != null) {
        hash ^= _unknownFields.GetHashCode();
      }
      return hash;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override string ToString() {
      return pb::JsonFormatter.ToDiagnosticString(this);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void WriteTo(pb::CodedOutputStream output) {
    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      output.WriteRawMessage(this);
    #else
      if (AbilityId != 0L) {
        output.WriteRawTag(8);
        output.WriteInt64(AbilityId);
      }
      grade_.WriteTo(output, _repeated_grade_codec);
      if (_unknownFields != null) {
        _unknownFields.WriteTo(output);
      }
    #endif
    }

    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    void pb::IBufferMessage.InternalWriteTo(ref pb::WriteContext output) {
      if (AbilityId != 0L) {
        output.WriteRawTag(8);
        output.WriteInt64(AbilityId);
      }
      grade_.WriteTo(ref output, _repeated_grade_codec);
      if (_unknownFields != null) {
        _unknownFields.WriteTo(ref output);
      }
    }
    #endif

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public int CalculateSize() {
      int size = 0;
      if (AbilityId != 0L) {
        size += 1 + pb::CodedOutputStream.ComputeInt64Size(AbilityId);
      }
      size += grade_.CalculateSize(_repeated_grade_codec);
      if (_unknownFields != null) {
        size += _unknownFields.CalculateSize();
      }
      return size;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void MergeFrom(GuideAbilityGrades other) {
      if (other == null) {
        return;
      }
      if (other.AbilityId != 0L) {
        AbilityId = other.AbilityId;
      }
      grade_.Add(other.grade_);
      _unknownFields = pb::UnknownFieldSet.MergeFrom(_unknownFields, other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void MergeFrom(pb::CodedInputStream input) {
    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      input.ReadRawMessage(this);
    #else
      uint tag;
      while ((tag = input.ReadTag()) != 0) {
        switch(tag) {
          default:
            _unknownFields = pb::UnknownFieldSet.MergeFieldFrom(_unknownFields, input);
            break;
          case 8: {
            AbilityId = input.ReadInt64();
            break;
          }
          case 18: {
            grade_.AddEntriesFrom(input, _repeated_grade_codec);
            break;
          }
        }
      }
    #endif
    }

    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    void pb::IBufferMessage.InternalMergeFrom(ref pb::ParseContext input) {
      uint tag;
      while ((tag = input.ReadTag()) != 0) {
        switch(tag) {
          default:
            _unknownFields = pb::UnknownFieldSet.MergeFieldFrom(_unknownFields, ref input);
            break;
          case 8: {
            AbilityId = input.ReadInt64();
            break;
          }
          case 18: {
            grade_.AddEntriesFrom(ref input, _repeated_grade_codec);
            break;
          }
        }
      }
    }
    #endif

  }

  [global::System.Diagnostics.DebuggerDisplayAttribute("{ToString(),nq}")]
  public sealed partial class Range : pb::IMessage<Range>
  #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      , pb::IBufferMessage
  #endif
  {
    private static readonly pb::MessageParser<Range> _parser = new pb::MessageParser<Range>(() => new Range());
    private pb::UnknownFieldSet _unknownFields;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public static pb::MessageParser<Range> Parser { get { return _parser; } }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public static pbr::MessageDescriptor Descriptor {
      get { return global::Common.CommonReflection.Descriptor.MessageTypes[16]; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    pbr::MessageDescriptor pb::IMessage.Descriptor {
      get { return Descriptor; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public Range() {
      OnConstruction();
    }

    partial void OnConstruction();

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public Range(Range other) : this() {
      min_ = other.min_;
      max_ = other.max_;
      _unknownFields = pb::UnknownFieldSet.Clone(other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public Range Clone() {
      return new Range(this);
    }

    /// <summary>Field number for the "min" field.</summary>
    public const int MinFieldNumber = 1;
    private int min_;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public int Min {
      get { return min_; }
      set {
        min_ = value;
      }
    }

    /// <summary>Field number for the "max" field.</summary>
    public const int MaxFieldNumber = 2;
    private int max_;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public int Max {
      get { return max_; }
      set {
        max_ = value;
      }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override bool Equals(object other) {
      return Equals(other as Range);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public bool Equals(Range other) {
      if (ReferenceEquals(other, null)) {
        return false;
      }
      if (ReferenceEquals(other, this)) {
        return true;
      }
      if (Min != other.Min) return false;
      if (Max != other.Max) return false;
      return Equals(_unknownFields, other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override int GetHashCode() {
      int hash = 1;
      if (Min != 0) hash ^= Min.GetHashCode();
      if (Max != 0) hash ^= Max.GetHashCode();
      if (_unknownFields != null) {
        hash ^= _unknownFields.GetHashCode();
      }
      return hash;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override string ToString() {
      return pb::JsonFormatter.ToDiagnosticString(this);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void WriteTo(pb::CodedOutputStream output) {
    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      output.WriteRawMessage(this);
    #else
      if (Min != 0) {
        output.WriteRawTag(8);
        output.WriteInt32(Min);
      }
      if (Max != 0) {
        output.WriteRawTag(16);
        output.WriteInt32(Max);
      }
      if (_unknownFields != null) {
        _unknownFields.WriteTo(output);
      }
    #endif
    }

    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    void pb::IBufferMessage.InternalWriteTo(ref pb::WriteContext output) {
      if (Min != 0) {
        output.WriteRawTag(8);
        output.WriteInt32(Min);
      }
      if (Max != 0) {
        output.WriteRawTag(16);
        output.WriteInt32(Max);
      }
      if (_unknownFields != null) {
        _unknownFields.WriteTo(ref output);
      }
    }
    #endif

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public int CalculateSize() {
      int size = 0;
      if (Min != 0) {
        size += 1 + pb::CodedOutputStream.ComputeInt32Size(Min);
      }
      if (Max != 0) {
        size += 1 + pb::CodedOutputStream.ComputeInt32Size(Max);
      }
      if (_unknownFields != null) {
        size += _unknownFields.CalculateSize();
      }
      return size;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void MergeFrom(Range other) {
      if (other == null) {
        return;
      }
      if (other.Min != 0) {
        Min = other.Min;
      }
      if (other.Max != 0) {
        Max = other.Max;
      }
      _unknownFields = pb::UnknownFieldSet.MergeFrom(_unknownFields, other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void MergeFrom(pb::CodedInputStream input) {
    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      input.ReadRawMessage(this);
    #else
      uint tag;
      while ((tag = input.ReadTag()) != 0) {
        switch(tag) {
          default:
            _unknownFields = pb::UnknownFieldSet.MergeFieldFrom(_unknownFields, input);
            break;
          case 8: {
            Min = input.ReadInt32();
            break;
          }
          case 16: {
            Max = input.ReadInt32();
            break;
          }
        }
      }
    #endif
    }

    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    void pb::IBufferMessage.InternalMergeFrom(ref pb::ParseContext input) {
      uint tag;
      while ((tag = input.ReadTag()) != 0) {
        switch(tag) {
          default:
            _unknownFields = pb::UnknownFieldSet.MergeFieldFrom(_unknownFields, ref input);
            break;
          case 8: {
            Min = input.ReadInt32();
            break;
          }
          case 16: {
            Max = input.ReadInt32();
            break;
          }
        }
      }
    }
    #endif

  }

  [global::System.Diagnostics.DebuggerDisplayAttribute("{ToString(),nq}")]
  public sealed partial class NullableRange : pb::IMessage<NullableRange>
  #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      , pb::IBufferMessage
  #endif
  {
    private static readonly pb::MessageParser<NullableRange> _parser = new pb::MessageParser<NullableRange>(() => new NullableRange());
    private pb::UnknownFieldSet _unknownFields;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public static pb::MessageParser<NullableRange> Parser { get { return _parser; } }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public static pbr::MessageDescriptor Descriptor {
      get { return global::Common.CommonReflection.Descriptor.MessageTypes[17]; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    pbr::MessageDescriptor pb::IMessage.Descriptor {
      get { return Descriptor; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public NullableRange() {
      OnConstruction();
    }

    partial void OnConstruction();

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public NullableRange(NullableRange other) : this() {
      switch (other.KindCase) {
        case KindOneofCase.Null:
          Null = other.Null;
          break;
        case KindOneofCase.Value:
          Value = other.Value.Clone();
          break;
      }

      _unknownFields = pb::UnknownFieldSet.Clone(other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public NullableRange Clone() {
      return new NullableRange(this);
    }

    /// <summary>Field number for the "null" field.</summary>
    public const int NullFieldNumber = 1;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public global::Google.Protobuf.WellKnownTypes.NullValue Null {
      get { return HasNull ? (global::Google.Protobuf.WellKnownTypes.NullValue) kind_ : global::Google.Protobuf.WellKnownTypes.NullValue.NullValue; }
      set {
        kind_ = value;
        kindCase_ = KindOneofCase.Null;
      }
    }
    /// <summary>Gets whether the "null" field is set</summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public bool HasNull {
      get { return kindCase_ == KindOneofCase.Null; }
    }
    /// <summary> Clears the value of the oneof if it's currently set to "null" </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void ClearNull() {
      if (HasNull) {
        ClearKind();
      }
    }

    /// <summary>Field number for the "value" field.</summary>
    public const int ValueFieldNumber = 2;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public global::Common.Range Value {
      get { return kindCase_ == KindOneofCase.Value ? (global::Common.Range) kind_ : null; }
      set {
        kind_ = value;
        kindCase_ = value == null ? KindOneofCase.None : KindOneofCase.Value;
      }
    }

    private object kind_;
    /// <summary>Enum of possible cases for the "Kind" oneof.</summary>
    public enum KindOneofCase {
      None = 0,
      Null = 1,
      Value = 2,
    }
    private KindOneofCase kindCase_ = KindOneofCase.None;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public KindOneofCase KindCase {
      get { return kindCase_; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void ClearKind() {
      kindCase_ = KindOneofCase.None;
      kind_ = null;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override bool Equals(object other) {
      return Equals(other as NullableRange);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public bool Equals(NullableRange other) {
      if (ReferenceEquals(other, null)) {
        return false;
      }
      if (ReferenceEquals(other, this)) {
        return true;
      }
      if (Null != other.Null) return false;
      if (!object.Equals(Value, other.Value)) return false;
      if (KindCase != other.KindCase) return false;
      return Equals(_unknownFields, other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override int GetHashCode() {
      int hash = 1;
      if (HasNull) hash ^= Null.GetHashCode();
      if (kindCase_ == KindOneofCase.Value) hash ^= Value.GetHashCode();
      hash ^= (int) kindCase_;
      if (_unknownFields != null) {
        hash ^= _unknownFields.GetHashCode();
      }
      return hash;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override string ToString() {
      return pb::JsonFormatter.ToDiagnosticString(this);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void WriteTo(pb::CodedOutputStream output) {
    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      output.WriteRawMessage(this);
    #else
      if (HasNull) {
        output.WriteRawTag(8);
        output.WriteEnum((int) Null);
      }
      if (kindCase_ == KindOneofCase.Value) {
        output.WriteRawTag(18);
        output.WriteMessage(Value);
      }
      if (_unknownFields != null) {
        _unknownFields.WriteTo(output);
      }
    #endif
    }

    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    void pb::IBufferMessage.InternalWriteTo(ref pb::WriteContext output) {
      if (HasNull) {
        output.WriteRawTag(8);
        output.WriteEnum((int) Null);
      }
      if (kindCase_ == KindOneofCase.Value) {
        output.WriteRawTag(18);
        output.WriteMessage(Value);
      }
      if (_unknownFields != null) {
        _unknownFields.WriteTo(ref output);
      }
    }
    #endif

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public int CalculateSize() {
      int size = 0;
      if (HasNull) {
        size += 1 + pb::CodedOutputStream.ComputeEnumSize((int) Null);
      }
      if (kindCase_ == KindOneofCase.Value) {
        size += 1 + pb::CodedOutputStream.ComputeMessageSize(Value);
      }
      if (_unknownFields != null) {
        size += _unknownFields.CalculateSize();
      }
      return size;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void MergeFrom(NullableRange other) {
      if (other == null) {
        return;
      }
      switch (other.KindCase) {
        case KindOneofCase.Null:
          Null = other.Null;
          break;
        case KindOneofCase.Value:
          if (Value == null) {
            Value = new global::Common.Range();
          }
          Value.MergeFrom(other.Value);
          break;
      }

      _unknownFields = pb::UnknownFieldSet.MergeFrom(_unknownFields, other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void MergeFrom(pb::CodedInputStream input) {
    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      input.ReadRawMessage(this);
    #else
      uint tag;
      while ((tag = input.ReadTag()) != 0) {
        switch(tag) {
          default:
            _unknownFields = pb::UnknownFieldSet.MergeFieldFrom(_unknownFields, input);
            break;
          case 8: {
            kind_ = input.ReadEnum();
            kindCase_ = KindOneofCase.Null;
            break;
          }
          case 18: {
            global::Common.Range subBuilder = new global::Common.Range();
            if (kindCase_ == KindOneofCase.Value) {
              subBuilder.MergeFrom(Value);
            }
            input.ReadMessage(subBuilder);
            Value = subBuilder;
            break;
          }
        }
      }
    #endif
    }

    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    void pb::IBufferMessage.InternalMergeFrom(ref pb::ParseContext input) {
      uint tag;
      while ((tag = input.ReadTag()) != 0) {
        switch(tag) {
          default:
            _unknownFields = pb::UnknownFieldSet.MergeFieldFrom(_unknownFields, ref input);
            break;
          case 8: {
            kind_ = input.ReadEnum();
            kindCase_ = KindOneofCase.Null;
            break;
          }
          case 18: {
            global::Common.Range subBuilder = new global::Common.Range();
            if (kindCase_ == KindOneofCase.Value) {
              subBuilder.MergeFrom(Value);
            }
            input.ReadMessage(subBuilder);
            Value = subBuilder;
            break;
          }
        }
      }
    }
    #endif

  }

  [global::System.Diagnostics.DebuggerDisplayAttribute("{ToString(),nq}")]
  public sealed partial class GuideLeague : pb::IMessage<GuideLeague>
  #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      , pb::IBufferMessage
  #endif
  {
    private static readonly pb::MessageParser<GuideLeague> _parser = new pb::MessageParser<GuideLeague>(() => new GuideLeague());
    private pb::UnknownFieldSet _unknownFields;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public static pb::MessageParser<GuideLeague> Parser { get { return _parser; } }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public static pbr::MessageDescriptor Descriptor {
      get { return global::Common.CommonReflection.Descriptor.MessageTypes[18]; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    pbr::MessageDescriptor pb::IMessage.Descriptor {
      get { return Descriptor; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public GuideLeague() {
      OnConstruction();
    }

    partial void OnConstruction();

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public GuideLeague(GuideLeague other) : this() {
      id_ = other.id_;
      name_ = other.name_;
      placeToUp_ = other.placeToUp_ != null ? other.placeToUp_.Clone() : null;
      placeToStay_ = other.placeToStay_ != null ? other.placeToStay_.Clone() : null;
      placeToDown_ = other.placeToDown_ != null ? other.placeToDown_.Clone() : null;
      dailyReward_ = other.dailyReward_.Clone();
      _unknownFields = pb::UnknownFieldSet.Clone(other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public GuideLeague Clone() {
      return new GuideLeague(this);
    }

    /// <summary>Field number for the "id" field.</summary>
    public const int IdFieldNumber = 1;
    private int id_;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public int Id {
      get { return id_; }
      set {
        id_ = value;
      }
    }

    /// <summary>Field number for the "name" field.</summary>
    public const int NameFieldNumber = 2;
    private string name_ = "";
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public string Name {
      get { return name_; }
      set {
        name_ = pb::ProtoPreconditions.CheckNotNull(value, "value");
      }
    }

    /// <summary>Field number for the "place_to_up" field.</summary>
    public const int PlaceToUpFieldNumber = 3;
    private global::Common.NullableRange placeToUp_;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public global::Common.NullableRange PlaceToUp {
      get { return placeToUp_; }
      set {
        placeToUp_ = value;
      }
    }

    /// <summary>Field number for the "place_to_stay" field.</summary>
    public const int PlaceToStayFieldNumber = 4;
    private global::Common.NullableRange placeToStay_;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public global::Common.NullableRange PlaceToStay {
      get { return placeToStay_; }
      set {
        placeToStay_ = value;
      }
    }

    /// <summary>Field number for the "place_to_down" field.</summary>
    public const int PlaceToDownFieldNumber = 5;
    private global::Common.NullableRange placeToDown_;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public global::Common.NullableRange PlaceToDown {
      get { return placeToDown_; }
      set {
        placeToDown_ = value;
      }
    }

    /// <summary>Field number for the "daily_reward" field.</summary>
    public const int DailyRewardFieldNumber = 6;
    private static readonly pb::FieldCodec<global::Common.ItemComponent> _repeated_dailyReward_codec
        = pb::FieldCodec.ForMessage(50, global::Common.ItemComponent.Parser);
    private readonly pbc::RepeatedField<global::Common.ItemComponent> dailyReward_ = new pbc::RepeatedField<global::Common.ItemComponent>();
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public pbc::RepeatedField<global::Common.ItemComponent> DailyReward {
      get { return dailyReward_; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override bool Equals(object other) {
      return Equals(other as GuideLeague);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public bool Equals(GuideLeague other) {
      if (ReferenceEquals(other, null)) {
        return false;
      }
      if (ReferenceEquals(other, this)) {
        return true;
      }
      if (Id != other.Id) return false;
      if (Name != other.Name) return false;
      if (!object.Equals(PlaceToUp, other.PlaceToUp)) return false;
      if (!object.Equals(PlaceToStay, other.PlaceToStay)) return false;
      if (!object.Equals(PlaceToDown, other.PlaceToDown)) return false;
      if(!dailyReward_.Equals(other.dailyReward_)) return false;
      return Equals(_unknownFields, other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override int GetHashCode() {
      int hash = 1;
      if (Id != 0) hash ^= Id.GetHashCode();
      if (Name.Length != 0) hash ^= Name.GetHashCode();
      if (placeToUp_ != null) hash ^= PlaceToUp.GetHashCode();
      if (placeToStay_ != null) hash ^= PlaceToStay.GetHashCode();
      if (placeToDown_ != null) hash ^= PlaceToDown.GetHashCode();
      hash ^= dailyReward_.GetHashCode();
      if (_unknownFields != null) {
        hash ^= _unknownFields.GetHashCode();
      }
      return hash;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override string ToString() {
      return pb::JsonFormatter.ToDiagnosticString(this);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void WriteTo(pb::CodedOutputStream output) {
    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      output.WriteRawMessage(this);
    #else
      if (Id != 0) {
        output.WriteRawTag(8);
        output.WriteInt32(Id);
      }
      if (Name.Length != 0) {
        output.WriteRawTag(18);
        output.WriteString(Name);
      }
      if (placeToUp_ != null) {
        output.WriteRawTag(26);
        output.WriteMessage(PlaceToUp);
      }
      if (placeToStay_ != null) {
        output.WriteRawTag(34);
        output.WriteMessage(PlaceToStay);
      }
      if (placeToDown_ != null) {
        output.WriteRawTag(42);
        output.WriteMessage(PlaceToDown);
      }
      dailyReward_.WriteTo(output, _repeated_dailyReward_codec);
      if (_unknownFields != null) {
        _unknownFields.WriteTo(output);
      }
    #endif
    }

    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    void pb::IBufferMessage.InternalWriteTo(ref pb::WriteContext output) {
      if (Id != 0) {
        output.WriteRawTag(8);
        output.WriteInt32(Id);
      }
      if (Name.Length != 0) {
        output.WriteRawTag(18);
        output.WriteString(Name);
      }
      if (placeToUp_ != null) {
        output.WriteRawTag(26);
        output.WriteMessage(PlaceToUp);
      }
      if (placeToStay_ != null) {
        output.WriteRawTag(34);
        output.WriteMessage(PlaceToStay);
      }
      if (placeToDown_ != null) {
        output.WriteRawTag(42);
        output.WriteMessage(PlaceToDown);
      }
      dailyReward_.WriteTo(ref output, _repeated_dailyReward_codec);
      if (_unknownFields != null) {
        _unknownFields.WriteTo(ref output);
      }
    }
    #endif

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public int CalculateSize() {
      int size = 0;
      if (Id != 0) {
        size += 1 + pb::CodedOutputStream.ComputeInt32Size(Id);
      }
      if (Name.Length != 0) {
        size += 1 + pb::CodedOutputStream.ComputeStringSize(Name);
      }
      if (placeToUp_ != null) {
        size += 1 + pb::CodedOutputStream.ComputeMessageSize(PlaceToUp);
      }
      if (placeToStay_ != null) {
        size += 1 + pb::CodedOutputStream.ComputeMessageSize(PlaceToStay);
      }
      if (placeToDown_ != null) {
        size += 1 + pb::CodedOutputStream.ComputeMessageSize(PlaceToDown);
      }
      size += dailyReward_.CalculateSize(_repeated_dailyReward_codec);
      if (_unknownFields != null) {
        size += _unknownFields.CalculateSize();
      }
      return size;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void MergeFrom(GuideLeague other) {
      if (other == null) {
        return;
      }
      if (other.Id != 0) {
        Id = other.Id;
      }
      if (other.Name.Length != 0) {
        Name = other.Name;
      }
      if (other.placeToUp_ != null) {
        if (placeToUp_ == null) {
          PlaceToUp = new global::Common.NullableRange();
        }
        PlaceToUp.MergeFrom(other.PlaceToUp);
      }
      if (other.placeToStay_ != null) {
        if (placeToStay_ == null) {
          PlaceToStay = new global::Common.NullableRange();
        }
        PlaceToStay.MergeFrom(other.PlaceToStay);
      }
      if (other.placeToDown_ != null) {
        if (placeToDown_ == null) {
          PlaceToDown = new global::Common.NullableRange();
        }
        PlaceToDown.MergeFrom(other.PlaceToDown);
      }
      dailyReward_.Add(other.dailyReward_);
      _unknownFields = pb::UnknownFieldSet.MergeFrom(_unknownFields, other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void MergeFrom(pb::CodedInputStream input) {
    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      input.ReadRawMessage(this);
    #else
      uint tag;
      while ((tag = input.ReadTag()) != 0) {
        switch(tag) {
          default:
            _unknownFields = pb::UnknownFieldSet.MergeFieldFrom(_unknownFields, input);
            break;
          case 8: {
            Id = input.ReadInt32();
            break;
          }
          case 18: {
            Name = input.ReadString();
            break;
          }
          case 26: {
            if (placeToUp_ == null) {
              PlaceToUp = new global::Common.NullableRange();
            }
            input.ReadMessage(PlaceToUp);
            break;
          }
          case 34: {
            if (placeToStay_ == null) {
              PlaceToStay = new global::Common.NullableRange();
            }
            input.ReadMessage(PlaceToStay);
            break;
          }
          case 42: {
            if (placeToDown_ == null) {
              PlaceToDown = new global::Common.NullableRange();
            }
            input.ReadMessage(PlaceToDown);
            break;
          }
          case 50: {
            dailyReward_.AddEntriesFrom(input, _repeated_dailyReward_codec);
            break;
          }
        }
      }
    #endif
    }

    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    void pb::IBufferMessage.InternalMergeFrom(ref pb::ParseContext input) {
      uint tag;
      while ((tag = input.ReadTag()) != 0) {
        switch(tag) {
          default:
            _unknownFields = pb::UnknownFieldSet.MergeFieldFrom(_unknownFields, ref input);
            break;
          case 8: {
            Id = input.ReadInt32();
            break;
          }
          case 18: {
            Name = input.ReadString();
            break;
          }
          case 26: {
            if (placeToUp_ == null) {
              PlaceToUp = new global::Common.NullableRange();
            }
            input.ReadMessage(PlaceToUp);
            break;
          }
          case 34: {
            if (placeToStay_ == null) {
              PlaceToStay = new global::Common.NullableRange();
            }
            input.ReadMessage(PlaceToStay);
            break;
          }
          case 42: {
            if (placeToDown_ == null) {
              PlaceToDown = new global::Common.NullableRange();
            }
            input.ReadMessage(PlaceToDown);
            break;
          }
          case 50: {
            dailyReward_.AddEntriesFrom(ref input, _repeated_dailyReward_codec);
            break;
          }
        }
      }
    }
    #endif

  }

  [global::System.Diagnostics.DebuggerDisplayAttribute("{ToString(),nq}")]
  public sealed partial class KillLocalReward : pb::IMessage<KillLocalReward>
  #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      , pb::IBufferMessage
  #endif
  {
    private static readonly pb::MessageParser<KillLocalReward> _parser = new pb::MessageParser<KillLocalReward>(() => new KillLocalReward());
    private pb::UnknownFieldSet _unknownFields;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public static pb::MessageParser<KillLocalReward> Parser { get { return _parser; } }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public static pbr::MessageDescriptor Descriptor {
      get { return global::Common.CommonReflection.Descriptor.MessageTypes[19]; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    pbr::MessageDescriptor pb::IMessage.Descriptor {
      get { return Descriptor; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public KillLocalReward() {
      OnConstruction();
    }

    partial void OnConstruction();

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public KillLocalReward(KillLocalReward other) : this() {
      id_ = other.id_;
      region_ = other.region_;
      kills_ = other.kills_;
      reward_ = other.reward_.Clone();
      _unknownFields = pb::UnknownFieldSet.Clone(other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public KillLocalReward Clone() {
      return new KillLocalReward(this);
    }

    /// <summary>Field number for the "id" field.</summary>
    public const int IdFieldNumber = 1;
    private int id_;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public int Id {
      get { return id_; }
      set {
        id_ = value;
      }
    }

    /// <summary>Field number for the "region" field.</summary>
    public const int RegionFieldNumber = 2;
    private int region_;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public int Region {
      get { return region_; }
      set {
        region_ = value;
      }
    }

    /// <summary>Field number for the "kills" field.</summary>
    public const int KillsFieldNumber = 3;
    private long kills_;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public long Kills {
      get { return kills_; }
      set {
        kills_ = value;
      }
    }

    /// <summary>Field number for the "reward" field.</summary>
    public const int RewardFieldNumber = 4;
    private static readonly pb::FieldCodec<global::Common.ItemComponent> _repeated_reward_codec
        = pb::FieldCodec.ForMessage(34, global::Common.ItemComponent.Parser);
    private readonly pbc::RepeatedField<global::Common.ItemComponent> reward_ = new pbc::RepeatedField<global::Common.ItemComponent>();
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public pbc::RepeatedField<global::Common.ItemComponent> Reward {
      get { return reward_; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override bool Equals(object other) {
      return Equals(other as KillLocalReward);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public bool Equals(KillLocalReward other) {
      if (ReferenceEquals(other, null)) {
        return false;
      }
      if (ReferenceEquals(other, this)) {
        return true;
      }
      if (Id != other.Id) return false;
      if (Region != other.Region) return false;
      if (Kills != other.Kills) return false;
      if(!reward_.Equals(other.reward_)) return false;
      return Equals(_unknownFields, other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override int GetHashCode() {
      int hash = 1;
      if (Id != 0) hash ^= Id.GetHashCode();
      if (Region != 0) hash ^= Region.GetHashCode();
      if (Kills != 0L) hash ^= Kills.GetHashCode();
      hash ^= reward_.GetHashCode();
      if (_unknownFields != null) {
        hash ^= _unknownFields.GetHashCode();
      }
      return hash;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override string ToString() {
      return pb::JsonFormatter.ToDiagnosticString(this);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void WriteTo(pb::CodedOutputStream output) {
    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      output.WriteRawMessage(this);
    #else
      if (Id != 0) {
        output.WriteRawTag(8);
        output.WriteInt32(Id);
      }
      if (Region != 0) {
        output.WriteRawTag(16);
        output.WriteInt32(Region);
      }
      if (Kills != 0L) {
        output.WriteRawTag(24);
        output.WriteInt64(Kills);
      }
      reward_.WriteTo(output, _repeated_reward_codec);
      if (_unknownFields != null) {
        _unknownFields.WriteTo(output);
      }
    #endif
    }

    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    void pb::IBufferMessage.InternalWriteTo(ref pb::WriteContext output) {
      if (Id != 0) {
        output.WriteRawTag(8);
        output.WriteInt32(Id);
      }
      if (Region != 0) {
        output.WriteRawTag(16);
        output.WriteInt32(Region);
      }
      if (Kills != 0L) {
        output.WriteRawTag(24);
        output.WriteInt64(Kills);
      }
      reward_.WriteTo(ref output, _repeated_reward_codec);
      if (_unknownFields != null) {
        _unknownFields.WriteTo(ref output);
      }
    }
    #endif

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public int CalculateSize() {
      int size = 0;
      if (Id != 0) {
        size += 1 + pb::CodedOutputStream.ComputeInt32Size(Id);
      }
      if (Region != 0) {
        size += 1 + pb::CodedOutputStream.ComputeInt32Size(Region);
      }
      if (Kills != 0L) {
        size += 1 + pb::CodedOutputStream.ComputeInt64Size(Kills);
      }
      size += reward_.CalculateSize(_repeated_reward_codec);
      if (_unknownFields != null) {
        size += _unknownFields.CalculateSize();
      }
      return size;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void MergeFrom(KillLocalReward other) {
      if (other == null) {
        return;
      }
      if (other.Id != 0) {
        Id = other.Id;
      }
      if (other.Region != 0) {
        Region = other.Region;
      }
      if (other.Kills != 0L) {
        Kills = other.Kills;
      }
      reward_.Add(other.reward_);
      _unknownFields = pb::UnknownFieldSet.MergeFrom(_unknownFields, other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void MergeFrom(pb::CodedInputStream input) {
    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      input.ReadRawMessage(this);
    #else
      uint tag;
      while ((tag = input.ReadTag()) != 0) {
        switch(tag) {
          default:
            _unknownFields = pb::UnknownFieldSet.MergeFieldFrom(_unknownFields, input);
            break;
          case 8: {
            Id = input.ReadInt32();
            break;
          }
          case 16: {
            Region = input.ReadInt32();
            break;
          }
          case 24: {
            Kills = input.ReadInt64();
            break;
          }
          case 34: {
            reward_.AddEntriesFrom(input, _repeated_reward_codec);
            break;
          }
        }
      }
    #endif
    }

    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    void pb::IBufferMessage.InternalMergeFrom(ref pb::ParseContext input) {
      uint tag;
      while ((tag = input.ReadTag()) != 0) {
        switch(tag) {
          default:
            _unknownFields = pb::UnknownFieldSet.MergeFieldFrom(_unknownFields, ref input);
            break;
          case 8: {
            Id = input.ReadInt32();
            break;
          }
          case 16: {
            Region = input.ReadInt32();
            break;
          }
          case 24: {
            Kills = input.ReadInt64();
            break;
          }
          case 34: {
            reward_.AddEntriesFrom(ref input, _repeated_reward_codec);
            break;
          }
        }
      }
    }
    #endif

  }

  [global::System.Diagnostics.DebuggerDisplayAttribute("{ToString(),nq}")]
  public sealed partial class NullableInt64 : pb::IMessage<NullableInt64>
  #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      , pb::IBufferMessage
  #endif
  {
    private static readonly pb::MessageParser<NullableInt64> _parser = new pb::MessageParser<NullableInt64>(() => new NullableInt64());
    private pb::UnknownFieldSet _unknownFields;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public static pb::MessageParser<NullableInt64> Parser { get { return _parser; } }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public static pbr::MessageDescriptor Descriptor {
      get { return global::Common.CommonReflection.Descriptor.MessageTypes[20]; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    pbr::MessageDescriptor pb::IMessage.Descriptor {
      get { return Descriptor; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public NullableInt64() {
      OnConstruction();
    }

    partial void OnConstruction();

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public NullableInt64(NullableInt64 other) : this() {
      switch (other.KindCase) {
        case KindOneofCase.Null:
          Null = other.Null;
          break;
        case KindOneofCase.Value:
          Value = other.Value;
          break;
      }

      _unknownFields = pb::UnknownFieldSet.Clone(other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public NullableInt64 Clone() {
      return new NullableInt64(this);
    }

    /// <summary>Field number for the "null" field.</summary>
    public const int NullFieldNumber = 1;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public global::Google.Protobuf.WellKnownTypes.NullValue Null {
      get { return HasNull ? (global::Google.Protobuf.WellKnownTypes.NullValue) kind_ : global::Google.Protobuf.WellKnownTypes.NullValue.NullValue; }
      set {
        kind_ = value;
        kindCase_ = KindOneofCase.Null;
      }
    }
    /// <summary>Gets whether the "null" field is set</summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public bool HasNull {
      get { return kindCase_ == KindOneofCase.Null; }
    }
    /// <summary> Clears the value of the oneof if it's currently set to "null" </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void ClearNull() {
      if (HasNull) {
        ClearKind();
      }
    }

    /// <summary>Field number for the "value" field.</summary>
    public const int ValueFieldNumber = 2;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public long Value {
      get { return HasValue ? (long) kind_ : 0L; }
      set {
        kind_ = value;
        kindCase_ = KindOneofCase.Value;
      }
    }
    /// <summary>Gets whether the "value" field is set</summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public bool HasValue {
      get { return kindCase_ == KindOneofCase.Value; }
    }
    /// <summary> Clears the value of the oneof if it's currently set to "value" </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void ClearValue() {
      if (HasValue) {
        ClearKind();
      }
    }

    private object kind_;
    /// <summary>Enum of possible cases for the "Kind" oneof.</summary>
    public enum KindOneofCase {
      None = 0,
      Null = 1,
      Value = 2,
    }
    private KindOneofCase kindCase_ = KindOneofCase.None;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public KindOneofCase KindCase {
      get { return kindCase_; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void ClearKind() {
      kindCase_ = KindOneofCase.None;
      kind_ = null;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override bool Equals(object other) {
      return Equals(other as NullableInt64);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public bool Equals(NullableInt64 other) {
      if (ReferenceEquals(other, null)) {
        return false;
      }
      if (ReferenceEquals(other, this)) {
        return true;
      }
      if (Null != other.Null) return false;
      if (Value != other.Value) return false;
      if (KindCase != other.KindCase) return false;
      return Equals(_unknownFields, other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override int GetHashCode() {
      int hash = 1;
      if (HasNull) hash ^= Null.GetHashCode();
      if (HasValue) hash ^= Value.GetHashCode();
      hash ^= (int) kindCase_;
      if (_unknownFields != null) {
        hash ^= _unknownFields.GetHashCode();
      }
      return hash;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override string ToString() {
      return pb::JsonFormatter.ToDiagnosticString(this);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void WriteTo(pb::CodedOutputStream output) {
    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      output.WriteRawMessage(this);
    #else
      if (HasNull) {
        output.WriteRawTag(8);
        output.WriteEnum((int) Null);
      }
      if (HasValue) {
        output.WriteRawTag(16);
        output.WriteInt64(Value);
      }
      if (_unknownFields != null) {
        _unknownFields.WriteTo(output);
      }
    #endif
    }

    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    void pb::IBufferMessage.InternalWriteTo(ref pb::WriteContext output) {
      if (HasNull) {
        output.WriteRawTag(8);
        output.WriteEnum((int) Null);
      }
      if (HasValue) {
        output.WriteRawTag(16);
        output.WriteInt64(Value);
      }
      if (_unknownFields != null) {
        _unknownFields.WriteTo(ref output);
      }
    }
    #endif

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public int CalculateSize() {
      int size = 0;
      if (HasNull) {
        size += 1 + pb::CodedOutputStream.ComputeEnumSize((int) Null);
      }
      if (HasValue) {
        size += 1 + pb::CodedOutputStream.ComputeInt64Size(Value);
      }
      if (_unknownFields != null) {
        size += _unknownFields.CalculateSize();
      }
      return size;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void MergeFrom(NullableInt64 other) {
      if (other == null) {
        return;
      }
      switch (other.KindCase) {
        case KindOneofCase.Null:
          Null = other.Null;
          break;
        case KindOneofCase.Value:
          Value = other.Value;
          break;
      }

      _unknownFields = pb::UnknownFieldSet.MergeFrom(_unknownFields, other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void MergeFrom(pb::CodedInputStream input) {
    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      input.ReadRawMessage(this);
    #else
      uint tag;
      while ((tag = input.ReadTag()) != 0) {
        switch(tag) {
          default:
            _unknownFields = pb::UnknownFieldSet.MergeFieldFrom(_unknownFields, input);
            break;
          case 8: {
            kind_ = input.ReadEnum();
            kindCase_ = KindOneofCase.Null;
            break;
          }
          case 16: {
            Value = input.ReadInt64();
            break;
          }
        }
      }
    #endif
    }

    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    void pb::IBufferMessage.InternalMergeFrom(ref pb::ParseContext input) {
      uint tag;
      while ((tag = input.ReadTag()) != 0) {
        switch(tag) {
          default:
            _unknownFields = pb::UnknownFieldSet.MergeFieldFrom(_unknownFields, ref input);
            break;
          case 8: {
            kind_ = input.ReadEnum();
            kindCase_ = KindOneofCase.Null;
            break;
          }
          case 16: {
            Value = input.ReadInt64();
            break;
          }
        }
      }
    }
    #endif

  }

  [global::System.Diagnostics.DebuggerDisplayAttribute("{ToString(),nq}")]
  public sealed partial class NullableFloat : pb::IMessage<NullableFloat>
  #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      , pb::IBufferMessage
  #endif
  {
    private static readonly pb::MessageParser<NullableFloat> _parser = new pb::MessageParser<NullableFloat>(() => new NullableFloat());
    private pb::UnknownFieldSet _unknownFields;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public static pb::MessageParser<NullableFloat> Parser { get { return _parser; } }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public static pbr::MessageDescriptor Descriptor {
      get { return global::Common.CommonReflection.Descriptor.MessageTypes[21]; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    pbr::MessageDescriptor pb::IMessage.Descriptor {
      get { return Descriptor; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public NullableFloat() {
      OnConstruction();
    }

    partial void OnConstruction();

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public NullableFloat(NullableFloat other) : this() {
      switch (other.KindCase) {
        case KindOneofCase.Null:
          Null = other.Null;
          break;
        case KindOneofCase.Value:
          Value = other.Value;
          break;
      }

      _unknownFields = pb::UnknownFieldSet.Clone(other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public NullableFloat Clone() {
      return new NullableFloat(this);
    }

    /// <summary>Field number for the "null" field.</summary>
    public const int NullFieldNumber = 1;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public global::Google.Protobuf.WellKnownTypes.NullValue Null {
      get { return HasNull ? (global::Google.Protobuf.WellKnownTypes.NullValue) kind_ : global::Google.Protobuf.WellKnownTypes.NullValue.NullValue; }
      set {
        kind_ = value;
        kindCase_ = KindOneofCase.Null;
      }
    }
    /// <summary>Gets whether the "null" field is set</summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public bool HasNull {
      get { return kindCase_ == KindOneofCase.Null; }
    }
    /// <summary> Clears the value of the oneof if it's currently set to "null" </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void ClearNull() {
      if (HasNull) {
        ClearKind();
      }
    }

    /// <summary>Field number for the "value" field.</summary>
    public const int ValueFieldNumber = 2;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public float Value {
      get { return HasValue ? (float) kind_ : 0F; }
      set {
        kind_ = value;
        kindCase_ = KindOneofCase.Value;
      }
    }
    /// <summary>Gets whether the "value" field is set</summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public bool HasValue {
      get { return kindCase_ == KindOneofCase.Value; }
    }
    /// <summary> Clears the value of the oneof if it's currently set to "value" </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void ClearValue() {
      if (HasValue) {
        ClearKind();
      }
    }

    private object kind_;
    /// <summary>Enum of possible cases for the "Kind" oneof.</summary>
    public enum KindOneofCase {
      None = 0,
      Null = 1,
      Value = 2,
    }
    private KindOneofCase kindCase_ = KindOneofCase.None;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public KindOneofCase KindCase {
      get { return kindCase_; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void ClearKind() {
      kindCase_ = KindOneofCase.None;
      kind_ = null;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override bool Equals(object other) {
      return Equals(other as NullableFloat);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public bool Equals(NullableFloat other) {
      if (ReferenceEquals(other, null)) {
        return false;
      }
      if (ReferenceEquals(other, this)) {
        return true;
      }
      if (Null != other.Null) return false;
      if (!pbc::ProtobufEqualityComparers.BitwiseSingleEqualityComparer.Equals(Value, other.Value)) return false;
      if (KindCase != other.KindCase) return false;
      return Equals(_unknownFields, other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override int GetHashCode() {
      int hash = 1;
      if (HasNull) hash ^= Null.GetHashCode();
      if (HasValue) hash ^= pbc::ProtobufEqualityComparers.BitwiseSingleEqualityComparer.GetHashCode(Value);
      hash ^= (int) kindCase_;
      if (_unknownFields != null) {
        hash ^= _unknownFields.GetHashCode();
      }
      return hash;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override string ToString() {
      return pb::JsonFormatter.ToDiagnosticString(this);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void WriteTo(pb::CodedOutputStream output) {
    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      output.WriteRawMessage(this);
    #else
      if (HasNull) {
        output.WriteRawTag(8);
        output.WriteEnum((int) Null);
      }
      if (HasValue) {
        output.WriteRawTag(21);
        output.WriteFloat(Value);
      }
      if (_unknownFields != null) {
        _unknownFields.WriteTo(output);
      }
    #endif
    }

    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    void pb::IBufferMessage.InternalWriteTo(ref pb::WriteContext output) {
      if (HasNull) {
        output.WriteRawTag(8);
        output.WriteEnum((int) Null);
      }
      if (HasValue) {
        output.WriteRawTag(21);
        output.WriteFloat(Value);
      }
      if (_unknownFields != null) {
        _unknownFields.WriteTo(ref output);
      }
    }
    #endif

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public int CalculateSize() {
      int size = 0;
      if (HasNull) {
        size += 1 + pb::CodedOutputStream.ComputeEnumSize((int) Null);
      }
      if (HasValue) {
        size += 1 + 4;
      }
      if (_unknownFields != null) {
        size += _unknownFields.CalculateSize();
      }
      return size;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void MergeFrom(NullableFloat other) {
      if (other == null) {
        return;
      }
      switch (other.KindCase) {
        case KindOneofCase.Null:
          Null = other.Null;
          break;
        case KindOneofCase.Value:
          Value = other.Value;
          break;
      }

      _unknownFields = pb::UnknownFieldSet.MergeFrom(_unknownFields, other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void MergeFrom(pb::CodedInputStream input) {
    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      input.ReadRawMessage(this);
    #else
      uint tag;
      while ((tag = input.ReadTag()) != 0) {
        switch(tag) {
          default:
            _unknownFields = pb::UnknownFieldSet.MergeFieldFrom(_unknownFields, input);
            break;
          case 8: {
            kind_ = input.ReadEnum();
            kindCase_ = KindOneofCase.Null;
            break;
          }
          case 21: {
            Value = input.ReadFloat();
            break;
          }
        }
      }
    #endif
    }

    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    void pb::IBufferMessage.InternalMergeFrom(ref pb::ParseContext input) {
      uint tag;
      while ((tag = input.ReadTag()) != 0) {
        switch(tag) {
          default:
            _unknownFields = pb::UnknownFieldSet.MergeFieldFrom(_unknownFields, ref input);
            break;
          case 8: {
            kind_ = input.ReadEnum();
            kindCase_ = KindOneofCase.Null;
            break;
          }
          case 21: {
            Value = input.ReadFloat();
            break;
          }
        }
      }
    }
    #endif

  }

  [global::System.Diagnostics.DebuggerDisplayAttribute("{ToString(),nq}")]
  public sealed partial class AccountStatistic : pb::IMessage<AccountStatistic>
  #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      , pb::IBufferMessage
  #endif
  {
    private static readonly pb::MessageParser<AccountStatistic> _parser = new pb::MessageParser<AccountStatistic>(() => new AccountStatistic());
    private pb::UnknownFieldSet _unknownFields;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public static pb::MessageParser<AccountStatistic> Parser { get { return _parser; } }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public static pbr::MessageDescriptor Descriptor {
      get { return global::Common.CommonReflection.Descriptor.MessageTypes[22]; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    pbr::MessageDescriptor pb::IMessage.Descriptor {
      get { return Descriptor; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public AccountStatistic() {
      OnConstruction();
    }

    partial void OnConstruction();

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public AccountStatistic(AccountStatistic other) : this() {
      userId_ = other.userId_;
      fights_ = other.fights_ != null ? other.fights_.Clone() : null;
      winRate_ = other.winRate_ != null ? other.winRate_.Clone() : null;
      harpoons_ = other.harpoons_ != null ? other.harpoons_.Clone() : null;
      headshots_ = other.headshots_ != null ? other.headshots_.Clone() : null;
      looted_ = other.looted_ != null ? other.looted_.Clone() : null;
      healer_ = other.healer_ != null ? other.healer_.Clone() : null;
      unlocker_ = other.unlocker_ != null ? other.unlocker_.Clone() : null;
      crafter_ = other.crafter_ != null ? other.crafter_.Clone() : null;
      _unknownFields = pb::UnknownFieldSet.Clone(other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public AccountStatistic Clone() {
      return new AccountStatistic(this);
    }

    /// <summary>Field number for the "user_id" field.</summary>
    public const int UserIdFieldNumber = 1;
    private string userId_ = "";
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public string UserId {
      get { return userId_; }
      set {
        userId_ = pb::ProtoPreconditions.CheckNotNull(value, "value");
      }
    }

    /// <summary>Field number for the "fights" field.</summary>
    public const int FightsFieldNumber = 2;
    private global::Common.NullableInt64 fights_;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public global::Common.NullableInt64 Fights {
      get { return fights_; }
      set {
        fights_ = value;
      }
    }

    /// <summary>Field number for the "win_rate" field.</summary>
    public const int WinRateFieldNumber = 3;
    private global::Common.NullableFloat winRate_;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public global::Common.NullableFloat WinRate {
      get { return winRate_; }
      set {
        winRate_ = value;
      }
    }

    /// <summary>Field number for the "harpoons" field.</summary>
    public const int HarpoonsFieldNumber = 4;
    private global::Common.NullableInt64 harpoons_;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public global::Common.NullableInt64 Harpoons {
      get { return harpoons_; }
      set {
        harpoons_ = value;
      }
    }

    /// <summary>Field number for the "headshots" field.</summary>
    public const int HeadshotsFieldNumber = 5;
    private global::Common.NullableInt64 headshots_;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public global::Common.NullableInt64 Headshots {
      get { return headshots_; }
      set {
        headshots_ = value;
      }
    }

    /// <summary>Field number for the "looted" field.</summary>
    public const int LootedFieldNumber = 6;
    private global::Common.NullableInt64 looted_;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public global::Common.NullableInt64 Looted {
      get { return looted_; }
      set {
        looted_ = value;
      }
    }

    /// <summary>Field number for the "healer" field.</summary>
    public const int HealerFieldNumber = 7;
    private global::Common.NullableInt64 healer_;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public global::Common.NullableInt64 Healer {
      get { return healer_; }
      set {
        healer_ = value;
      }
    }

    /// <summary>Field number for the "unlocker" field.</summary>
    public const int UnlockerFieldNumber = 8;
    private global::Common.NullableInt64 unlocker_;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public global::Common.NullableInt64 Unlocker {
      get { return unlocker_; }
      set {
        unlocker_ = value;
      }
    }

    /// <summary>Field number for the "crafter" field.</summary>
    public const int CrafterFieldNumber = 9;
    private global::Common.NullableInt64 crafter_;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public global::Common.NullableInt64 Crafter {
      get { return crafter_; }
      set {
        crafter_ = value;
      }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override bool Equals(object other) {
      return Equals(other as AccountStatistic);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public bool Equals(AccountStatistic other) {
      if (ReferenceEquals(other, null)) {
        return false;
      }
      if (ReferenceEquals(other, this)) {
        return true;
      }
      if (UserId != other.UserId) return false;
      if (!object.Equals(Fights, other.Fights)) return false;
      if (!object.Equals(WinRate, other.WinRate)) return false;
      if (!object.Equals(Harpoons, other.Harpoons)) return false;
      if (!object.Equals(Headshots, other.Headshots)) return false;
      if (!object.Equals(Looted, other.Looted)) return false;
      if (!object.Equals(Healer, other.Healer)) return false;
      if (!object.Equals(Unlocker, other.Unlocker)) return false;
      if (!object.Equals(Crafter, other.Crafter)) return false;
      return Equals(_unknownFields, other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override int GetHashCode() {
      int hash = 1;
      if (UserId.Length != 0) hash ^= UserId.GetHashCode();
      if (fights_ != null) hash ^= Fights.GetHashCode();
      if (winRate_ != null) hash ^= WinRate.GetHashCode();
      if (harpoons_ != null) hash ^= Harpoons.GetHashCode();
      if (headshots_ != null) hash ^= Headshots.GetHashCode();
      if (looted_ != null) hash ^= Looted.GetHashCode();
      if (healer_ != null) hash ^= Healer.GetHashCode();
      if (unlocker_ != null) hash ^= Unlocker.GetHashCode();
      if (crafter_ != null) hash ^= Crafter.GetHashCode();
      if (_unknownFields != null) {
        hash ^= _unknownFields.GetHashCode();
      }
      return hash;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override string ToString() {
      return pb::JsonFormatter.ToDiagnosticString(this);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void WriteTo(pb::CodedOutputStream output) {
    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      output.WriteRawMessage(this);
    #else
      if (UserId.Length != 0) {
        output.WriteRawTag(10);
        output.WriteString(UserId);
      }
      if (fights_ != null) {
        output.WriteRawTag(18);
        output.WriteMessage(Fights);
      }
      if (winRate_ != null) {
        output.WriteRawTag(26);
        output.WriteMessage(WinRate);
      }
      if (harpoons_ != null) {
        output.WriteRawTag(34);
        output.WriteMessage(Harpoons);
      }
      if (headshots_ != null) {
        output.WriteRawTag(42);
        output.WriteMessage(Headshots);
      }
      if (looted_ != null) {
        output.WriteRawTag(50);
        output.WriteMessage(Looted);
      }
      if (healer_ != null) {
        output.WriteRawTag(58);
        output.WriteMessage(Healer);
      }
      if (unlocker_ != null) {
        output.WriteRawTag(66);
        output.WriteMessage(Unlocker);
      }
      if (crafter_ != null) {
        output.WriteRawTag(74);
        output.WriteMessage(Crafter);
      }
      if (_unknownFields != null) {
        _unknownFields.WriteTo(output);
      }
    #endif
    }

    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    void pb::IBufferMessage.InternalWriteTo(ref pb::WriteContext output) {
      if (UserId.Length != 0) {
        output.WriteRawTag(10);
        output.WriteString(UserId);
      }
      if (fights_ != null) {
        output.WriteRawTag(18);
        output.WriteMessage(Fights);
      }
      if (winRate_ != null) {
        output.WriteRawTag(26);
        output.WriteMessage(WinRate);
      }
      if (harpoons_ != null) {
        output.WriteRawTag(34);
        output.WriteMessage(Harpoons);
      }
      if (headshots_ != null) {
        output.WriteRawTag(42);
        output.WriteMessage(Headshots);
      }
      if (looted_ != null) {
        output.WriteRawTag(50);
        output.WriteMessage(Looted);
      }
      if (healer_ != null) {
        output.WriteRawTag(58);
        output.WriteMessage(Healer);
      }
      if (unlocker_ != null) {
        output.WriteRawTag(66);
        output.WriteMessage(Unlocker);
      }
      if (crafter_ != null) {
        output.WriteRawTag(74);
        output.WriteMessage(Crafter);
      }
      if (_unknownFields != null) {
        _unknownFields.WriteTo(ref output);
      }
    }
    #endif

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public int CalculateSize() {
      int size = 0;
      if (UserId.Length != 0) {
        size += 1 + pb::CodedOutputStream.ComputeStringSize(UserId);
      }
      if (fights_ != null) {
        size += 1 + pb::CodedOutputStream.ComputeMessageSize(Fights);
      }
      if (winRate_ != null) {
        size += 1 + pb::CodedOutputStream.ComputeMessageSize(WinRate);
      }
      if (harpoons_ != null) {
        size += 1 + pb::CodedOutputStream.ComputeMessageSize(Harpoons);
      }
      if (headshots_ != null) {
        size += 1 + pb::CodedOutputStream.ComputeMessageSize(Headshots);
      }
      if (looted_ != null) {
        size += 1 + pb::CodedOutputStream.ComputeMessageSize(Looted);
      }
      if (healer_ != null) {
        size += 1 + pb::CodedOutputStream.ComputeMessageSize(Healer);
      }
      if (unlocker_ != null) {
        size += 1 + pb::CodedOutputStream.ComputeMessageSize(Unlocker);
      }
      if (crafter_ != null) {
        size += 1 + pb::CodedOutputStream.ComputeMessageSize(Crafter);
      }
      if (_unknownFields != null) {
        size += _unknownFields.CalculateSize();
      }
      return size;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void MergeFrom(AccountStatistic other) {
      if (other == null) {
        return;
      }
      if (other.UserId.Length != 0) {
        UserId = other.UserId;
      }
      if (other.fights_ != null) {
        if (fights_ == null) {
          Fights = new global::Common.NullableInt64();
        }
        Fights.MergeFrom(other.Fights);
      }
      if (other.winRate_ != null) {
        if (winRate_ == null) {
          WinRate = new global::Common.NullableFloat();
        }
        WinRate.MergeFrom(other.WinRate);
      }
      if (other.harpoons_ != null) {
        if (harpoons_ == null) {
          Harpoons = new global::Common.NullableInt64();
        }
        Harpoons.MergeFrom(other.Harpoons);
      }
      if (other.headshots_ != null) {
        if (headshots_ == null) {
          Headshots = new global::Common.NullableInt64();
        }
        Headshots.MergeFrom(other.Headshots);
      }
      if (other.looted_ != null) {
        if (looted_ == null) {
          Looted = new global::Common.NullableInt64();
        }
        Looted.MergeFrom(other.Looted);
      }
      if (other.healer_ != null) {
        if (healer_ == null) {
          Healer = new global::Common.NullableInt64();
        }
        Healer.MergeFrom(other.Healer);
      }
      if (other.unlocker_ != null) {
        if (unlocker_ == null) {
          Unlocker = new global::Common.NullableInt64();
        }
        Unlocker.MergeFrom(other.Unlocker);
      }
      if (other.crafter_ != null) {
        if (crafter_ == null) {
          Crafter = new global::Common.NullableInt64();
        }
        Crafter.MergeFrom(other.Crafter);
      }
      _unknownFields = pb::UnknownFieldSet.MergeFrom(_unknownFields, other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void MergeFrom(pb::CodedInputStream input) {
    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      input.ReadRawMessage(this);
    #else
      uint tag;
      while ((tag = input.ReadTag()) != 0) {
        switch(tag) {
          default:
            _unknownFields = pb::UnknownFieldSet.MergeFieldFrom(_unknownFields, input);
            break;
          case 10: {
            UserId = input.ReadString();
            break;
          }
          case 18: {
            if (fights_ == null) {
              Fights = new global::Common.NullableInt64();
            }
            input.ReadMessage(Fights);
            break;
          }
          case 26: {
            if (winRate_ == null) {
              WinRate = new global::Common.NullableFloat();
            }
            input.ReadMessage(WinRate);
            break;
          }
          case 34: {
            if (harpoons_ == null) {
              Harpoons = new global::Common.NullableInt64();
            }
            input.ReadMessage(Harpoons);
            break;
          }
          case 42: {
            if (headshots_ == null) {
              Headshots = new global::Common.NullableInt64();
            }
            input.ReadMessage(Headshots);
            break;
          }
          case 50: {
            if (looted_ == null) {
              Looted = new global::Common.NullableInt64();
            }
            input.ReadMessage(Looted);
            break;
          }
          case 58: {
            if (healer_ == null) {
              Healer = new global::Common.NullableInt64();
            }
            input.ReadMessage(Healer);
            break;
          }
          case 66: {
            if (unlocker_ == null) {
              Unlocker = new global::Common.NullableInt64();
            }
            input.ReadMessage(Unlocker);
            break;
          }
          case 74: {
            if (crafter_ == null) {
              Crafter = new global::Common.NullableInt64();
            }
            input.ReadMessage(Crafter);
            break;
          }
        }
      }
    #endif
    }

    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    void pb::IBufferMessage.InternalMergeFrom(ref pb::ParseContext input) {
      uint tag;
      while ((tag = input.ReadTag()) != 0) {
        switch(tag) {
          default:
            _unknownFields = pb::UnknownFieldSet.MergeFieldFrom(_unknownFields, ref input);
            break;
          case 10: {
            UserId = input.ReadString();
            break;
          }
          case 18: {
            if (fights_ == null) {
              Fights = new global::Common.NullableInt64();
            }
            input.ReadMessage(Fights);
            break;
          }
          case 26: {
            if (winRate_ == null) {
              WinRate = new global::Common.NullableFloat();
            }
            input.ReadMessage(WinRate);
            break;
          }
          case 34: {
            if (harpoons_ == null) {
              Harpoons = new global::Common.NullableInt64();
            }
            input.ReadMessage(Harpoons);
            break;
          }
          case 42: {
            if (headshots_ == null) {
              Headshots = new global::Common.NullableInt64();
            }
            input.ReadMessage(Headshots);
            break;
          }
          case 50: {
            if (looted_ == null) {
              Looted = new global::Common.NullableInt64();
            }
            input.ReadMessage(Looted);
            break;
          }
          case 58: {
            if (healer_ == null) {
              Healer = new global::Common.NullableInt64();
            }
            input.ReadMessage(Healer);
            break;
          }
          case 66: {
            if (unlocker_ == null) {
              Unlocker = new global::Common.NullableInt64();
            }
            input.ReadMessage(Unlocker);
            break;
          }
          case 74: {
            if (crafter_ == null) {
              Crafter = new global::Common.NullableInt64();
            }
            input.ReadMessage(Crafter);
            break;
          }
        }
      }
    }
    #endif

  }

  [global::System.Diagnostics.DebuggerDisplayAttribute("{ToString(),nq}")]
  public sealed partial class UserSeason : pb::IMessage<UserSeason>
  #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      , pb::IBufferMessage
  #endif
  {
    private static readonly pb::MessageParser<UserSeason> _parser = new pb::MessageParser<UserSeason>(() => new UserSeason());
    private pb::UnknownFieldSet _unknownFields;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public static pb::MessageParser<UserSeason> Parser { get { return _parser; } }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public static pbr::MessageDescriptor Descriptor {
      get { return global::Common.CommonReflection.Descriptor.MessageTypes[23]; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    pbr::MessageDescriptor pb::IMessage.Descriptor {
      get { return Descriptor; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public UserSeason() {
      OnConstruction();
    }

    partial void OnConstruction();

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public UserSeason(UserSeason other) : this() {
      user_ = other.user_ != null ? other.user_.Clone() : null;
      league_ = other.league_;
      _unknownFields = pb::UnknownFieldSet.Clone(other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public UserSeason Clone() {
      return new UserSeason(this);
    }

    /// <summary>Field number for the "user" field.</summary>
    public const int UserFieldNumber = 1;
    private global::Common.AccountData user_;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public global::Common.AccountData User {
      get { return user_; }
      set {
        user_ = value;
      }
    }

    /// <summary>Field number for the "league" field.</summary>
    public const int LeagueFieldNumber = 3;
    private uint league_;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public uint League {
      get { return league_; }
      set {
        league_ = value;
      }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override bool Equals(object other) {
      return Equals(other as UserSeason);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public bool Equals(UserSeason other) {
      if (ReferenceEquals(other, null)) {
        return false;
      }
      if (ReferenceEquals(other, this)) {
        return true;
      }
      if (!object.Equals(User, other.User)) return false;
      if (League != other.League) return false;
      return Equals(_unknownFields, other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override int GetHashCode() {
      int hash = 1;
      if (user_ != null) hash ^= User.GetHashCode();
      if (League != 0) hash ^= League.GetHashCode();
      if (_unknownFields != null) {
        hash ^= _unknownFields.GetHashCode();
      }
      return hash;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override string ToString() {
      return pb::JsonFormatter.ToDiagnosticString(this);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void WriteTo(pb::CodedOutputStream output) {
    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      output.WriteRawMessage(this);
    #else
      if (user_ != null) {
        output.WriteRawTag(10);
        output.WriteMessage(User);
      }
      if (League != 0) {
        output.WriteRawTag(24);
        output.WriteUInt32(League);
      }
      if (_unknownFields != null) {
        _unknownFields.WriteTo(output);
      }
    #endif
    }

    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    void pb::IBufferMessage.InternalWriteTo(ref pb::WriteContext output) {
      if (user_ != null) {
        output.WriteRawTag(10);
        output.WriteMessage(User);
      }
      if (League != 0) {
        output.WriteRawTag(24);
        output.WriteUInt32(League);
      }
      if (_unknownFields != null) {
        _unknownFields.WriteTo(ref output);
      }
    }
    #endif

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public int CalculateSize() {
      int size = 0;
      if (user_ != null) {
        size += 1 + pb::CodedOutputStream.ComputeMessageSize(User);
      }
      if (League != 0) {
        size += 1 + pb::CodedOutputStream.ComputeUInt32Size(League);
      }
      if (_unknownFields != null) {
        size += _unknownFields.CalculateSize();
      }
      return size;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void MergeFrom(UserSeason other) {
      if (other == null) {
        return;
      }
      if (other.user_ != null) {
        if (user_ == null) {
          User = new global::Common.AccountData();
        }
        User.MergeFrom(other.User);
      }
      if (other.League != 0) {
        League = other.League;
      }
      _unknownFields = pb::UnknownFieldSet.MergeFrom(_unknownFields, other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void MergeFrom(pb::CodedInputStream input) {
    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      input.ReadRawMessage(this);
    #else
      uint tag;
      while ((tag = input.ReadTag()) != 0) {
        switch(tag) {
          default:
            _unknownFields = pb::UnknownFieldSet.MergeFieldFrom(_unknownFields, input);
            break;
          case 10: {
            if (user_ == null) {
              User = new global::Common.AccountData();
            }
            input.ReadMessage(User);
            break;
          }
          case 24: {
            League = input.ReadUInt32();
            break;
          }
        }
      }
    #endif
    }

    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    void pb::IBufferMessage.InternalMergeFrom(ref pb::ParseContext input) {
      uint tag;
      while ((tag = input.ReadTag()) != 0) {
        switch(tag) {
          default:
            _unknownFields = pb::UnknownFieldSet.MergeFieldFrom(_unknownFields, ref input);
            break;
          case 10: {
            if (user_ == null) {
              User = new global::Common.AccountData();
            }
            input.ReadMessage(User);
            break;
          }
          case 24: {
            League = input.ReadUInt32();
            break;
          }
        }
      }
    }
    #endif

  }

  [global::System.Diagnostics.DebuggerDisplayAttribute("{ToString(),nq}")]
  public sealed partial class WeaponSkin : pb::IMessage<WeaponSkin>
  #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      , pb::IBufferMessage
  #endif
  {
    private static readonly pb::MessageParser<WeaponSkin> _parser = new pb::MessageParser<WeaponSkin>(() => new WeaponSkin());
    private pb::UnknownFieldSet _unknownFields;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public static pb::MessageParser<WeaponSkin> Parser { get { return _parser; } }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public static pbr::MessageDescriptor Descriptor {
      get { return global::Common.CommonReflection.Descriptor.MessageTypes[24]; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    pbr::MessageDescriptor pb::IMessage.Descriptor {
      get { return Descriptor; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public WeaponSkin() {
      OnConstruction();
    }

    partial void OnConstruction();

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public WeaponSkin(WeaponSkin other) : this() {
      id_ = other.id_;
      weaponId_ = other.weaponId_;
      durability_ = other.durability_;
      _unknownFields = pb::UnknownFieldSet.Clone(other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public WeaponSkin Clone() {
      return new WeaponSkin(this);
    }

    /// <summary>Field number for the "id" field.</summary>
    public const int IdFieldNumber = 1;
    private long id_;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public long Id {
      get { return id_; }
      set {
        id_ = value;
      }
    }

    /// <summary>Field number for the "weapon_id" field.</summary>
    public const int WeaponIdFieldNumber = 2;
    private long weaponId_;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public long WeaponId {
      get { return weaponId_; }
      set {
        weaponId_ = value;
      }
    }

    /// <summary>Field number for the "durability" field.</summary>
    public const int DurabilityFieldNumber = 3;
    private long durability_;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public long Durability {
      get { return durability_; }
      set {
        durability_ = value;
      }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override bool Equals(object other) {
      return Equals(other as WeaponSkin);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public bool Equals(WeaponSkin other) {
      if (ReferenceEquals(other, null)) {
        return false;
      }
      if (ReferenceEquals(other, this)) {
        return true;
      }
      if (Id != other.Id) return false;
      if (WeaponId != other.WeaponId) return false;
      if (Durability != other.Durability) return false;
      return Equals(_unknownFields, other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override int GetHashCode() {
      int hash = 1;
      if (Id != 0L) hash ^= Id.GetHashCode();
      if (WeaponId != 0L) hash ^= WeaponId.GetHashCode();
      if (Durability != 0L) hash ^= Durability.GetHashCode();
      if (_unknownFields != null) {
        hash ^= _unknownFields.GetHashCode();
      }
      return hash;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override string ToString() {
      return pb::JsonFormatter.ToDiagnosticString(this);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void WriteTo(pb::CodedOutputStream output) {
    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      output.WriteRawMessage(this);
    #else
      if (Id != 0L) {
        output.WriteRawTag(8);
        output.WriteInt64(Id);
      }
      if (WeaponId != 0L) {
        output.WriteRawTag(16);
        output.WriteInt64(WeaponId);
      }
      if (Durability != 0L) {
        output.WriteRawTag(24);
        output.WriteInt64(Durability);
      }
      if (_unknownFields != null) {
        _unknownFields.WriteTo(output);
      }
    #endif
    }

    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    void pb::IBufferMessage.InternalWriteTo(ref pb::WriteContext output) {
      if (Id != 0L) {
        output.WriteRawTag(8);
        output.WriteInt64(Id);
      }
      if (WeaponId != 0L) {
        output.WriteRawTag(16);
        output.WriteInt64(WeaponId);
      }
      if (Durability != 0L) {
        output.WriteRawTag(24);
        output.WriteInt64(Durability);
      }
      if (_unknownFields != null) {
        _unknownFields.WriteTo(ref output);
      }
    }
    #endif

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public int CalculateSize() {
      int size = 0;
      if (Id != 0L) {
        size += 1 + pb::CodedOutputStream.ComputeInt64Size(Id);
      }
      if (WeaponId != 0L) {
        size += 1 + pb::CodedOutputStream.ComputeInt64Size(WeaponId);
      }
      if (Durability != 0L) {
        size += 1 + pb::CodedOutputStream.ComputeInt64Size(Durability);
      }
      if (_unknownFields != null) {
        size += _unknownFields.CalculateSize();
      }
      return size;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void MergeFrom(WeaponSkin other) {
      if (other == null) {
        return;
      }
      if (other.Id != 0L) {
        Id = other.Id;
      }
      if (other.WeaponId != 0L) {
        WeaponId = other.WeaponId;
      }
      if (other.Durability != 0L) {
        Durability = other.Durability;
      }
      _unknownFields = pb::UnknownFieldSet.MergeFrom(_unknownFields, other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void MergeFrom(pb::CodedInputStream input) {
    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      input.ReadRawMessage(this);
    #else
      uint tag;
      while ((tag = input.ReadTag()) != 0) {
        switch(tag) {
          default:
            _unknownFields = pb::UnknownFieldSet.MergeFieldFrom(_unknownFields, input);
            break;
          case 8: {
            Id = input.ReadInt64();
            break;
          }
          case 16: {
            WeaponId = input.ReadInt64();
            break;
          }
          case 24: {
            Durability = input.ReadInt64();
            break;
          }
        }
      }
    #endif
    }

    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    void pb::IBufferMessage.InternalMergeFrom(ref pb::ParseContext input) {
      uint tag;
      while ((tag = input.ReadTag()) != 0) {
        switch(tag) {
          default:
            _unknownFields = pb::UnknownFieldSet.MergeFieldFrom(_unknownFields, ref input);
            break;
          case 8: {
            Id = input.ReadInt64();
            break;
          }
          case 16: {
            WeaponId = input.ReadInt64();
            break;
          }
          case 24: {
            Durability = input.ReadInt64();
            break;
          }
        }
      }
    }
    #endif

  }

  [global::System.Diagnostics.DebuggerDisplayAttribute("{ToString(),nq}")]
  public sealed partial class SkinDurability : pb::IMessage<SkinDurability>
  #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      , pb::IBufferMessage
  #endif
  {
    private static readonly pb::MessageParser<SkinDurability> _parser = new pb::MessageParser<SkinDurability>(() => new SkinDurability());
    private pb::UnknownFieldSet _unknownFields;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public static pb::MessageParser<SkinDurability> Parser { get { return _parser; } }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public static pbr::MessageDescriptor Descriptor {
      get { return global::Common.CommonReflection.Descriptor.MessageTypes[25]; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    pbr::MessageDescriptor pb::IMessage.Descriptor {
      get { return Descriptor; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public SkinDurability() {
      OnConstruction();
    }

    partial void OnConstruction();

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public SkinDurability(SkinDurability other) : this() {
      id_ = other.id_;
      durability_ = other.durability_;
      _unknownFields = pb::UnknownFieldSet.Clone(other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public SkinDurability Clone() {
      return new SkinDurability(this);
    }

    /// <summary>Field number for the "id" field.</summary>
    public const int IdFieldNumber = 1;
    private long id_;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public long Id {
      get { return id_; }
      set {
        id_ = value;
      }
    }

    /// <summary>Field number for the "durability" field.</summary>
    public const int DurabilityFieldNumber = 2;
    private long durability_;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public long Durability {
      get { return durability_; }
      set {
        durability_ = value;
      }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override bool Equals(object other) {
      return Equals(other as SkinDurability);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public bool Equals(SkinDurability other) {
      if (ReferenceEquals(other, null)) {
        return false;
      }
      if (ReferenceEquals(other, this)) {
        return true;
      }
      if (Id != other.Id) return false;
      if (Durability != other.Durability) return false;
      return Equals(_unknownFields, other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override int GetHashCode() {
      int hash = 1;
      if (Id != 0L) hash ^= Id.GetHashCode();
      if (Durability != 0L) hash ^= Durability.GetHashCode();
      if (_unknownFields != null) {
        hash ^= _unknownFields.GetHashCode();
      }
      return hash;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override string ToString() {
      return pb::JsonFormatter.ToDiagnosticString(this);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void WriteTo(pb::CodedOutputStream output) {
    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      output.WriteRawMessage(this);
    #else
      if (Id != 0L) {
        output.WriteRawTag(8);
        output.WriteInt64(Id);
      }
      if (Durability != 0L) {
        output.WriteRawTag(16);
        output.WriteInt64(Durability);
      }
      if (_unknownFields != null) {
        _unknownFields.WriteTo(output);
      }
    #endif
    }

    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    void pb::IBufferMessage.InternalWriteTo(ref pb::WriteContext output) {
      if (Id != 0L) {
        output.WriteRawTag(8);
        output.WriteInt64(Id);
      }
      if (Durability != 0L) {
        output.WriteRawTag(16);
        output.WriteInt64(Durability);
      }
      if (_unknownFields != null) {
        _unknownFields.WriteTo(ref output);
      }
    }
    #endif

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public int CalculateSize() {
      int size = 0;
      if (Id != 0L) {
        size += 1 + pb::CodedOutputStream.ComputeInt64Size(Id);
      }
      if (Durability != 0L) {
        size += 1 + pb::CodedOutputStream.ComputeInt64Size(Durability);
      }
      if (_unknownFields != null) {
        size += _unknownFields.CalculateSize();
      }
      return size;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void MergeFrom(SkinDurability other) {
      if (other == null) {
        return;
      }
      if (other.Id != 0L) {
        Id = other.Id;
      }
      if (other.Durability != 0L) {
        Durability = other.Durability;
      }
      _unknownFields = pb::UnknownFieldSet.MergeFrom(_unknownFields, other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void MergeFrom(pb::CodedInputStream input) {
    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      input.ReadRawMessage(this);
    #else
      uint tag;
      while ((tag = input.ReadTag()) != 0) {
        switch(tag) {
          default:
            _unknownFields = pb::UnknownFieldSet.MergeFieldFrom(_unknownFields, input);
            break;
          case 8: {
            Id = input.ReadInt64();
            break;
          }
          case 16: {
            Durability = input.ReadInt64();
            break;
          }
        }
      }
    #endif
    }

    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    void pb::IBufferMessage.InternalMergeFrom(ref pb::ParseContext input) {
      uint tag;
      while ((tag = input.ReadTag()) != 0) {
        switch(tag) {
          default:
            _unknownFields = pb::UnknownFieldSet.MergeFieldFrom(_unknownFields, ref input);
            break;
          case 8: {
            Id = input.ReadInt64();
            break;
          }
          case 16: {
            Durability = input.ReadInt64();
            break;
          }
        }
      }
    }
    #endif

  }

  [global::System.Diagnostics.DebuggerDisplayAttribute("{ToString(),nq}")]
  public sealed partial class Progress : pb::IMessage<Progress>
  #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      , pb::IBufferMessage
  #endif
  {
    private static readonly pb::MessageParser<Progress> _parser = new pb::MessageParser<Progress>(() => new Progress());
    private pb::UnknownFieldSet _unknownFields;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public static pb::MessageParser<Progress> Parser { get { return _parser; } }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public static pbr::MessageDescriptor Descriptor {
      get { return global::Common.CommonReflection.Descriptor.MessageTypes[26]; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    pbr::MessageDescriptor pb::IMessage.Descriptor {
      get { return Descriptor; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public Progress() {
      OnConstruction();
    }

    partial void OnConstruction();

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public Progress(Progress other) : this() {
      rank_ = other.rank_;
      kills_ = other.kills_;
      weaponId_ = other.weaponId_;
      outfitId_ = other.outfitId_;
      amuletId_ = other.amuletId_;
      weaponSkins_ = other.weaponSkins_.Clone();
      bloodStep_ = other.bloodStep_;
      stages_ = other.stages_.Clone();
      isSetStartingRank_ = other.isSetStartingRank_;
      skinDurabilities_ = other.skinDurabilities_.Clone();
      bloodBottleMaxSize_ = other.bloodBottleMaxSize_;
      isPaidRevive_ = other.isPaidRevive_;
      steps_ = other.steps_.Clone();
      pockets_ = other.pockets_.Clone();
      _unknownFields = pb::UnknownFieldSet.Clone(other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public Progress Clone() {
      return new Progress(this);
    }

    /// <summary>Field number for the "rank" field.</summary>
    public const int RankFieldNumber = 1;
    private long rank_;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public long Rank {
      get { return rank_; }
      set {
        rank_ = value;
      }
    }

    /// <summary>Field number for the "kills" field.</summary>
    public const int KillsFieldNumber = 2;
    private long kills_;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public long Kills {
      get { return kills_; }
      set {
        kills_ = value;
      }
    }

    /// <summary>Field number for the "weapon_id" field.</summary>
    public const int WeaponIdFieldNumber = 5;
    private long weaponId_;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public long WeaponId {
      get { return weaponId_; }
      set {
        weaponId_ = value;
      }
    }

    /// <summary>Field number for the "outfit_id" field.</summary>
    public const int OutfitIdFieldNumber = 6;
    private long outfitId_;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public long OutfitId {
      get { return outfitId_; }
      set {
        outfitId_ = value;
      }
    }

    /// <summary>Field number for the "amulet_id" field.</summary>
    public const int AmuletIdFieldNumber = 7;
    private long amuletId_;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public long AmuletId {
      get { return amuletId_; }
      set {
        amuletId_ = value;
      }
    }

    /// <summary>Field number for the "weapon_skins" field.</summary>
    public const int WeaponSkinsFieldNumber = 9;
    private static readonly pb::FieldCodec<global::Common.WeaponSkin> _repeated_weaponSkins_codec
        = pb::FieldCodec.ForMessage(74, global::Common.WeaponSkin.Parser);
    private readonly pbc::RepeatedField<global::Common.WeaponSkin> weaponSkins_ = new pbc::RepeatedField<global::Common.WeaponSkin>();
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public pbc::RepeatedField<global::Common.WeaponSkin> WeaponSkins {
      get { return weaponSkins_; }
    }

    /// <summary>Field number for the "blood_step" field.</summary>
    public const int BloodStepFieldNumber = 11;
    private long bloodStep_;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public long BloodStep {
      get { return bloodStep_; }
      set {
        bloodStep_ = value;
      }
    }

    /// <summary>Field number for the "stages" field.</summary>
    public const int StagesFieldNumber = 12;
    private static readonly pb::FieldCodec<string> _repeated_stages_codec
        = pb::FieldCodec.ForString(98);
    private readonly pbc::RepeatedField<string> stages_ = new pbc::RepeatedField<string>();
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public pbc::RepeatedField<string> Stages {
      get { return stages_; }
    }

    /// <summary>Field number for the "is_set_starting_rank" field.</summary>
    public const int IsSetStartingRankFieldNumber = 13;
    private bool isSetStartingRank_;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public bool IsSetStartingRank {
      get { return isSetStartingRank_; }
      set {
        isSetStartingRank_ = value;
      }
    }

    /// <summary>Field number for the "skin_durabilities" field.</summary>
    public const int SkinDurabilitiesFieldNumber = 14;
    private static readonly pb::FieldCodec<global::Common.SkinDurability> _repeated_skinDurabilities_codec
        = pb::FieldCodec.ForMessage(114, global::Common.SkinDurability.Parser);
    private readonly pbc::RepeatedField<global::Common.SkinDurability> skinDurabilities_ = new pbc::RepeatedField<global::Common.SkinDurability>();
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public pbc::RepeatedField<global::Common.SkinDurability> SkinDurabilities {
      get { return skinDurabilities_; }
    }

    /// <summary>Field number for the "blood_bottle_max_size" field.</summary>
    public const int BloodBottleMaxSizeFieldNumber = 15;
    private long bloodBottleMaxSize_;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public long BloodBottleMaxSize {
      get { return bloodBottleMaxSize_; }
      set {
        bloodBottleMaxSize_ = value;
      }
    }

    /// <summary>Field number for the "is_paid_revive" field.</summary>
    public const int IsPaidReviveFieldNumber = 16;
    private bool isPaidRevive_;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public bool IsPaidRevive {
      get { return isPaidRevive_; }
      set {
        isPaidRevive_ = value;
      }
    }

    /// <summary>Field number for the "steps" field.</summary>
    public const int StepsFieldNumber = 17;
    private static readonly pb::FieldCodec<global::Common.AccountStep> _repeated_steps_codec
        = pb::FieldCodec.ForEnum(138, x => (int) x, x => (global::Common.AccountStep) x);
    private readonly pbc::RepeatedField<global::Common.AccountStep> steps_ = new pbc::RepeatedField<global::Common.AccountStep>();
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public pbc::RepeatedField<global::Common.AccountStep> Steps {
      get { return steps_; }
    }

    /// <summary>Field number for the "pockets" field.</summary>
    public const int PocketsFieldNumber = 18;
    private static readonly pb::FieldCodec<global::Common.Pocket> _repeated_pockets_codec
        = pb::FieldCodec.ForMessage(146, global::Common.Pocket.Parser);
    private readonly pbc::RepeatedField<global::Common.Pocket> pockets_ = new pbc::RepeatedField<global::Common.Pocket>();
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public pbc::RepeatedField<global::Common.Pocket> Pockets {
      get { return pockets_; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override bool Equals(object other) {
      return Equals(other as Progress);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public bool Equals(Progress other) {
      if (ReferenceEquals(other, null)) {
        return false;
      }
      if (ReferenceEquals(other, this)) {
        return true;
      }
      if (Rank != other.Rank) return false;
      if (Kills != other.Kills) return false;
      if (WeaponId != other.WeaponId) return false;
      if (OutfitId != other.OutfitId) return false;
      if (AmuletId != other.AmuletId) return false;
      if(!weaponSkins_.Equals(other.weaponSkins_)) return false;
      if (BloodStep != other.BloodStep) return false;
      if(!stages_.Equals(other.stages_)) return false;
      if (IsSetStartingRank != other.IsSetStartingRank) return false;
      if(!skinDurabilities_.Equals(other.skinDurabilities_)) return false;
      if (BloodBottleMaxSize != other.BloodBottleMaxSize) return false;
      if (IsPaidRevive != other.IsPaidRevive) return false;
      if(!steps_.Equals(other.steps_)) return false;
      if(!pockets_.Equals(other.pockets_)) return false;
      return Equals(_unknownFields, other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override int GetHashCode() {
      int hash = 1;
      if (Rank != 0L) hash ^= Rank.GetHashCode();
      if (Kills != 0L) hash ^= Kills.GetHashCode();
      if (WeaponId != 0L) hash ^= WeaponId.GetHashCode();
      if (OutfitId != 0L) hash ^= OutfitId.GetHashCode();
      if (AmuletId != 0L) hash ^= AmuletId.GetHashCode();
      hash ^= weaponSkins_.GetHashCode();
      if (BloodStep != 0L) hash ^= BloodStep.GetHashCode();
      hash ^= stages_.GetHashCode();
      if (IsSetStartingRank != false) hash ^= IsSetStartingRank.GetHashCode();
      hash ^= skinDurabilities_.GetHashCode();
      if (BloodBottleMaxSize != 0L) hash ^= BloodBottleMaxSize.GetHashCode();
      if (IsPaidRevive != false) hash ^= IsPaidRevive.GetHashCode();
      hash ^= steps_.GetHashCode();
      hash ^= pockets_.GetHashCode();
      if (_unknownFields != null) {
        hash ^= _unknownFields.GetHashCode();
      }
      return hash;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override string ToString() {
      return pb::JsonFormatter.ToDiagnosticString(this);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void WriteTo(pb::CodedOutputStream output) {
    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      output.WriteRawMessage(this);
    #else
      if (Rank != 0L) {
        output.WriteRawTag(8);
        output.WriteInt64(Rank);
      }
      if (Kills != 0L) {
        output.WriteRawTag(16);
        output.WriteInt64(Kills);
      }
      if (WeaponId != 0L) {
        output.WriteRawTag(40);
        output.WriteInt64(WeaponId);
      }
      if (OutfitId != 0L) {
        output.WriteRawTag(48);
        output.WriteInt64(OutfitId);
      }
      if (AmuletId != 0L) {
        output.WriteRawTag(56);
        output.WriteInt64(AmuletId);
      }
      weaponSkins_.WriteTo(output, _repeated_weaponSkins_codec);
      if (BloodStep != 0L) {
        output.WriteRawTag(88);
        output.WriteInt64(BloodStep);
      }
      stages_.WriteTo(output, _repeated_stages_codec);
      if (IsSetStartingRank != false) {
        output.WriteRawTag(104);
        output.WriteBool(IsSetStartingRank);
      }
      skinDurabilities_.WriteTo(output, _repeated_skinDurabilities_codec);
      if (BloodBottleMaxSize != 0L) {
        output.WriteRawTag(120);
        output.WriteInt64(BloodBottleMaxSize);
      }
      if (IsPaidRevive != false) {
        output.WriteRawTag(128, 1);
        output.WriteBool(IsPaidRevive);
      }
      steps_.WriteTo(output, _repeated_steps_codec);
      pockets_.WriteTo(output, _repeated_pockets_codec);
      if (_unknownFields != null) {
        _unknownFields.WriteTo(output);
      }
    #endif
    }

    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    void pb::IBufferMessage.InternalWriteTo(ref pb::WriteContext output) {
      if (Rank != 0L) {
        output.WriteRawTag(8);
        output.WriteInt64(Rank);
      }
      if (Kills != 0L) {
        output.WriteRawTag(16);
        output.WriteInt64(Kills);
      }
      if (WeaponId != 0L) {
        output.WriteRawTag(40);
        output.WriteInt64(WeaponId);
      }
      if (OutfitId != 0L) {
        output.WriteRawTag(48);
        output.WriteInt64(OutfitId);
      }
      if (AmuletId != 0L) {
        output.WriteRawTag(56);
        output.WriteInt64(AmuletId);
      }
      weaponSkins_.WriteTo(ref output, _repeated_weaponSkins_codec);
      if (BloodStep != 0L) {
        output.WriteRawTag(88);
        output.WriteInt64(BloodStep);
      }
      stages_.WriteTo(ref output, _repeated_stages_codec);
      if (IsSetStartingRank != false) {
        output.WriteRawTag(104);
        output.WriteBool(IsSetStartingRank);
      }
      skinDurabilities_.WriteTo(ref output, _repeated_skinDurabilities_codec);
      if (BloodBottleMaxSize != 0L) {
        output.WriteRawTag(120);
        output.WriteInt64(BloodBottleMaxSize);
      }
      if (IsPaidRevive != false) {
        output.WriteRawTag(128, 1);
        output.WriteBool(IsPaidRevive);
      }
      steps_.WriteTo(ref output, _repeated_steps_codec);
      pockets_.WriteTo(ref output, _repeated_pockets_codec);
      if (_unknownFields != null) {
        _unknownFields.WriteTo(ref output);
      }
    }
    #endif

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public int CalculateSize() {
      int size = 0;
      if (Rank != 0L) {
        size += 1 + pb::CodedOutputStream.ComputeInt64Size(Rank);
      }
      if (Kills != 0L) {
        size += 1 + pb::CodedOutputStream.ComputeInt64Size(Kills);
      }
      if (WeaponId != 0L) {
        size += 1 + pb::CodedOutputStream.ComputeInt64Size(WeaponId);
      }
      if (OutfitId != 0L) {
        size += 1 + pb::CodedOutputStream.ComputeInt64Size(OutfitId);
      }
      if (AmuletId != 0L) {
        size += 1 + pb::CodedOutputStream.ComputeInt64Size(AmuletId);
      }
      size += weaponSkins_.CalculateSize(_repeated_weaponSkins_codec);
      if (BloodStep != 0L) {
        size += 1 + pb::CodedOutputStream.ComputeInt64Size(BloodStep);
      }
      size += stages_.CalculateSize(_repeated_stages_codec);
      if (IsSetStartingRank != false) {
        size += 1 + 1;
      }
      size += skinDurabilities_.CalculateSize(_repeated_skinDurabilities_codec);
      if (BloodBottleMaxSize != 0L) {
        size += 1 + pb::CodedOutputStream.ComputeInt64Size(BloodBottleMaxSize);
      }
      if (IsPaidRevive != false) {
        size += 2 + 1;
      }
      size += steps_.CalculateSize(_repeated_steps_codec);
      size += pockets_.CalculateSize(_repeated_pockets_codec);
      if (_unknownFields != null) {
        size += _unknownFields.CalculateSize();
      }
      return size;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void MergeFrom(Progress other) {
      if (other == null) {
        return;
      }
      if (other.Rank != 0L) {
        Rank = other.Rank;
      }
      if (other.Kills != 0L) {
        Kills = other.Kills;
      }
      if (other.WeaponId != 0L) {
        WeaponId = other.WeaponId;
      }
      if (other.OutfitId != 0L) {
        OutfitId = other.OutfitId;
      }
      if (other.AmuletId != 0L) {
        AmuletId = other.AmuletId;
      }
      weaponSkins_.Add(other.weaponSkins_);
      if (other.BloodStep != 0L) {
        BloodStep = other.BloodStep;
      }
      stages_.Add(other.stages_);
      if (other.IsSetStartingRank != false) {
        IsSetStartingRank = other.IsSetStartingRank;
      }
      skinDurabilities_.Add(other.skinDurabilities_);
      if (other.BloodBottleMaxSize != 0L) {
        BloodBottleMaxSize = other.BloodBottleMaxSize;
      }
      if (other.IsPaidRevive != false) {
        IsPaidRevive = other.IsPaidRevive;
      }
      steps_.Add(other.steps_);
      pockets_.Add(other.pockets_);
      _unknownFields = pb::UnknownFieldSet.MergeFrom(_unknownFields, other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void MergeFrom(pb::CodedInputStream input) {
    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      input.ReadRawMessage(this);
    #else
      uint tag;
      while ((tag = input.ReadTag()) != 0) {
        switch(tag) {
          default:
            _unknownFields = pb::UnknownFieldSet.MergeFieldFrom(_unknownFields, input);
            break;
          case 8: {
            Rank = input.ReadInt64();
            break;
          }
          case 16: {
            Kills = input.ReadInt64();
            break;
          }
          case 40: {
            WeaponId = input.ReadInt64();
            break;
          }
          case 48: {
            OutfitId = input.ReadInt64();
            break;
          }
          case 56: {
            AmuletId = input.ReadInt64();
            break;
          }
          case 74: {
            weaponSkins_.AddEntriesFrom(input, _repeated_weaponSkins_codec);
            break;
          }
          case 88: {
            BloodStep = input.ReadInt64();
            break;
          }
          case 98: {
            stages_.AddEntriesFrom(input, _repeated_stages_codec);
            break;
          }
          case 104: {
            IsSetStartingRank = input.ReadBool();
            break;
          }
          case 114: {
            skinDurabilities_.AddEntriesFrom(input, _repeated_skinDurabilities_codec);
            break;
          }
          case 120: {
            BloodBottleMaxSize = input.ReadInt64();
            break;
          }
          case 128: {
            IsPaidRevive = input.ReadBool();
            break;
          }
          case 138:
          case 136: {
            steps_.AddEntriesFrom(input, _repeated_steps_codec);
            break;
          }
          case 146: {
            pockets_.AddEntriesFrom(input, _repeated_pockets_codec);
            break;
          }
        }
      }
    #endif
    }

    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    void pb::IBufferMessage.InternalMergeFrom(ref pb::ParseContext input) {
      uint tag;
      while ((tag = input.ReadTag()) != 0) {
        switch(tag) {
          default:
            _unknownFields = pb::UnknownFieldSet.MergeFieldFrom(_unknownFields, ref input);
            break;
          case 8: {
            Rank = input.ReadInt64();
            break;
          }
          case 16: {
            Kills = input.ReadInt64();
            break;
          }
          case 40: {
            WeaponId = input.ReadInt64();
            break;
          }
          case 48: {
            OutfitId = input.ReadInt64();
            break;
          }
          case 56: {
            AmuletId = input.ReadInt64();
            break;
          }
          case 74: {
            weaponSkins_.AddEntriesFrom(ref input, _repeated_weaponSkins_codec);
            break;
          }
          case 88: {
            BloodStep = input.ReadInt64();
            break;
          }
          case 98: {
            stages_.AddEntriesFrom(ref input, _repeated_stages_codec);
            break;
          }
          case 104: {
            IsSetStartingRank = input.ReadBool();
            break;
          }
          case 114: {
            skinDurabilities_.AddEntriesFrom(ref input, _repeated_skinDurabilities_codec);
            break;
          }
          case 120: {
            BloodBottleMaxSize = input.ReadInt64();
            break;
          }
          case 128: {
            IsPaidRevive = input.ReadBool();
            break;
          }
          case 138:
          case 136: {
            steps_.AddEntriesFrom(ref input, _repeated_steps_codec);
            break;
          }
          case 146: {
            pockets_.AddEntriesFrom(ref input, _repeated_pockets_codec);
            break;
          }
        }
      }
    }
    #endif

  }

  [global::System.Diagnostics.DebuggerDisplayAttribute("{ToString(),nq}")]
  public sealed partial class Timer : pb::IMessage<Timer>
  #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      , pb::IBufferMessage
  #endif
  {
    private static readonly pb::MessageParser<Timer> _parser = new pb::MessageParser<Timer>(() => new Timer());
    private pb::UnknownFieldSet _unknownFields;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public static pb::MessageParser<Timer> Parser { get { return _parser; } }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public static pbr::MessageDescriptor Descriptor {
      get { return global::Common.CommonReflection.Descriptor.MessageTypes[27]; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    pbr::MessageDescriptor pb::IMessage.Descriptor {
      get { return Descriptor; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public Timer() {
      OnConstruction();
    }

    partial void OnConstruction();

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public Timer(Timer other) : this() {
      id_ = other.id_;
      userId_ = other.userId_;
      itemId_ = other.itemId_;
      type_ = other.type_;
      deadTime_ = other.deadTime_;
      startTime_ = other.startTime_;
      _unknownFields = pb::UnknownFieldSet.Clone(other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public Timer Clone() {
      return new Timer(this);
    }

    /// <summary>Field number for the "id" field.</summary>
    public const int IdFieldNumber = 1;
    private string id_ = "";
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public string Id {
      get { return id_; }
      set {
        id_ = pb::ProtoPreconditions.CheckNotNull(value, "value");
      }
    }

    /// <summary>Field number for the "user_id" field.</summary>
    public const int UserIdFieldNumber = 2;
    private string userId_ = "";
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public string UserId {
      get { return userId_; }
      set {
        userId_ = pb::ProtoPreconditions.CheckNotNull(value, "value");
      }
    }

    /// <summary>Field number for the "item_id" field.</summary>
    public const int ItemIdFieldNumber = 3;
    private long itemId_;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public long ItemId {
      get { return itemId_; }
      set {
        itemId_ = value;
      }
    }

    /// <summary>Field number for the "type" field.</summary>
    public const int TypeFieldNumber = 4;
    private global::Common.TimerType type_ = global::Common.TimerType.Use;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public global::Common.TimerType Type {
      get { return type_; }
      set {
        type_ = value;
      }
    }

    /// <summary>Field number for the "dead_time" field.</summary>
    public const int DeadTimeFieldNumber = 5;
    private string deadTime_ = "";
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public string DeadTime {
      get { return deadTime_; }
      set {
        deadTime_ = pb::ProtoPreconditions.CheckNotNull(value, "value");
      }
    }

    /// <summary>Field number for the "start_time" field.</summary>
    public const int StartTimeFieldNumber = 6;
    private string startTime_ = "";
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public string StartTime {
      get { return startTime_; }
      set {
        startTime_ = pb::ProtoPreconditions.CheckNotNull(value, "value");
      }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override bool Equals(object other) {
      return Equals(other as Timer);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public bool Equals(Timer other) {
      if (ReferenceEquals(other, null)) {
        return false;
      }
      if (ReferenceEquals(other, this)) {
        return true;
      }
      if (Id != other.Id) return false;
      if (UserId != other.UserId) return false;
      if (ItemId != other.ItemId) return false;
      if (Type != other.Type) return false;
      if (DeadTime != other.DeadTime) return false;
      if (StartTime != other.StartTime) return false;
      return Equals(_unknownFields, other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override int GetHashCode() {
      int hash = 1;
      if (Id.Length != 0) hash ^= Id.GetHashCode();
      if (UserId.Length != 0) hash ^= UserId.GetHashCode();
      if (ItemId != 0L) hash ^= ItemId.GetHashCode();
      if (Type != global::Common.TimerType.Use) hash ^= Type.GetHashCode();
      if (DeadTime.Length != 0) hash ^= DeadTime.GetHashCode();
      if (StartTime.Length != 0) hash ^= StartTime.GetHashCode();
      if (_unknownFields != null) {
        hash ^= _unknownFields.GetHashCode();
      }
      return hash;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override string ToString() {
      return pb::JsonFormatter.ToDiagnosticString(this);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void WriteTo(pb::CodedOutputStream output) {
    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      output.WriteRawMessage(this);
    #else
      if (Id.Length != 0) {
        output.WriteRawTag(10);
        output.WriteString(Id);
      }
      if (UserId.Length != 0) {
        output.WriteRawTag(18);
        output.WriteString(UserId);
      }
      if (ItemId != 0L) {
        output.WriteRawTag(24);
        output.WriteInt64(ItemId);
      }
      if (Type != global::Common.TimerType.Use) {
        output.WriteRawTag(32);
        output.WriteEnum((int) Type);
      }
      if (DeadTime.Length != 0) {
        output.WriteRawTag(42);
        output.WriteString(DeadTime);
      }
      if (StartTime.Length != 0) {
        output.WriteRawTag(50);
        output.WriteString(StartTime);
      }
      if (_unknownFields != null) {
        _unknownFields.WriteTo(output);
      }
    #endif
    }

    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    void pb::IBufferMessage.InternalWriteTo(ref pb::WriteContext output) {
      if (Id.Length != 0) {
        output.WriteRawTag(10);
        output.WriteString(Id);
      }
      if (UserId.Length != 0) {
        output.WriteRawTag(18);
        output.WriteString(UserId);
      }
      if (ItemId != 0L) {
        output.WriteRawTag(24);
        output.WriteInt64(ItemId);
      }
      if (Type != global::Common.TimerType.Use) {
        output.WriteRawTag(32);
        output.WriteEnum((int) Type);
      }
      if (DeadTime.Length != 0) {
        output.WriteRawTag(42);
        output.WriteString(DeadTime);
      }
      if (StartTime.Length != 0) {
        output.WriteRawTag(50);
        output.WriteString(StartTime);
      }
      if (_unknownFields != null) {
        _unknownFields.WriteTo(ref output);
      }
    }
    #endif

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public int CalculateSize() {
      int size = 0;
      if (Id.Length != 0) {
        size += 1 + pb::CodedOutputStream.ComputeStringSize(Id);
      }
      if (UserId.Length != 0) {
        size += 1 + pb::CodedOutputStream.ComputeStringSize(UserId);
      }
      if (ItemId != 0L) {
        size += 1 + pb::CodedOutputStream.ComputeInt64Size(ItemId);
      }
      if (Type != global::Common.TimerType.Use) {
        size += 1 + pb::CodedOutputStream.ComputeEnumSize((int) Type);
      }
      if (DeadTime.Length != 0) {
        size += 1 + pb::CodedOutputStream.ComputeStringSize(DeadTime);
      }
      if (StartTime.Length != 0) {
        size += 1 + pb::CodedOutputStream.ComputeStringSize(StartTime);
      }
      if (_unknownFields != null) {
        size += _unknownFields.CalculateSize();
      }
      return size;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void MergeFrom(Timer other) {
      if (other == null) {
        return;
      }
      if (other.Id.Length != 0) {
        Id = other.Id;
      }
      if (other.UserId.Length != 0) {
        UserId = other.UserId;
      }
      if (other.ItemId != 0L) {
        ItemId = other.ItemId;
      }
      if (other.Type != global::Common.TimerType.Use) {
        Type = other.Type;
      }
      if (other.DeadTime.Length != 0) {
        DeadTime = other.DeadTime;
      }
      if (other.StartTime.Length != 0) {
        StartTime = other.StartTime;
      }
      _unknownFields = pb::UnknownFieldSet.MergeFrom(_unknownFields, other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void MergeFrom(pb::CodedInputStream input) {
    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      input.ReadRawMessage(this);
    #else
      uint tag;
      while ((tag = input.ReadTag()) != 0) {
        switch(tag) {
          default:
            _unknownFields = pb::UnknownFieldSet.MergeFieldFrom(_unknownFields, input);
            break;
          case 10: {
            Id = input.ReadString();
            break;
          }
          case 18: {
            UserId = input.ReadString();
            break;
          }
          case 24: {
            ItemId = input.ReadInt64();
            break;
          }
          case 32: {
            Type = (global::Common.TimerType) input.ReadEnum();
            break;
          }
          case 42: {
            DeadTime = input.ReadString();
            break;
          }
          case 50: {
            StartTime = input.ReadString();
            break;
          }
        }
      }
    #endif
    }

    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    void pb::IBufferMessage.InternalMergeFrom(ref pb::ParseContext input) {
      uint tag;
      while ((tag = input.ReadTag()) != 0) {
        switch(tag) {
          default:
            _unknownFields = pb::UnknownFieldSet.MergeFieldFrom(_unknownFields, ref input);
            break;
          case 10: {
            Id = input.ReadString();
            break;
          }
          case 18: {
            UserId = input.ReadString();
            break;
          }
          case 24: {
            ItemId = input.ReadInt64();
            break;
          }
          case 32: {
            Type = (global::Common.TimerType) input.ReadEnum();
            break;
          }
          case 42: {
            DeadTime = input.ReadString();
            break;
          }
          case 50: {
            StartTime = input.ReadString();
            break;
          }
        }
      }
    }
    #endif

  }

  [global::System.Diagnostics.DebuggerDisplayAttribute("{ToString(),nq}")]
  public sealed partial class TimerResponse : pb::IMessage<TimerResponse>
  #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      , pb::IBufferMessage
  #endif
  {
    private static readonly pb::MessageParser<TimerResponse> _parser = new pb::MessageParser<TimerResponse>(() => new TimerResponse());
    private pb::UnknownFieldSet _unknownFields;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public static pb::MessageParser<TimerResponse> Parser { get { return _parser; } }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public static pbr::MessageDescriptor Descriptor {
      get { return global::Common.CommonReflection.Descriptor.MessageTypes[28]; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    pbr::MessageDescriptor pb::IMessage.Descriptor {
      get { return Descriptor; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public TimerResponse() {
      OnConstruction();
    }

    partial void OnConstruction();

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public TimerResponse(TimerResponse other) : this() {
      timer_ = other.timer_ != null ? other.timer_.Clone() : null;
      status_ = other.status_ != null ? other.status_.Clone() : null;
      _unknownFields = pb::UnknownFieldSet.Clone(other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public TimerResponse Clone() {
      return new TimerResponse(this);
    }

    /// <summary>Field number for the "timer" field.</summary>
    public const int TimerFieldNumber = 1;
    private global::Common.Timer timer_;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public global::Common.Timer Timer {
      get { return timer_; }
      set {
        timer_ = value;
      }
    }

    /// <summary>Field number for the "status" field.</summary>
    public const int StatusFieldNumber = 2;
    private global::Common.StatusResponse status_;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public global::Common.StatusResponse Status {
      get { return status_; }
      set {
        status_ = value;
      }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override bool Equals(object other) {
      return Equals(other as TimerResponse);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public bool Equals(TimerResponse other) {
      if (ReferenceEquals(other, null)) {
        return false;
      }
      if (ReferenceEquals(other, this)) {
        return true;
      }
      if (!object.Equals(Timer, other.Timer)) return false;
      if (!object.Equals(Status, other.Status)) return false;
      return Equals(_unknownFields, other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override int GetHashCode() {
      int hash = 1;
      if (timer_ != null) hash ^= Timer.GetHashCode();
      if (status_ != null) hash ^= Status.GetHashCode();
      if (_unknownFields != null) {
        hash ^= _unknownFields.GetHashCode();
      }
      return hash;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override string ToString() {
      return pb::JsonFormatter.ToDiagnosticString(this);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void WriteTo(pb::CodedOutputStream output) {
    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      output.WriteRawMessage(this);
    #else
      if (timer_ != null) {
        output.WriteRawTag(10);
        output.WriteMessage(Timer);
      }
      if (status_ != null) {
        output.WriteRawTag(18);
        output.WriteMessage(Status);
      }
      if (_unknownFields != null) {
        _unknownFields.WriteTo(output);
      }
    #endif
    }

    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    void pb::IBufferMessage.InternalWriteTo(ref pb::WriteContext output) {
      if (timer_ != null) {
        output.WriteRawTag(10);
        output.WriteMessage(Timer);
      }
      if (status_ != null) {
        output.WriteRawTag(18);
        output.WriteMessage(Status);
      }
      if (_unknownFields != null) {
        _unknownFields.WriteTo(ref output);
      }
    }
    #endif

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public int CalculateSize() {
      int size = 0;
      if (timer_ != null) {
        size += 1 + pb::CodedOutputStream.ComputeMessageSize(Timer);
      }
      if (status_ != null) {
        size += 1 + pb::CodedOutputStream.ComputeMessageSize(Status);
      }
      if (_unknownFields != null) {
        size += _unknownFields.CalculateSize();
      }
      return size;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void MergeFrom(TimerResponse other) {
      if (other == null) {
        return;
      }
      if (other.timer_ != null) {
        if (timer_ == null) {
          Timer = new global::Common.Timer();
        }
        Timer.MergeFrom(other.Timer);
      }
      if (other.status_ != null) {
        if (status_ == null) {
          Status = new global::Common.StatusResponse();
        }
        Status.MergeFrom(other.Status);
      }
      _unknownFields = pb::UnknownFieldSet.MergeFrom(_unknownFields, other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void MergeFrom(pb::CodedInputStream input) {
    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      input.ReadRawMessage(this);
    #else
      uint tag;
      while ((tag = input.ReadTag()) != 0) {
        switch(tag) {
          default:
            _unknownFields = pb::UnknownFieldSet.MergeFieldFrom(_unknownFields, input);
            break;
          case 10: {
            if (timer_ == null) {
              Timer = new global::Common.Timer();
            }
            input.ReadMessage(Timer);
            break;
          }
          case 18: {
            if (status_ == null) {
              Status = new global::Common.StatusResponse();
            }
            input.ReadMessage(Status);
            break;
          }
        }
      }
    #endif
    }

    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    void pb::IBufferMessage.InternalMergeFrom(ref pb::ParseContext input) {
      uint tag;
      while ((tag = input.ReadTag()) != 0) {
        switch(tag) {
          default:
            _unknownFields = pb::UnknownFieldSet.MergeFieldFrom(_unknownFields, ref input);
            break;
          case 10: {
            if (timer_ == null) {
              Timer = new global::Common.Timer();
            }
            input.ReadMessage(Timer);
            break;
          }
          case 18: {
            if (status_ == null) {
              Status = new global::Common.StatusResponse();
            }
            input.ReadMessage(Status);
            break;
          }
        }
      }
    }
    #endif

  }

  #endregion

}

#endregion Designer generated code
