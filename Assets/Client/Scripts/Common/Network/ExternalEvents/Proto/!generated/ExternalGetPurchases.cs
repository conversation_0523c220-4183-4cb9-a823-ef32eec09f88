// <auto-generated>
//     Generated by the protocol buffer compiler.  DO NOT EDIT!
//     source: external/external-get-purchases.proto
// </auto-generated>
#pragma warning disable 1591, 0612, 3021, 8981
#region Designer generated code

using pb = global::Google.Protobuf;
using pbc = global::Google.Protobuf.Collections;
using pbr = global::Google.Protobuf.Reflection;
using scg = global::System.Collections.Generic;
namespace External {

  /// <summary>Holder for reflection information generated from external/external-get-purchases.proto</summary>
  public static partial class ExternalGetPurchasesReflection {

    #region Descriptor
    /// <summary>File descriptor for external/external-get-purchases.proto</summary>
    public static pbr::FileDescriptor Descriptor {
      get { return descriptor; }
    }
    private static pbr::FileDescriptor descriptor;

    static ExternalGetPurchasesReflection() {
      byte[] descriptorData = global::System.Convert.FromBase64String(
          string.Concat(
            "CiVleHRlcm5hbC9leHRlcm5hbC1nZXQtcHVyY2hhc2VzLnByb3RvEghleHRl",
            "cm5hbBoMY29tbW9uLnByb3RvImAKG0V4dGVybmFsR2V0UHVyY2hhc2VzUmVx",
            "dWVzdBJBChhmaXJzdF90aW1lX3NlZW5fcHVyY2hhc2UYASADKAsyHy5leHRl",
            "cm5hbC5GaXJzdFRpbWVTZWVuUHVyY2hhc2UiTQocRXh0ZXJuYWxHZXRQdXJj",
            "aGFzZXNSZXNwb25zZRItCglwdXJjaGFzZXMYASADKAsyGi5leHRlcm5hbC5Q",
            "dXJjaGFzZVJlc3BvbnNlIjcKFUZpcnN0VGltZVNlZW5QdXJjaGFzZRIQCghz",
            "dG9yZV9pZBgBIAEoCRIMCgRkYXRlGAIgASgDIr0CChBQdXJjaGFzZVJlc3Bv",
            "bnNlEgoKAmlkGAEgASgDEhAKCHN0b3JlX2lkGAIgASgJEiQKBWl0ZW1zGAMg",
            "AygLMhUuY29tbW9uLkl0ZW1Db21wb25lbnQSJgoIY3VycmVuY3kYBCABKA4y",
            "FC5jb21tb24uQ3VycmVuY3lUeXBlEg0KBXByaWNlGAUgASgBEhcKD2V4cGly",
            "YXRpb25fdGltZRgGIAEoAxIRCglvbGRfcHJpY2UYByABKAESGAoQZGlzY291",
            "bnRfcGVyY2VudBgIIAEoBRISCgptdWx0aXBsaWVyGAkgASgFEg0KBWxhYmVs",
            "GAogASgJEgsKA3RhYhgLIAEoCRITCgtvcmRlcl9pbmRleBgMIAEoBRINCgV0",
            "aW1lchgNIAEoAxIUCgxsaW1pdF9hbW91bnQYDiABKAViBnByb3RvMw=="));
      descriptor = pbr::FileDescriptor.FromGeneratedCode(descriptorData,
          new pbr::FileDescriptor[] { global::Common.CommonReflection.Descriptor, },
          new pbr::GeneratedClrTypeInfo(null, null, new pbr::GeneratedClrTypeInfo[] {
            new pbr::GeneratedClrTypeInfo(typeof(global::External.ExternalGetPurchasesRequest), global::External.ExternalGetPurchasesRequest.Parser, new[]{ "FirstTimeSeenPurchase" }, null, null, null, null),
            new pbr::GeneratedClrTypeInfo(typeof(global::External.ExternalGetPurchasesResponse), global::External.ExternalGetPurchasesResponse.Parser, new[]{ "Purchases" }, null, null, null, null),
            new pbr::GeneratedClrTypeInfo(typeof(global::External.FirstTimeSeenPurchase), global::External.FirstTimeSeenPurchase.Parser, new[]{ "StoreId", "Date" }, null, null, null, null),
            new pbr::GeneratedClrTypeInfo(typeof(global::External.PurchaseResponse), global::External.PurchaseResponse.Parser, new[]{ "Id", "StoreId", "Items", "Currency", "Price", "ExpirationTime", "OldPrice", "DiscountPercent", "Multiplier", "Label", "Tab", "OrderIndex", "Timer", "LimitAmount" }, null, null, null, null)
          }));
    }
    #endregion

  }
  #region Messages
  [global::System.Diagnostics.DebuggerDisplayAttribute("{ToString(),nq}")]
  public sealed partial class ExternalGetPurchasesRequest : pb::IMessage<ExternalGetPurchasesRequest>
  #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      , pb::IBufferMessage
  #endif
  {
    private static readonly pb::MessageParser<ExternalGetPurchasesRequest> _parser = new pb::MessageParser<ExternalGetPurchasesRequest>(() => new ExternalGetPurchasesRequest());
    private pb::UnknownFieldSet _unknownFields;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public static pb::MessageParser<ExternalGetPurchasesRequest> Parser { get { return _parser; } }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public static pbr::MessageDescriptor Descriptor {
      get { return global::External.ExternalGetPurchasesReflection.Descriptor.MessageTypes[0]; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    pbr::MessageDescriptor pb::IMessage.Descriptor {
      get { return Descriptor; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public ExternalGetPurchasesRequest() {
      OnConstruction();
    }

    partial void OnConstruction();

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public ExternalGetPurchasesRequest(ExternalGetPurchasesRequest other) : this() {
      firstTimeSeenPurchase_ = other.firstTimeSeenPurchase_.Clone();
      _unknownFields = pb::UnknownFieldSet.Clone(other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public ExternalGetPurchasesRequest Clone() {
      return new ExternalGetPurchasesRequest(this);
    }

    /// <summary>Field number for the "first_time_seen_purchase" field.</summary>
    public const int FirstTimeSeenPurchaseFieldNumber = 1;
    private static readonly pb::FieldCodec<global::External.FirstTimeSeenPurchase> _repeated_firstTimeSeenPurchase_codec
        = pb::FieldCodec.ForMessage(10, global::External.FirstTimeSeenPurchase.Parser);
    private readonly pbc::RepeatedField<global::External.FirstTimeSeenPurchase> firstTimeSeenPurchase_ = new pbc::RepeatedField<global::External.FirstTimeSeenPurchase>();
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public pbc::RepeatedField<global::External.FirstTimeSeenPurchase> FirstTimeSeenPurchase {
      get { return firstTimeSeenPurchase_; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override bool Equals(object other) {
      return Equals(other as ExternalGetPurchasesRequest);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public bool Equals(ExternalGetPurchasesRequest other) {
      if (ReferenceEquals(other, null)) {
        return false;
      }
      if (ReferenceEquals(other, this)) {
        return true;
      }
      if(!firstTimeSeenPurchase_.Equals(other.firstTimeSeenPurchase_)) return false;
      return Equals(_unknownFields, other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override int GetHashCode() {
      int hash = 1;
      hash ^= firstTimeSeenPurchase_.GetHashCode();
      if (_unknownFields != null) {
        hash ^= _unknownFields.GetHashCode();
      }
      return hash;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override string ToString() {
      return pb::JsonFormatter.ToDiagnosticString(this);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void WriteTo(pb::CodedOutputStream output) {
    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      output.WriteRawMessage(this);
    #else
      firstTimeSeenPurchase_.WriteTo(output, _repeated_firstTimeSeenPurchase_codec);
      if (_unknownFields != null) {
        _unknownFields.WriteTo(output);
      }
    #endif
    }

    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    void pb::IBufferMessage.InternalWriteTo(ref pb::WriteContext output) {
      firstTimeSeenPurchase_.WriteTo(ref output, _repeated_firstTimeSeenPurchase_codec);
      if (_unknownFields != null) {
        _unknownFields.WriteTo(ref output);
      }
    }
    #endif

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public int CalculateSize() {
      int size = 0;
      size += firstTimeSeenPurchase_.CalculateSize(_repeated_firstTimeSeenPurchase_codec);
      if (_unknownFields != null) {
        size += _unknownFields.CalculateSize();
      }
      return size;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void MergeFrom(ExternalGetPurchasesRequest other) {
      if (other == null) {
        return;
      }
      firstTimeSeenPurchase_.Add(other.firstTimeSeenPurchase_);
      _unknownFields = pb::UnknownFieldSet.MergeFrom(_unknownFields, other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void MergeFrom(pb::CodedInputStream input) {
    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      input.ReadRawMessage(this);
    #else
      uint tag;
      while ((tag = input.ReadTag()) != 0) {
        switch(tag) {
          default:
            _unknownFields = pb::UnknownFieldSet.MergeFieldFrom(_unknownFields, input);
            break;
          case 10: {
            firstTimeSeenPurchase_.AddEntriesFrom(input, _repeated_firstTimeSeenPurchase_codec);
            break;
          }
        }
      }
    #endif
    }

    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    void pb::IBufferMessage.InternalMergeFrom(ref pb::ParseContext input) {
      uint tag;
      while ((tag = input.ReadTag()) != 0) {
        switch(tag) {
          default:
            _unknownFields = pb::UnknownFieldSet.MergeFieldFrom(_unknownFields, ref input);
            break;
          case 10: {
            firstTimeSeenPurchase_.AddEntriesFrom(ref input, _repeated_firstTimeSeenPurchase_codec);
            break;
          }
        }
      }
    }
    #endif

  }

  [global::System.Diagnostics.DebuggerDisplayAttribute("{ToString(),nq}")]
  public sealed partial class ExternalGetPurchasesResponse : pb::IMessage<ExternalGetPurchasesResponse>
  #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      , pb::IBufferMessage
  #endif
  {
    private static readonly pb::MessageParser<ExternalGetPurchasesResponse> _parser = new pb::MessageParser<ExternalGetPurchasesResponse>(() => new ExternalGetPurchasesResponse());
    private pb::UnknownFieldSet _unknownFields;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public static pb::MessageParser<ExternalGetPurchasesResponse> Parser { get { return _parser; } }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public static pbr::MessageDescriptor Descriptor {
      get { return global::External.ExternalGetPurchasesReflection.Descriptor.MessageTypes[1]; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    pbr::MessageDescriptor pb::IMessage.Descriptor {
      get { return Descriptor; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public ExternalGetPurchasesResponse() {
      OnConstruction();
    }

    partial void OnConstruction();

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public ExternalGetPurchasesResponse(ExternalGetPurchasesResponse other) : this() {
      purchases_ = other.purchases_.Clone();
      _unknownFields = pb::UnknownFieldSet.Clone(other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public ExternalGetPurchasesResponse Clone() {
      return new ExternalGetPurchasesResponse(this);
    }

    /// <summary>Field number for the "purchases" field.</summary>
    public const int PurchasesFieldNumber = 1;
    private static readonly pb::FieldCodec<global::External.PurchaseResponse> _repeated_purchases_codec
        = pb::FieldCodec.ForMessage(10, global::External.PurchaseResponse.Parser);
    private readonly pbc::RepeatedField<global::External.PurchaseResponse> purchases_ = new pbc::RepeatedField<global::External.PurchaseResponse>();
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public pbc::RepeatedField<global::External.PurchaseResponse> Purchases {
      get { return purchases_; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override bool Equals(object other) {
      return Equals(other as ExternalGetPurchasesResponse);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public bool Equals(ExternalGetPurchasesResponse other) {
      if (ReferenceEquals(other, null)) {
        return false;
      }
      if (ReferenceEquals(other, this)) {
        return true;
      }
      if(!purchases_.Equals(other.purchases_)) return false;
      return Equals(_unknownFields, other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override int GetHashCode() {
      int hash = 1;
      hash ^= purchases_.GetHashCode();
      if (_unknownFields != null) {
        hash ^= _unknownFields.GetHashCode();
      }
      return hash;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override string ToString() {
      return pb::JsonFormatter.ToDiagnosticString(this);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void WriteTo(pb::CodedOutputStream output) {
    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      output.WriteRawMessage(this);
    #else
      purchases_.WriteTo(output, _repeated_purchases_codec);
      if (_unknownFields != null) {
        _unknownFields.WriteTo(output);
      }
    #endif
    }

    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    void pb::IBufferMessage.InternalWriteTo(ref pb::WriteContext output) {
      purchases_.WriteTo(ref output, _repeated_purchases_codec);
      if (_unknownFields != null) {
        _unknownFields.WriteTo(ref output);
      }
    }
    #endif

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public int CalculateSize() {
      int size = 0;
      size += purchases_.CalculateSize(_repeated_purchases_codec);
      if (_unknownFields != null) {
        size += _unknownFields.CalculateSize();
      }
      return size;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void MergeFrom(ExternalGetPurchasesResponse other) {
      if (other == null) {
        return;
      }
      purchases_.Add(other.purchases_);
      _unknownFields = pb::UnknownFieldSet.MergeFrom(_unknownFields, other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void MergeFrom(pb::CodedInputStream input) {
    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      input.ReadRawMessage(this);
    #else
      uint tag;
      while ((tag = input.ReadTag()) != 0) {
        switch(tag) {
          default:
            _unknownFields = pb::UnknownFieldSet.MergeFieldFrom(_unknownFields, input);
            break;
          case 10: {
            purchases_.AddEntriesFrom(input, _repeated_purchases_codec);
            break;
          }
        }
      }
    #endif
    }

    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    void pb::IBufferMessage.InternalMergeFrom(ref pb::ParseContext input) {
      uint tag;
      while ((tag = input.ReadTag()) != 0) {
        switch(tag) {
          default:
            _unknownFields = pb::UnknownFieldSet.MergeFieldFrom(_unknownFields, ref input);
            break;
          case 10: {
            purchases_.AddEntriesFrom(ref input, _repeated_purchases_codec);
            break;
          }
        }
      }
    }
    #endif

  }

  [global::System.Diagnostics.DebuggerDisplayAttribute("{ToString(),nq}")]
  public sealed partial class FirstTimeSeenPurchase : pb::IMessage<FirstTimeSeenPurchase>
  #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      , pb::IBufferMessage
  #endif
  {
    private static readonly pb::MessageParser<FirstTimeSeenPurchase> _parser = new pb::MessageParser<FirstTimeSeenPurchase>(() => new FirstTimeSeenPurchase());
    private pb::UnknownFieldSet _unknownFields;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public static pb::MessageParser<FirstTimeSeenPurchase> Parser { get { return _parser; } }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public static pbr::MessageDescriptor Descriptor {
      get { return global::External.ExternalGetPurchasesReflection.Descriptor.MessageTypes[2]; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    pbr::MessageDescriptor pb::IMessage.Descriptor {
      get { return Descriptor; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public FirstTimeSeenPurchase() {
      OnConstruction();
    }

    partial void OnConstruction();

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public FirstTimeSeenPurchase(FirstTimeSeenPurchase other) : this() {
      storeId_ = other.storeId_;
      date_ = other.date_;
      _unknownFields = pb::UnknownFieldSet.Clone(other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public FirstTimeSeenPurchase Clone() {
      return new FirstTimeSeenPurchase(this);
    }

    /// <summary>Field number for the "store_id" field.</summary>
    public const int StoreIdFieldNumber = 1;
    private string storeId_ = "";
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public string StoreId {
      get { return storeId_; }
      set {
        storeId_ = pb::ProtoPreconditions.CheckNotNull(value, "value");
      }
    }

    /// <summary>Field number for the "date" field.</summary>
    public const int DateFieldNumber = 2;
    private long date_;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public long Date {
      get { return date_; }
      set {
        date_ = value;
      }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override bool Equals(object other) {
      return Equals(other as FirstTimeSeenPurchase);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public bool Equals(FirstTimeSeenPurchase other) {
      if (ReferenceEquals(other, null)) {
        return false;
      }
      if (ReferenceEquals(other, this)) {
        return true;
      }
      if (StoreId != other.StoreId) return false;
      if (Date != other.Date) return false;
      return Equals(_unknownFields, other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override int GetHashCode() {
      int hash = 1;
      if (StoreId.Length != 0) hash ^= StoreId.GetHashCode();
      if (Date != 0L) hash ^= Date.GetHashCode();
      if (_unknownFields != null) {
        hash ^= _unknownFields.GetHashCode();
      }
      return hash;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override string ToString() {
      return pb::JsonFormatter.ToDiagnosticString(this);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void WriteTo(pb::CodedOutputStream output) {
    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      output.WriteRawMessage(this);
    #else
      if (StoreId.Length != 0) {
        output.WriteRawTag(10);
        output.WriteString(StoreId);
      }
      if (Date != 0L) {
        output.WriteRawTag(16);
        output.WriteInt64(Date);
      }
      if (_unknownFields != null) {
        _unknownFields.WriteTo(output);
      }
    #endif
    }

    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    void pb::IBufferMessage.InternalWriteTo(ref pb::WriteContext output) {
      if (StoreId.Length != 0) {
        output.WriteRawTag(10);
        output.WriteString(StoreId);
      }
      if (Date != 0L) {
        output.WriteRawTag(16);
        output.WriteInt64(Date);
      }
      if (_unknownFields != null) {
        _unknownFields.WriteTo(ref output);
      }
    }
    #endif

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public int CalculateSize() {
      int size = 0;
      if (StoreId.Length != 0) {
        size += 1 + pb::CodedOutputStream.ComputeStringSize(StoreId);
      }
      if (Date != 0L) {
        size += 1 + pb::CodedOutputStream.ComputeInt64Size(Date);
      }
      if (_unknownFields != null) {
        size += _unknownFields.CalculateSize();
      }
      return size;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void MergeFrom(FirstTimeSeenPurchase other) {
      if (other == null) {
        return;
      }
      if (other.StoreId.Length != 0) {
        StoreId = other.StoreId;
      }
      if (other.Date != 0L) {
        Date = other.Date;
      }
      _unknownFields = pb::UnknownFieldSet.MergeFrom(_unknownFields, other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void MergeFrom(pb::CodedInputStream input) {
    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      input.ReadRawMessage(this);
    #else
      uint tag;
      while ((tag = input.ReadTag()) != 0) {
        switch(tag) {
          default:
            _unknownFields = pb::UnknownFieldSet.MergeFieldFrom(_unknownFields, input);
            break;
          case 10: {
            StoreId = input.ReadString();
            break;
          }
          case 16: {
            Date = input.ReadInt64();
            break;
          }
        }
      }
    #endif
    }

    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    void pb::IBufferMessage.InternalMergeFrom(ref pb::ParseContext input) {
      uint tag;
      while ((tag = input.ReadTag()) != 0) {
        switch(tag) {
          default:
            _unknownFields = pb::UnknownFieldSet.MergeFieldFrom(_unknownFields, ref input);
            break;
          case 10: {
            StoreId = input.ReadString();
            break;
          }
          case 16: {
            Date = input.ReadInt64();
            break;
          }
        }
      }
    }
    #endif

  }

  [global::System.Diagnostics.DebuggerDisplayAttribute("{ToString(),nq}")]
  public sealed partial class PurchaseResponse : pb::IMessage<PurchaseResponse>
  #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      , pb::IBufferMessage
  #endif
  {
    private static readonly pb::MessageParser<PurchaseResponse> _parser = new pb::MessageParser<PurchaseResponse>(() => new PurchaseResponse());
    private pb::UnknownFieldSet _unknownFields;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public static pb::MessageParser<PurchaseResponse> Parser { get { return _parser; } }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public static pbr::MessageDescriptor Descriptor {
      get { return global::External.ExternalGetPurchasesReflection.Descriptor.MessageTypes[3]; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    pbr::MessageDescriptor pb::IMessage.Descriptor {
      get { return Descriptor; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public PurchaseResponse() {
      OnConstruction();
    }

    partial void OnConstruction();

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public PurchaseResponse(PurchaseResponse other) : this() {
      id_ = other.id_;
      storeId_ = other.storeId_;
      items_ = other.items_.Clone();
      currency_ = other.currency_;
      price_ = other.price_;
      expirationTime_ = other.expirationTime_;
      oldPrice_ = other.oldPrice_;
      discountPercent_ = other.discountPercent_;
      multiplier_ = other.multiplier_;
      label_ = other.label_;
      tab_ = other.tab_;
      orderIndex_ = other.orderIndex_;
      timer_ = other.timer_;
      limitAmount_ = other.limitAmount_;
      _unknownFields = pb::UnknownFieldSet.Clone(other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public PurchaseResponse Clone() {
      return new PurchaseResponse(this);
    }

    /// <summary>Field number for the "id" field.</summary>
    public const int IdFieldNumber = 1;
    private long id_;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public long Id {
      get { return id_; }
      set {
        id_ = value;
      }
    }

    /// <summary>Field number for the "store_id" field.</summary>
    public const int StoreIdFieldNumber = 2;
    private string storeId_ = "";
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public string StoreId {
      get { return storeId_; }
      set {
        storeId_ = pb::ProtoPreconditions.CheckNotNull(value, "value");
      }
    }

    /// <summary>Field number for the "items" field.</summary>
    public const int ItemsFieldNumber = 3;
    private static readonly pb::FieldCodec<global::Common.ItemComponent> _repeated_items_codec
        = pb::FieldCodec.ForMessage(26, global::Common.ItemComponent.Parser);
    private readonly pbc::RepeatedField<global::Common.ItemComponent> items_ = new pbc::RepeatedField<global::Common.ItemComponent>();
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public pbc::RepeatedField<global::Common.ItemComponent> Items {
      get { return items_; }
    }

    /// <summary>Field number for the "currency" field.</summary>
    public const int CurrencyFieldNumber = 4;
    private global::Common.CurrencyType currency_ = global::Common.CurrencyType.UnknownCurrency;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public global::Common.CurrencyType Currency {
      get { return currency_; }
      set {
        currency_ = value;
      }
    }

    /// <summary>Field number for the "price" field.</summary>
    public const int PriceFieldNumber = 5;
    private double price_;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public double Price {
      get { return price_; }
      set {
        price_ = value;
      }
    }

    /// <summary>Field number for the "expiration_time" field.</summary>
    public const int ExpirationTimeFieldNumber = 6;
    private long expirationTime_;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public long ExpirationTime {
      get { return expirationTime_; }
      set {
        expirationTime_ = value;
      }
    }

    /// <summary>Field number for the "old_price" field.</summary>
    public const int OldPriceFieldNumber = 7;
    private double oldPrice_;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public double OldPrice {
      get { return oldPrice_; }
      set {
        oldPrice_ = value;
      }
    }

    /// <summary>Field number for the "discount_percent" field.</summary>
    public const int DiscountPercentFieldNumber = 8;
    private int discountPercent_;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public int DiscountPercent {
      get { return discountPercent_; }
      set {
        discountPercent_ = value;
      }
    }

    /// <summary>Field number for the "multiplier" field.</summary>
    public const int MultiplierFieldNumber = 9;
    private int multiplier_;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public int Multiplier {
      get { return multiplier_; }
      set {
        multiplier_ = value;
      }
    }

    /// <summary>Field number for the "label" field.</summary>
    public const int LabelFieldNumber = 10;
    private string label_ = "";
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public string Label {
      get { return label_; }
      set {
        label_ = pb::ProtoPreconditions.CheckNotNull(value, "value");
      }
    }

    /// <summary>Field number for the "tab" field.</summary>
    public const int TabFieldNumber = 11;
    private string tab_ = "";
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public string Tab {
      get { return tab_; }
      set {
        tab_ = pb::ProtoPreconditions.CheckNotNull(value, "value");
      }
    }

    /// <summary>Field number for the "order_index" field.</summary>
    public const int OrderIndexFieldNumber = 12;
    private int orderIndex_;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public int OrderIndex {
      get { return orderIndex_; }
      set {
        orderIndex_ = value;
      }
    }

    /// <summary>Field number for the "timer" field.</summary>
    public const int TimerFieldNumber = 13;
    private long timer_;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public long Timer {
      get { return timer_; }
      set {
        timer_ = value;
      }
    }

    /// <summary>Field number for the "limit_amount" field.</summary>
    public const int LimitAmountFieldNumber = 14;
    private int limitAmount_;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public int LimitAmount {
      get { return limitAmount_; }
      set {
        limitAmount_ = value;
      }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override bool Equals(object other) {
      return Equals(other as PurchaseResponse);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public bool Equals(PurchaseResponse other) {
      if (ReferenceEquals(other, null)) {
        return false;
      }
      if (ReferenceEquals(other, this)) {
        return true;
      }
      if (Id != other.Id) return false;
      if (StoreId != other.StoreId) return false;
      if(!items_.Equals(other.items_)) return false;
      if (Currency != other.Currency) return false;
      if (!pbc::ProtobufEqualityComparers.BitwiseDoubleEqualityComparer.Equals(Price, other.Price)) return false;
      if (ExpirationTime != other.ExpirationTime) return false;
      if (!pbc::ProtobufEqualityComparers.BitwiseDoubleEqualityComparer.Equals(OldPrice, other.OldPrice)) return false;
      if (DiscountPercent != other.DiscountPercent) return false;
      if (Multiplier != other.Multiplier) return false;
      if (Label != other.Label) return false;
      if (Tab != other.Tab) return false;
      if (OrderIndex != other.OrderIndex) return false;
      if (Timer != other.Timer) return false;
      if (LimitAmount != other.LimitAmount) return false;
      return Equals(_unknownFields, other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override int GetHashCode() {
      int hash = 1;
      if (Id != 0L) hash ^= Id.GetHashCode();
      if (StoreId.Length != 0) hash ^= StoreId.GetHashCode();
      hash ^= items_.GetHashCode();
      if (Currency != global::Common.CurrencyType.UnknownCurrency) hash ^= Currency.GetHashCode();
      if (Price != 0D) hash ^= pbc::ProtobufEqualityComparers.BitwiseDoubleEqualityComparer.GetHashCode(Price);
      if (ExpirationTime != 0L) hash ^= ExpirationTime.GetHashCode();
      if (OldPrice != 0D) hash ^= pbc::ProtobufEqualityComparers.BitwiseDoubleEqualityComparer.GetHashCode(OldPrice);
      if (DiscountPercent != 0) hash ^= DiscountPercent.GetHashCode();
      if (Multiplier != 0) hash ^= Multiplier.GetHashCode();
      if (Label.Length != 0) hash ^= Label.GetHashCode();
      if (Tab.Length != 0) hash ^= Tab.GetHashCode();
      if (OrderIndex != 0) hash ^= OrderIndex.GetHashCode();
      if (Timer != 0L) hash ^= Timer.GetHashCode();
      if (LimitAmount != 0) hash ^= LimitAmount.GetHashCode();
      if (_unknownFields != null) {
        hash ^= _unknownFields.GetHashCode();
      }
      return hash;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override string ToString() {
      return pb::JsonFormatter.ToDiagnosticString(this);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void WriteTo(pb::CodedOutputStream output) {
    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      output.WriteRawMessage(this);
    #else
      if (Id != 0L) {
        output.WriteRawTag(8);
        output.WriteInt64(Id);
      }
      if (StoreId.Length != 0) {
        output.WriteRawTag(18);
        output.WriteString(StoreId);
      }
      items_.WriteTo(output, _repeated_items_codec);
      if (Currency != global::Common.CurrencyType.UnknownCurrency) {
        output.WriteRawTag(32);
        output.WriteEnum((int) Currency);
      }
      if (Price != 0D) {
        output.WriteRawTag(41);
        output.WriteDouble(Price);
      }
      if (ExpirationTime != 0L) {
        output.WriteRawTag(48);
        output.WriteInt64(ExpirationTime);
      }
      if (OldPrice != 0D) {
        output.WriteRawTag(57);
        output.WriteDouble(OldPrice);
      }
      if (DiscountPercent != 0) {
        output.WriteRawTag(64);
        output.WriteInt32(DiscountPercent);
      }
      if (Multiplier != 0) {
        output.WriteRawTag(72);
        output.WriteInt32(Multiplier);
      }
      if (Label.Length != 0) {
        output.WriteRawTag(82);
        output.WriteString(Label);
      }
      if (Tab.Length != 0) {
        output.WriteRawTag(90);
        output.WriteString(Tab);
      }
      if (OrderIndex != 0) {
        output.WriteRawTag(96);
        output.WriteInt32(OrderIndex);
      }
      if (Timer != 0L) {
        output.WriteRawTag(104);
        output.WriteInt64(Timer);
      }
      if (LimitAmount != 0) {
        output.WriteRawTag(112);
        output.WriteInt32(LimitAmount);
      }
      if (_unknownFields != null) {
        _unknownFields.WriteTo(output);
      }
    #endif
    }

    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    void pb::IBufferMessage.InternalWriteTo(ref pb::WriteContext output) {
      if (Id != 0L) {
        output.WriteRawTag(8);
        output.WriteInt64(Id);
      }
      if (StoreId.Length != 0) {
        output.WriteRawTag(18);
        output.WriteString(StoreId);
      }
      items_.WriteTo(ref output, _repeated_items_codec);
      if (Currency != global::Common.CurrencyType.UnknownCurrency) {
        output.WriteRawTag(32);
        output.WriteEnum((int) Currency);
      }
      if (Price != 0D) {
        output.WriteRawTag(41);
        output.WriteDouble(Price);
      }
      if (ExpirationTime != 0L) {
        output.WriteRawTag(48);
        output.WriteInt64(ExpirationTime);
      }
      if (OldPrice != 0D) {
        output.WriteRawTag(57);
        output.WriteDouble(OldPrice);
      }
      if (DiscountPercent != 0) {
        output.WriteRawTag(64);
        output.WriteInt32(DiscountPercent);
      }
      if (Multiplier != 0) {
        output.WriteRawTag(72);
        output.WriteInt32(Multiplier);
      }
      if (Label.Length != 0) {
        output.WriteRawTag(82);
        output.WriteString(Label);
      }
      if (Tab.Length != 0) {
        output.WriteRawTag(90);
        output.WriteString(Tab);
      }
      if (OrderIndex != 0) {
        output.WriteRawTag(96);
        output.WriteInt32(OrderIndex);
      }
      if (Timer != 0L) {
        output.WriteRawTag(104);
        output.WriteInt64(Timer);
      }
      if (LimitAmount != 0) {
        output.WriteRawTag(112);
        output.WriteInt32(LimitAmount);
      }
      if (_unknownFields != null) {
        _unknownFields.WriteTo(ref output);
      }
    }
    #endif

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public int CalculateSize() {
      int size = 0;
      if (Id != 0L) {
        size += 1 + pb::CodedOutputStream.ComputeInt64Size(Id);
      }
      if (StoreId.Length != 0) {
        size += 1 + pb::CodedOutputStream.ComputeStringSize(StoreId);
      }
      size += items_.CalculateSize(_repeated_items_codec);
      if (Currency != global::Common.CurrencyType.UnknownCurrency) {
        size += 1 + pb::CodedOutputStream.ComputeEnumSize((int) Currency);
      }
      if (Price != 0D) {
        size += 1 + 8;
      }
      if (ExpirationTime != 0L) {
        size += 1 + pb::CodedOutputStream.ComputeInt64Size(ExpirationTime);
      }
      if (OldPrice != 0D) {
        size += 1 + 8;
      }
      if (DiscountPercent != 0) {
        size += 1 + pb::CodedOutputStream.ComputeInt32Size(DiscountPercent);
      }
      if (Multiplier != 0) {
        size += 1 + pb::CodedOutputStream.ComputeInt32Size(Multiplier);
      }
      if (Label.Length != 0) {
        size += 1 + pb::CodedOutputStream.ComputeStringSize(Label);
      }
      if (Tab.Length != 0) {
        size += 1 + pb::CodedOutputStream.ComputeStringSize(Tab);
      }
      if (OrderIndex != 0) {
        size += 1 + pb::CodedOutputStream.ComputeInt32Size(OrderIndex);
      }
      if (Timer != 0L) {
        size += 1 + pb::CodedOutputStream.ComputeInt64Size(Timer);
      }
      if (LimitAmount != 0) {
        size += 1 + pb::CodedOutputStream.ComputeInt32Size(LimitAmount);
      }
      if (_unknownFields != null) {
        size += _unknownFields.CalculateSize();
      }
      return size;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void MergeFrom(PurchaseResponse other) {
      if (other == null) {
        return;
      }
      if (other.Id != 0L) {
        Id = other.Id;
      }
      if (other.StoreId.Length != 0) {
        StoreId = other.StoreId;
      }
      items_.Add(other.items_);
      if (other.Currency != global::Common.CurrencyType.UnknownCurrency) {
        Currency = other.Currency;
      }
      if (other.Price != 0D) {
        Price = other.Price;
      }
      if (other.ExpirationTime != 0L) {
        ExpirationTime = other.ExpirationTime;
      }
      if (other.OldPrice != 0D) {
        OldPrice = other.OldPrice;
      }
      if (other.DiscountPercent != 0) {
        DiscountPercent = other.DiscountPercent;
      }
      if (other.Multiplier != 0) {
        Multiplier = other.Multiplier;
      }
      if (other.Label.Length != 0) {
        Label = other.Label;
      }
      if (other.Tab.Length != 0) {
        Tab = other.Tab;
      }
      if (other.OrderIndex != 0) {
        OrderIndex = other.OrderIndex;
      }
      if (other.Timer != 0L) {
        Timer = other.Timer;
      }
      if (other.LimitAmount != 0) {
        LimitAmount = other.LimitAmount;
      }
      _unknownFields = pb::UnknownFieldSet.MergeFrom(_unknownFields, other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void MergeFrom(pb::CodedInputStream input) {
    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      input.ReadRawMessage(this);
    #else
      uint tag;
      while ((tag = input.ReadTag()) != 0) {
        switch(tag) {
          default:
            _unknownFields = pb::UnknownFieldSet.MergeFieldFrom(_unknownFields, input);
            break;
          case 8: {
            Id = input.ReadInt64();
            break;
          }
          case 18: {
            StoreId = input.ReadString();
            break;
          }
          case 26: {
            items_.AddEntriesFrom(input, _repeated_items_codec);
            break;
          }
          case 32: {
            Currency = (global::Common.CurrencyType) input.ReadEnum();
            break;
          }
          case 41: {
            Price = input.ReadDouble();
            break;
          }
          case 48: {
            ExpirationTime = input.ReadInt64();
            break;
          }
          case 57: {
            OldPrice = input.ReadDouble();
            break;
          }
          case 64: {
            DiscountPercent = input.ReadInt32();
            break;
          }
          case 72: {
            Multiplier = input.ReadInt32();
            break;
          }
          case 82: {
            Label = input.ReadString();
            break;
          }
          case 90: {
            Tab = input.ReadString();
            break;
          }
          case 96: {
            OrderIndex = input.ReadInt32();
            break;
          }
          case 104: {
            Timer = input.ReadInt64();
            break;
          }
          case 112: {
            LimitAmount = input.ReadInt32();
            break;
          }
        }
      }
    #endif
    }

    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    void pb::IBufferMessage.InternalMergeFrom(ref pb::ParseContext input) {
      uint tag;
      while ((tag = input.ReadTag()) != 0) {
        switch(tag) {
          default:
            _unknownFields = pb::UnknownFieldSet.MergeFieldFrom(_unknownFields, ref input);
            break;
          case 8: {
            Id = input.ReadInt64();
            break;
          }
          case 18: {
            StoreId = input.ReadString();
            break;
          }
          case 26: {
            items_.AddEntriesFrom(ref input, _repeated_items_codec);
            break;
          }
          case 32: {
            Currency = (global::Common.CurrencyType) input.ReadEnum();
            break;
          }
          case 41: {
            Price = input.ReadDouble();
            break;
          }
          case 48: {
            ExpirationTime = input.ReadInt64();
            break;
          }
          case 57: {
            OldPrice = input.ReadDouble();
            break;
          }
          case 64: {
            DiscountPercent = input.ReadInt32();
            break;
          }
          case 72: {
            Multiplier = input.ReadInt32();
            break;
          }
          case 82: {
            Label = input.ReadString();
            break;
          }
          case 90: {
            Tab = input.ReadString();
            break;
          }
          case 96: {
            OrderIndex = input.ReadInt32();
            break;
          }
          case 104: {
            Timer = input.ReadInt64();
            break;
          }
          case 112: {
            LimitAmount = input.ReadInt32();
            break;
          }
        }
      }
    }
    #endif

  }

  #endregion

}

#endregion Designer generated code
