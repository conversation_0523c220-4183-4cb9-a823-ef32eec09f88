// <auto-generated>
//     Generated by the protocol buffer compiler.  DO NOT EDIT!
//     source: external/external-get-user-settings.proto
// </auto-generated>
#pragma warning disable 1591, 0612, 3021, 8981
#region Designer generated code

using pb = global::Google.Protobuf;
using pbc = global::Google.Protobuf.Collections;
using pbr = global::Google.Protobuf.Reflection;
using scg = global::System.Collections.Generic;
namespace External {

  /// <summary>Holder for reflection information generated from external/external-get-user-settings.proto</summary>
  public static partial class ExternalGetUserSettingsReflection {

    #region Descriptor
    /// <summary>File descriptor for external/external-get-user-settings.proto</summary>
    public static pbr::FileDescriptor Descriptor {
      get { return descriptor; }
    }
    private static pbr::FileDescriptor descriptor;

    static ExternalGetUserSettingsReflection() {
      byte[] descriptorData = global::System.Convert.FromBase64String(
          string.Concat(
            "CilleHRlcm5hbC9leHRlcm5hbC1nZXQtdXNlci1zZXR0aW5ncy5wcm90bxII",
            "ZXh0ZXJuYWwiygEKH0V4dGVybmFsR2V0VXNlclNldHRpbmdzUmVzcG9uc2US",
            "DQoFc291bmQYASABKAgSDQoFbXVzaWMYAiABKAgSEQoJdmlicmF0aW9uGAMg",
            "ASgIEhAKCGxhbmd1YWdlGAQgASgJEg8KB2NvdW50cnkYBSABKAkSHAoUcmln",
            "aHRfaGFuZGVkX2NvbnRyb2wYBiABKAgSNQoNbm90aWZpY2F0aW9ucxgHIAMo",
            "CzIeLmV4dGVybmFsLk5vdGlmaWNhdGlvblNldHRpbmdzIkIKFE5vdGlmaWNh",
            "dGlvblNldHRpbmdzEhkKEW5vdGlmaWNhdGlvbl90eXBlGAEgASgJEg8KB2Vu",
            "YWJsZWQYAiABKAhiBnByb3RvMw=="));
      descriptor = pbr::FileDescriptor.FromGeneratedCode(descriptorData,
          new pbr::FileDescriptor[] { },
          new pbr::GeneratedClrTypeInfo(null, null, new pbr::GeneratedClrTypeInfo[] {
            new pbr::GeneratedClrTypeInfo(typeof(global::External.ExternalGetUserSettingsResponse), global::External.ExternalGetUserSettingsResponse.Parser, new[]{ "Sound", "Music", "Vibration", "Language", "Country", "RightHandedControl", "Notifications" }, null, null, null, null),
            new pbr::GeneratedClrTypeInfo(typeof(global::External.NotificationSettings), global::External.NotificationSettings.Parser, new[]{ "NotificationType", "Enabled" }, null, null, null, null)
          }));
    }
    #endregion

  }
  #region Messages
  [global::System.Diagnostics.DebuggerDisplayAttribute("{ToString(),nq}")]
  public sealed partial class ExternalGetUserSettingsResponse : pb::IMessage<ExternalGetUserSettingsResponse>
  #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      , pb::IBufferMessage
  #endif
  {
    private static readonly pb::MessageParser<ExternalGetUserSettingsResponse> _parser = new pb::MessageParser<ExternalGetUserSettingsResponse>(() => new ExternalGetUserSettingsResponse());
    private pb::UnknownFieldSet _unknownFields;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public static pb::MessageParser<ExternalGetUserSettingsResponse> Parser { get { return _parser; } }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public static pbr::MessageDescriptor Descriptor {
      get { return global::External.ExternalGetUserSettingsReflection.Descriptor.MessageTypes[0]; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    pbr::MessageDescriptor pb::IMessage.Descriptor {
      get { return Descriptor; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public ExternalGetUserSettingsResponse() {
      OnConstruction();
    }

    partial void OnConstruction();

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public ExternalGetUserSettingsResponse(ExternalGetUserSettingsResponse other) : this() {
      sound_ = other.sound_;
      music_ = other.music_;
      vibration_ = other.vibration_;
      language_ = other.language_;
      country_ = other.country_;
      rightHandedControl_ = other.rightHandedControl_;
      notifications_ = other.notifications_.Clone();
      _unknownFields = pb::UnknownFieldSet.Clone(other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public ExternalGetUserSettingsResponse Clone() {
      return new ExternalGetUserSettingsResponse(this);
    }

    /// <summary>Field number for the "sound" field.</summary>
    public const int SoundFieldNumber = 1;
    private bool sound_;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public bool Sound {
      get { return sound_; }
      set {
        sound_ = value;
      }
    }

    /// <summary>Field number for the "music" field.</summary>
    public const int MusicFieldNumber = 2;
    private bool music_;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public bool Music {
      get { return music_; }
      set {
        music_ = value;
      }
    }

    /// <summary>Field number for the "vibration" field.</summary>
    public const int VibrationFieldNumber = 3;
    private bool vibration_;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public bool Vibration {
      get { return vibration_; }
      set {
        vibration_ = value;
      }
    }

    /// <summary>Field number for the "language" field.</summary>
    public const int LanguageFieldNumber = 4;
    private string language_ = "";
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public string Language {
      get { return language_; }
      set {
        language_ = pb::ProtoPreconditions.CheckNotNull(value, "value");
      }
    }

    /// <summary>Field number for the "country" field.</summary>
    public const int CountryFieldNumber = 5;
    private string country_ = "";
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public string Country {
      get { return country_; }
      set {
        country_ = pb::ProtoPreconditions.CheckNotNull(value, "value");
      }
    }

    /// <summary>Field number for the "right_handed_control" field.</summary>
    public const int RightHandedControlFieldNumber = 6;
    private bool rightHandedControl_;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public bool RightHandedControl {
      get { return rightHandedControl_; }
      set {
        rightHandedControl_ = value;
      }
    }

    /// <summary>Field number for the "notifications" field.</summary>
    public const int NotificationsFieldNumber = 7;
    private static readonly pb::FieldCodec<global::External.NotificationSettings> _repeated_notifications_codec
        = pb::FieldCodec.ForMessage(58, global::External.NotificationSettings.Parser);
    private readonly pbc::RepeatedField<global::External.NotificationSettings> notifications_ = new pbc::RepeatedField<global::External.NotificationSettings>();
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public pbc::RepeatedField<global::External.NotificationSettings> Notifications {
      get { return notifications_; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override bool Equals(object other) {
      return Equals(other as ExternalGetUserSettingsResponse);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public bool Equals(ExternalGetUserSettingsResponse other) {
      if (ReferenceEquals(other, null)) {
        return false;
      }
      if (ReferenceEquals(other, this)) {
        return true;
      }
      if (Sound != other.Sound) return false;
      if (Music != other.Music) return false;
      if (Vibration != other.Vibration) return false;
      if (Language != other.Language) return false;
      if (Country != other.Country) return false;
      if (RightHandedControl != other.RightHandedControl) return false;
      if(!notifications_.Equals(other.notifications_)) return false;
      return Equals(_unknownFields, other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override int GetHashCode() {
      int hash = 1;
      if (Sound != false) hash ^= Sound.GetHashCode();
      if (Music != false) hash ^= Music.GetHashCode();
      if (Vibration != false) hash ^= Vibration.GetHashCode();
      if (Language.Length != 0) hash ^= Language.GetHashCode();
      if (Country.Length != 0) hash ^= Country.GetHashCode();
      if (RightHandedControl != false) hash ^= RightHandedControl.GetHashCode();
      hash ^= notifications_.GetHashCode();
      if (_unknownFields != null) {
        hash ^= _unknownFields.GetHashCode();
      }
      return hash;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override string ToString() {
      return pb::JsonFormatter.ToDiagnosticString(this);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void WriteTo(pb::CodedOutputStream output) {
    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      output.WriteRawMessage(this);
    #else
      if (Sound != false) {
        output.WriteRawTag(8);
        output.WriteBool(Sound);
      }
      if (Music != false) {
        output.WriteRawTag(16);
        output.WriteBool(Music);
      }
      if (Vibration != false) {
        output.WriteRawTag(24);
        output.WriteBool(Vibration);
      }
      if (Language.Length != 0) {
        output.WriteRawTag(34);
        output.WriteString(Language);
      }
      if (Country.Length != 0) {
        output.WriteRawTag(42);
        output.WriteString(Country);
      }
      if (RightHandedControl != false) {
        output.WriteRawTag(48);
        output.WriteBool(RightHandedControl);
      }
      notifications_.WriteTo(output, _repeated_notifications_codec);
      if (_unknownFields != null) {
        _unknownFields.WriteTo(output);
      }
    #endif
    }

    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    void pb::IBufferMessage.InternalWriteTo(ref pb::WriteContext output) {
      if (Sound != false) {
        output.WriteRawTag(8);
        output.WriteBool(Sound);
      }
      if (Music != false) {
        output.WriteRawTag(16);
        output.WriteBool(Music);
      }
      if (Vibration != false) {
        output.WriteRawTag(24);
        output.WriteBool(Vibration);
      }
      if (Language.Length != 0) {
        output.WriteRawTag(34);
        output.WriteString(Language);
      }
      if (Country.Length != 0) {
        output.WriteRawTag(42);
        output.WriteString(Country);
      }
      if (RightHandedControl != false) {
        output.WriteRawTag(48);
        output.WriteBool(RightHandedControl);
      }
      notifications_.WriteTo(ref output, _repeated_notifications_codec);
      if (_unknownFields != null) {
        _unknownFields.WriteTo(ref output);
      }
    }
    #endif

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public int CalculateSize() {
      int size = 0;
      if (Sound != false) {
        size += 1 + 1;
      }
      if (Music != false) {
        size += 1 + 1;
      }
      if (Vibration != false) {
        size += 1 + 1;
      }
      if (Language.Length != 0) {
        size += 1 + pb::CodedOutputStream.ComputeStringSize(Language);
      }
      if (Country.Length != 0) {
        size += 1 + pb::CodedOutputStream.ComputeStringSize(Country);
      }
      if (RightHandedControl != false) {
        size += 1 + 1;
      }
      size += notifications_.CalculateSize(_repeated_notifications_codec);
      if (_unknownFields != null) {
        size += _unknownFields.CalculateSize();
      }
      return size;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void MergeFrom(ExternalGetUserSettingsResponse other) {
      if (other == null) {
        return;
      }
      if (other.Sound != false) {
        Sound = other.Sound;
      }
      if (other.Music != false) {
        Music = other.Music;
      }
      if (other.Vibration != false) {
        Vibration = other.Vibration;
      }
      if (other.Language.Length != 0) {
        Language = other.Language;
      }
      if (other.Country.Length != 0) {
        Country = other.Country;
      }
      if (other.RightHandedControl != false) {
        RightHandedControl = other.RightHandedControl;
      }
      notifications_.Add(other.notifications_);
      _unknownFields = pb::UnknownFieldSet.MergeFrom(_unknownFields, other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void MergeFrom(pb::CodedInputStream input) {
    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      input.ReadRawMessage(this);
    #else
      uint tag;
      while ((tag = input.ReadTag()) != 0) {
        switch(tag) {
          default:
            _unknownFields = pb::UnknownFieldSet.MergeFieldFrom(_unknownFields, input);
            break;
          case 8: {
            Sound = input.ReadBool();
            break;
          }
          case 16: {
            Music = input.ReadBool();
            break;
          }
          case 24: {
            Vibration = input.ReadBool();
            break;
          }
          case 34: {
            Language = input.ReadString();
            break;
          }
          case 42: {
            Country = input.ReadString();
            break;
          }
          case 48: {
            RightHandedControl = input.ReadBool();
            break;
          }
          case 58: {
            notifications_.AddEntriesFrom(input, _repeated_notifications_codec);
            break;
          }
        }
      }
    #endif
    }

    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    void pb::IBufferMessage.InternalMergeFrom(ref pb::ParseContext input) {
      uint tag;
      while ((tag = input.ReadTag()) != 0) {
        switch(tag) {
          default:
            _unknownFields = pb::UnknownFieldSet.MergeFieldFrom(_unknownFields, ref input);
            break;
          case 8: {
            Sound = input.ReadBool();
            break;
          }
          case 16: {
            Music = input.ReadBool();
            break;
          }
          case 24: {
            Vibration = input.ReadBool();
            break;
          }
          case 34: {
            Language = input.ReadString();
            break;
          }
          case 42: {
            Country = input.ReadString();
            break;
          }
          case 48: {
            RightHandedControl = input.ReadBool();
            break;
          }
          case 58: {
            notifications_.AddEntriesFrom(ref input, _repeated_notifications_codec);
            break;
          }
        }
      }
    }
    #endif

  }

  [global::System.Diagnostics.DebuggerDisplayAttribute("{ToString(),nq}")]
  public sealed partial class NotificationSettings : pb::IMessage<NotificationSettings>
  #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      , pb::IBufferMessage
  #endif
  {
    private static readonly pb::MessageParser<NotificationSettings> _parser = new pb::MessageParser<NotificationSettings>(() => new NotificationSettings());
    private pb::UnknownFieldSet _unknownFields;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public static pb::MessageParser<NotificationSettings> Parser { get { return _parser; } }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public static pbr::MessageDescriptor Descriptor {
      get { return global::External.ExternalGetUserSettingsReflection.Descriptor.MessageTypes[1]; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    pbr::MessageDescriptor pb::IMessage.Descriptor {
      get { return Descriptor; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public NotificationSettings() {
      OnConstruction();
    }

    partial void OnConstruction();

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public NotificationSettings(NotificationSettings other) : this() {
      notificationType_ = other.notificationType_;
      enabled_ = other.enabled_;
      _unknownFields = pb::UnknownFieldSet.Clone(other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public NotificationSettings Clone() {
      return new NotificationSettings(this);
    }

    /// <summary>Field number for the "notification_type" field.</summary>
    public const int NotificationTypeFieldNumber = 1;
    private string notificationType_ = "";
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public string NotificationType {
      get { return notificationType_; }
      set {
        notificationType_ = pb::ProtoPreconditions.CheckNotNull(value, "value");
      }
    }

    /// <summary>Field number for the "enabled" field.</summary>
    public const int EnabledFieldNumber = 2;
    private bool enabled_;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public bool Enabled {
      get { return enabled_; }
      set {
        enabled_ = value;
      }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override bool Equals(object other) {
      return Equals(other as NotificationSettings);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public bool Equals(NotificationSettings other) {
      if (ReferenceEquals(other, null)) {
        return false;
      }
      if (ReferenceEquals(other, this)) {
        return true;
      }
      if (NotificationType != other.NotificationType) return false;
      if (Enabled != other.Enabled) return false;
      return Equals(_unknownFields, other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override int GetHashCode() {
      int hash = 1;
      if (NotificationType.Length != 0) hash ^= NotificationType.GetHashCode();
      if (Enabled != false) hash ^= Enabled.GetHashCode();
      if (_unknownFields != null) {
        hash ^= _unknownFields.GetHashCode();
      }
      return hash;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override string ToString() {
      return pb::JsonFormatter.ToDiagnosticString(this);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void WriteTo(pb::CodedOutputStream output) {
    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      output.WriteRawMessage(this);
    #else
      if (NotificationType.Length != 0) {
        output.WriteRawTag(10);
        output.WriteString(NotificationType);
      }
      if (Enabled != false) {
        output.WriteRawTag(16);
        output.WriteBool(Enabled);
      }
      if (_unknownFields != null) {
        _unknownFields.WriteTo(output);
      }
    #endif
    }

    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    void pb::IBufferMessage.InternalWriteTo(ref pb::WriteContext output) {
      if (NotificationType.Length != 0) {
        output.WriteRawTag(10);
        output.WriteString(NotificationType);
      }
      if (Enabled != false) {
        output.WriteRawTag(16);
        output.WriteBool(Enabled);
      }
      if (_unknownFields != null) {
        _unknownFields.WriteTo(ref output);
      }
    }
    #endif

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public int CalculateSize() {
      int size = 0;
      if (NotificationType.Length != 0) {
        size += 1 + pb::CodedOutputStream.ComputeStringSize(NotificationType);
      }
      if (Enabled != false) {
        size += 1 + 1;
      }
      if (_unknownFields != null) {
        size += _unknownFields.CalculateSize();
      }
      return size;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void MergeFrom(NotificationSettings other) {
      if (other == null) {
        return;
      }
      if (other.NotificationType.Length != 0) {
        NotificationType = other.NotificationType;
      }
      if (other.Enabled != false) {
        Enabled = other.Enabled;
      }
      _unknownFields = pb::UnknownFieldSet.MergeFrom(_unknownFields, other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void MergeFrom(pb::CodedInputStream input) {
    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      input.ReadRawMessage(this);
    #else
      uint tag;
      while ((tag = input.ReadTag()) != 0) {
        switch(tag) {
          default:
            _unknownFields = pb::UnknownFieldSet.MergeFieldFrom(_unknownFields, input);
            break;
          case 10: {
            NotificationType = input.ReadString();
            break;
          }
          case 16: {
            Enabled = input.ReadBool();
            break;
          }
        }
      }
    #endif
    }

    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    void pb::IBufferMessage.InternalMergeFrom(ref pb::ParseContext input) {
      uint tag;
      while ((tag = input.ReadTag()) != 0) {
        switch(tag) {
          default:
            _unknownFields = pb::UnknownFieldSet.MergeFieldFrom(_unknownFields, ref input);
            break;
          case 10: {
            NotificationType = input.ReadString();
            break;
          }
          case 16: {
            Enabled = input.ReadBool();
            break;
          }
        }
      }
    }
    #endif

  }

  #endregion

}

#endregion Designer generated code
