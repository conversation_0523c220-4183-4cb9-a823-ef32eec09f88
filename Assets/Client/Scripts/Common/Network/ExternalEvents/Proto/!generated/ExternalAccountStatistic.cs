// <auto-generated>
//     Generated by the protocol buffer compiler.  DO NOT EDIT!
//     source: external/external-account-statistic.proto
// </auto-generated>
#pragma warning disable 1591, 0612, 3021, 8981
#region Designer generated code

using pb = global::Google.Protobuf;
using pbc = global::Google.Protobuf.Collections;
using pbr = global::Google.Protobuf.Reflection;
using scg = global::System.Collections.Generic;
namespace External {

  /// <summary>Holder for reflection information generated from external/external-account-statistic.proto</summary>
  public static partial class ExternalAccountStatisticReflection {

    #region Descriptor
    /// <summary>File descriptor for external/external-account-statistic.proto</summary>
    public static pbr::FileDescriptor Descriptor {
      get { return descriptor; }
    }
    private static pbr::FileDescriptor descriptor;

    static ExternalAccountStatisticReflection() {
      byte[] descriptorData = global::System.Convert.FromBase64String(
          string.Concat(
            "CilleHRlcm5hbC9leHRlcm5hbC1hY2NvdW50LXN0YXRpc3RpYy5wcm90bxII",
            "ZXh0ZXJuYWwitQEKGEV4dGVybmFsQWNjb3VudFN0YXRpc3RpYxIPCgd1c2Vy",
            "X2lkGAEgASgJEg4KBmZpZ2h0cxgCIAEoAxIQCgh3aW5fcmF0ZRgDIAEoAhIQ",
            "CghoYXJwb29ucxgEIAEoAxIRCgloZWFkc2hvdHMYBSABKAMSDgoGbG9vdGVk",
            "GAYgASgDEg4KBmhlYWxlchgHIAEoAxIQCgh1bmxvY2tlchgIIAEoAxIPCgdj",
            "cmFmdGVyGAkgASgDYgZwcm90bzM="));
      descriptor = pbr::FileDescriptor.FromGeneratedCode(descriptorData,
          new pbr::FileDescriptor[] { },
          new pbr::GeneratedClrTypeInfo(null, null, new pbr::GeneratedClrTypeInfo[] {
            new pbr::GeneratedClrTypeInfo(typeof(global::External.ExternalAccountStatistic), global::External.ExternalAccountStatistic.Parser, new[]{ "UserId", "Fights", "WinRate", "Harpoons", "Headshots", "Looted", "Healer", "Unlocker", "Crafter" }, null, null, null, null)
          }));
    }
    #endregion

  }
  #region Messages
  [global::System.Diagnostics.DebuggerDisplayAttribute("{ToString(),nq}")]
  public sealed partial class ExternalAccountStatistic : pb::IMessage<ExternalAccountStatistic>
  #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      , pb::IBufferMessage
  #endif
  {
    private static readonly pb::MessageParser<ExternalAccountStatistic> _parser = new pb::MessageParser<ExternalAccountStatistic>(() => new ExternalAccountStatistic());
    private pb::UnknownFieldSet _unknownFields;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public static pb::MessageParser<ExternalAccountStatistic> Parser { get { return _parser; } }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public static pbr::MessageDescriptor Descriptor {
      get { return global::External.ExternalAccountStatisticReflection.Descriptor.MessageTypes[0]; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    pbr::MessageDescriptor pb::IMessage.Descriptor {
      get { return Descriptor; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public ExternalAccountStatistic() {
      OnConstruction();
    }

    partial void OnConstruction();

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public ExternalAccountStatistic(ExternalAccountStatistic other) : this() {
      userId_ = other.userId_;
      fights_ = other.fights_;
      winRate_ = other.winRate_;
      harpoons_ = other.harpoons_;
      headshots_ = other.headshots_;
      looted_ = other.looted_;
      healer_ = other.healer_;
      unlocker_ = other.unlocker_;
      crafter_ = other.crafter_;
      _unknownFields = pb::UnknownFieldSet.Clone(other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public ExternalAccountStatistic Clone() {
      return new ExternalAccountStatistic(this);
    }

    /// <summary>Field number for the "user_id" field.</summary>
    public const int UserIdFieldNumber = 1;
    private string userId_ = "";
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public string UserId {
      get { return userId_; }
      set {
        userId_ = pb::ProtoPreconditions.CheckNotNull(value, "value");
      }
    }

    /// <summary>Field number for the "fights" field.</summary>
    public const int FightsFieldNumber = 2;
    private long fights_;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public long Fights {
      get { return fights_; }
      set {
        fights_ = value;
      }
    }

    /// <summary>Field number for the "win_rate" field.</summary>
    public const int WinRateFieldNumber = 3;
    private float winRate_;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public float WinRate {
      get { return winRate_; }
      set {
        winRate_ = value;
      }
    }

    /// <summary>Field number for the "harpoons" field.</summary>
    public const int HarpoonsFieldNumber = 4;
    private long harpoons_;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public long Harpoons {
      get { return harpoons_; }
      set {
        harpoons_ = value;
      }
    }

    /// <summary>Field number for the "headshots" field.</summary>
    public const int HeadshotsFieldNumber = 5;
    private long headshots_;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public long Headshots {
      get { return headshots_; }
      set {
        headshots_ = value;
      }
    }

    /// <summary>Field number for the "looted" field.</summary>
    public const int LootedFieldNumber = 6;
    private long looted_;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public long Looted {
      get { return looted_; }
      set {
        looted_ = value;
      }
    }

    /// <summary>Field number for the "healer" field.</summary>
    public const int HealerFieldNumber = 7;
    private long healer_;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public long Healer {
      get { return healer_; }
      set {
        healer_ = value;
      }
    }

    /// <summary>Field number for the "unlocker" field.</summary>
    public const int UnlockerFieldNumber = 8;
    private long unlocker_;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public long Unlocker {
      get { return unlocker_; }
      set {
        unlocker_ = value;
      }
    }

    /// <summary>Field number for the "crafter" field.</summary>
    public const int CrafterFieldNumber = 9;
    private long crafter_;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public long Crafter {
      get { return crafter_; }
      set {
        crafter_ = value;
      }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override bool Equals(object other) {
      return Equals(other as ExternalAccountStatistic);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public bool Equals(ExternalAccountStatistic other) {
      if (ReferenceEquals(other, null)) {
        return false;
      }
      if (ReferenceEquals(other, this)) {
        return true;
      }
      if (UserId != other.UserId) return false;
      if (Fights != other.Fights) return false;
      if (!pbc::ProtobufEqualityComparers.BitwiseSingleEqualityComparer.Equals(WinRate, other.WinRate)) return false;
      if (Harpoons != other.Harpoons) return false;
      if (Headshots != other.Headshots) return false;
      if (Looted != other.Looted) return false;
      if (Healer != other.Healer) return false;
      if (Unlocker != other.Unlocker) return false;
      if (Crafter != other.Crafter) return false;
      return Equals(_unknownFields, other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override int GetHashCode() {
      int hash = 1;
      if (UserId.Length != 0) hash ^= UserId.GetHashCode();
      if (Fights != 0L) hash ^= Fights.GetHashCode();
      if (WinRate != 0F) hash ^= pbc::ProtobufEqualityComparers.BitwiseSingleEqualityComparer.GetHashCode(WinRate);
      if (Harpoons != 0L) hash ^= Harpoons.GetHashCode();
      if (Headshots != 0L) hash ^= Headshots.GetHashCode();
      if (Looted != 0L) hash ^= Looted.GetHashCode();
      if (Healer != 0L) hash ^= Healer.GetHashCode();
      if (Unlocker != 0L) hash ^= Unlocker.GetHashCode();
      if (Crafter != 0L) hash ^= Crafter.GetHashCode();
      if (_unknownFields != null) {
        hash ^= _unknownFields.GetHashCode();
      }
      return hash;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override string ToString() {
      return pb::JsonFormatter.ToDiagnosticString(this);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void WriteTo(pb::CodedOutputStream output) {
    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      output.WriteRawMessage(this);
    #else
      if (UserId.Length != 0) {
        output.WriteRawTag(10);
        output.WriteString(UserId);
      }
      if (Fights != 0L) {
        output.WriteRawTag(16);
        output.WriteInt64(Fights);
      }
      if (WinRate != 0F) {
        output.WriteRawTag(29);
        output.WriteFloat(WinRate);
      }
      if (Harpoons != 0L) {
        output.WriteRawTag(32);
        output.WriteInt64(Harpoons);
      }
      if (Headshots != 0L) {
        output.WriteRawTag(40);
        output.WriteInt64(Headshots);
      }
      if (Looted != 0L) {
        output.WriteRawTag(48);
        output.WriteInt64(Looted);
      }
      if (Healer != 0L) {
        output.WriteRawTag(56);
        output.WriteInt64(Healer);
      }
      if (Unlocker != 0L) {
        output.WriteRawTag(64);
        output.WriteInt64(Unlocker);
      }
      if (Crafter != 0L) {
        output.WriteRawTag(72);
        output.WriteInt64(Crafter);
      }
      if (_unknownFields != null) {
        _unknownFields.WriteTo(output);
      }
    #endif
    }

    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    void pb::IBufferMessage.InternalWriteTo(ref pb::WriteContext output) {
      if (UserId.Length != 0) {
        output.WriteRawTag(10);
        output.WriteString(UserId);
      }
      if (Fights != 0L) {
        output.WriteRawTag(16);
        output.WriteInt64(Fights);
      }
      if (WinRate != 0F) {
        output.WriteRawTag(29);
        output.WriteFloat(WinRate);
      }
      if (Harpoons != 0L) {
        output.WriteRawTag(32);
        output.WriteInt64(Harpoons);
      }
      if (Headshots != 0L) {
        output.WriteRawTag(40);
        output.WriteInt64(Headshots);
      }
      if (Looted != 0L) {
        output.WriteRawTag(48);
        output.WriteInt64(Looted);
      }
      if (Healer != 0L) {
        output.WriteRawTag(56);
        output.WriteInt64(Healer);
      }
      if (Unlocker != 0L) {
        output.WriteRawTag(64);
        output.WriteInt64(Unlocker);
      }
      if (Crafter != 0L) {
        output.WriteRawTag(72);
        output.WriteInt64(Crafter);
      }
      if (_unknownFields != null) {
        _unknownFields.WriteTo(ref output);
      }
    }
    #endif

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public int CalculateSize() {
      int size = 0;
      if (UserId.Length != 0) {
        size += 1 + pb::CodedOutputStream.ComputeStringSize(UserId);
      }
      if (Fights != 0L) {
        size += 1 + pb::CodedOutputStream.ComputeInt64Size(Fights);
      }
      if (WinRate != 0F) {
        size += 1 + 4;
      }
      if (Harpoons != 0L) {
        size += 1 + pb::CodedOutputStream.ComputeInt64Size(Harpoons);
      }
      if (Headshots != 0L) {
        size += 1 + pb::CodedOutputStream.ComputeInt64Size(Headshots);
      }
      if (Looted != 0L) {
        size += 1 + pb::CodedOutputStream.ComputeInt64Size(Looted);
      }
      if (Healer != 0L) {
        size += 1 + pb::CodedOutputStream.ComputeInt64Size(Healer);
      }
      if (Unlocker != 0L) {
        size += 1 + pb::CodedOutputStream.ComputeInt64Size(Unlocker);
      }
      if (Crafter != 0L) {
        size += 1 + pb::CodedOutputStream.ComputeInt64Size(Crafter);
      }
      if (_unknownFields != null) {
        size += _unknownFields.CalculateSize();
      }
      return size;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void MergeFrom(ExternalAccountStatistic other) {
      if (other == null) {
        return;
      }
      if (other.UserId.Length != 0) {
        UserId = other.UserId;
      }
      if (other.Fights != 0L) {
        Fights = other.Fights;
      }
      if (other.WinRate != 0F) {
        WinRate = other.WinRate;
      }
      if (other.Harpoons != 0L) {
        Harpoons = other.Harpoons;
      }
      if (other.Headshots != 0L) {
        Headshots = other.Headshots;
      }
      if (other.Looted != 0L) {
        Looted = other.Looted;
      }
      if (other.Healer != 0L) {
        Healer = other.Healer;
      }
      if (other.Unlocker != 0L) {
        Unlocker = other.Unlocker;
      }
      if (other.Crafter != 0L) {
        Crafter = other.Crafter;
      }
      _unknownFields = pb::UnknownFieldSet.MergeFrom(_unknownFields, other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void MergeFrom(pb::CodedInputStream input) {
    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      input.ReadRawMessage(this);
    #else
      uint tag;
      while ((tag = input.ReadTag()) != 0) {
        switch(tag) {
          default:
            _unknownFields = pb::UnknownFieldSet.MergeFieldFrom(_unknownFields, input);
            break;
          case 10: {
            UserId = input.ReadString();
            break;
          }
          case 16: {
            Fights = input.ReadInt64();
            break;
          }
          case 29: {
            WinRate = input.ReadFloat();
            break;
          }
          case 32: {
            Harpoons = input.ReadInt64();
            break;
          }
          case 40: {
            Headshots = input.ReadInt64();
            break;
          }
          case 48: {
            Looted = input.ReadInt64();
            break;
          }
          case 56: {
            Healer = input.ReadInt64();
            break;
          }
          case 64: {
            Unlocker = input.ReadInt64();
            break;
          }
          case 72: {
            Crafter = input.ReadInt64();
            break;
          }
        }
      }
    #endif
    }

    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    void pb::IBufferMessage.InternalMergeFrom(ref pb::ParseContext input) {
      uint tag;
      while ((tag = input.ReadTag()) != 0) {
        switch(tag) {
          default:
            _unknownFields = pb::UnknownFieldSet.MergeFieldFrom(_unknownFields, ref input);
            break;
          case 10: {
            UserId = input.ReadString();
            break;
          }
          case 16: {
            Fights = input.ReadInt64();
            break;
          }
          case 29: {
            WinRate = input.ReadFloat();
            break;
          }
          case 32: {
            Harpoons = input.ReadInt64();
            break;
          }
          case 40: {
            Headshots = input.ReadInt64();
            break;
          }
          case 48: {
            Looted = input.ReadInt64();
            break;
          }
          case 56: {
            Healer = input.ReadInt64();
            break;
          }
          case 64: {
            Unlocker = input.ReadInt64();
            break;
          }
          case 72: {
            Crafter = input.ReadInt64();
            break;
          }
        }
      }
    }
    #endif

  }

  #endregion

}

#endregion Designer generated code
