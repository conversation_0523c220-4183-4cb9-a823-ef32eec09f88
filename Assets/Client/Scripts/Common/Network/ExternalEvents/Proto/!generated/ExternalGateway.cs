// <auto-generated>
//     Generated by the protocol buffer compiler.  DO NOT EDIT!
//     source: external/external-gateway.proto
// </auto-generated>
#pragma warning disable 1591, 0612, 3021, 8981
#region Designer generated code

using pb = global::Google.Protobuf;
using pbc = global::Google.Protobuf.Collections;
using pbr = global::Google.Protobuf.Reflection;
using scg = global::System.Collections.Generic;
namespace External {

  /// <summary>Holder for reflection information generated from external/external-gateway.proto</summary>
  public static partial class ExternalGatewayReflection {

    #region Descriptor
    /// <summary>File descriptor for external/external-gateway.proto</summary>
    public static pbr::FileDescriptor Descriptor {
      get { return descriptor; }
    }
    private static pbr::FileDescriptor descriptor;

    static ExternalGatewayReflection() {
      byte[] descriptorData = global::System.Convert.FromBase64String(
          string.Concat(
            "Ch9leHRlcm5hbC9leHRlcm5hbC1nYXRld2F5LnByb3RvEghleHRlcm5hbBoM",
            "Y29tbW9uLnByb3RvIlcKD0V4dGVybmFsUmVxdWVzdBIzCgxyZXF1ZXN0X3R5",
            "cGUYASABKA4yHS5leHRlcm5hbC5FeHRlcm5hbFJlcXVlc3RUeXBlEg8KB3Bh",
            "eWxvYWQYAiABKAwifQoQRXh0ZXJuYWxSZXNwb25zZRIzCgxyZXF1ZXN0X3R5",
            "cGUYASABKA4yHS5leHRlcm5hbC5FeHRlcm5hbFJlcXVlc3RUeXBlEiYKBnN0",
            "YXR1cxgCIAEoCzIWLmNvbW1vbi5TdGF0dXNSZXNwb25zZRIMCgRkYXRhGAMg",
            "ASgMKu0OChNFeHRlcm5hbFJlcXVlc3RUeXBlEiEKHWV4dGVybmFsX3JlcXVl",
            "c3RfdHlwZV91bmtub3duEAASJwojZXh0ZXJuYWxfcmVxdWVzdF90eXBlX2Jp",
            "bmRfZmFjZWJvb2sQARIoCiRleHRlcm5hbF9yZXF1ZXN0X3R5cGVfc3RhcnRf",
            "ZmFjZWJvb2sQAhIpCiVleHRlcm5hbF9yZXF1ZXN0X3R5cGVfdW5iaW5kX2Zh",
            "Y2Vib29rEAMSKgomZXh0ZXJuYWxfcmVxdWVzdF90eXBlX3NldF93ZWFwb25f",
            "c2tpbnMQBBIvCitleHRlcm5hbF9yZXF1ZXN0X3R5cGVfc2V0X3NraW5fZHVy",
            "YWJpbGl0aWVzEAUSKwonZXh0ZXJuYWxfcmVxdWVzdF90eXBlX2VxdWlwX3dl",
            "YXBvbl9za2luEAYSKwonZXh0ZXJuYWxfcmVxdWVzdF90eXBlX2dldF91c2Vy",
            "X3NldHRpbmdzEAcSJQohZXh0ZXJuYWxfcmVxdWVzdF90eXBlX2xvb3RfY29y",
            "cHNlEAgSJwojZXh0ZXJuYWxfcmVxdWVzdF90eXBlX2dldF9wdXJjaGFzZXMQ",
            "CRIqCiZleHRlcm5hbF9yZXF1ZXN0X3R5cGVfcmVtb3ZlX2ludmVudG9yeRAK",
            "Ei4KKmV4dGVybmFsX3JlcXVlc3RfdHlwZV91cGRhdGVfYWNjb3VudF9zdGVw",
            "cxALEjMKL2V4dGVybmFsX3JlcXVlc3RfdHlwZV9nZXRfYWNjb3VudF9hYmls",
            "aXR5X2dyYWRlEAwSNAowZXh0ZXJuYWxfcmVxdWVzdF90eXBlX3NhdmVfYWNj",
            "b3VudF9hYmlsaXR5X2dyYWRlEA0SKAokZXh0ZXJuYWxfcmVxdWVzdF90eXBl",
            "X2dldF93aW5fc3RyZWFrEA4SOAo0ZXh0ZXJuYWxfcmVxdWVzdF90eXBlX2dl",
            "dF9hbGxfYWNjb3VudF9hYmlsaXR5X2dyYWRlcxAPEjMKL2V4dGVybmFsX3Jl",
            "cXVlc3RfdHlwZV9nZXRfbGFzdF91c2VyX2ZpbmdlcnByaW50EBASKwonZXh0",
            "ZXJuYWxfcmVxdWVzdF90eXBlX2dldF9ibG9vZF9yZXdhcmRzEBESIwofZXh0",
            "ZXJuYWxfcmVxdWVzdF90eXBlX29wZW5fc2FjaxASEiYKImV4dGVybmFsX3Jl",
            "cXVlc3RfdHlwZV9zcGVuZF9wb2NrZXQQExImCiJleHRlcm5hbF9yZXF1ZXN0",
            "X3R5cGVfZXF1aXBfcG9ja2V0EBQSJgoiZXh0ZXJuYWxfcmVxdWVzdF90eXBl",
            "X3N0YXRlX3BvY2tldBAVEicKI2V4dGVybmFsX3JlcXVlc3RfdHlwZV9hZGRf",
            "aW52ZW50b3J5EBYSLworZXh0ZXJuYWxfcmVxdWVzdF90eXBlX2dldF91c2Vy",
            "X2RhaWx5X3F1ZXN0cxAXEjAKLGV4dGVybmFsX3JlcXVlc3RfdHlwZV9zYXZl",
            "X3VzZXJfZGFpbHlfcXVlc3RzEBgSLgoqZXh0ZXJuYWxfcmVxdWVzdF90eXBl",
            "X2dldF9ndWlkZV90aW1lc3RhbXBzEBkSMQotZXh0ZXJuYWxfcmVxdWVzdF90",
            "eXBlX3VwZ3JhZGVfYWNjb3VudF9hYmlsaXR5EBoSMwovZXh0ZXJuYWxfcmVx",
            "dWVzdF90eXBlX2Rvd25ncmFkZV9hY2NvdW50X2FiaWxpdHkQGxIjCh9leHRl",
            "cm5hbF9yZXF1ZXN0X3R5cGVfZ2V0X2d1aWRlEBwSIwofZXh0ZXJuYWxfcmVx",
            "dWVzdF90eXBlX2hhcnBvb25lZBAdEisKJ2V4dGVybmFsX3JlcXVlc3RfdHlw",
            "ZV9hY2NvdW50X3N0YXRpc3RpYxAeEi4KKmV4dGVybmFsX3JlcXVlc3RfdHlw",
            "ZV9nZXRfdXNlcl9zZWFzb25faW5mbxAfEikKJWV4dGVybmFsX3JlcXVlc3Rf",
            "dHlwZV9nZXRfbGVhZ3VlX2luZm8QIBIpCiVleHRlcm5hbF9yZXF1ZXN0X3R5",
            "cGVfZ2V0X3NlYXNvbl9pbmZvECESKgomZXh0ZXJuYWxfcmVxdWVzdF90eXBl",
            "X2dldF9sZWFndWVfYXdhcmQQIhIvCitleHRlcm5hbF9yZXF1ZXN0X3R5cGVf",
            "Z2V0X2tpbGxfbG9jYWxfcmV3YXJkECMSOAo0ZXh0ZXJuYWxfcmVxdWVzdF90",
            "eXBlX2dldF9jbGFpbWVkX2tpbGxfbG9jYWxfcmV3YXJkcxAkEiwKKGV4dGVy",
            "bmFsX3JlcXVlc3RfdHlwZV9nZXRfdGVsZWdyYW1fdG9rZW4QJRIlCiFleHRl",
            "cm5hbF9yZXF1ZXN0X3R5cGVfYmluZF9nb29nbGUQJhInCiNleHRlcm5hbF9y",
            "ZXF1ZXN0X3R5cGVfdW5iaW5kX2dvb2dsZRAnEiYKImV4dGVybmFsX3JlcXVl",
            "c3RfdHlwZV9nZXRfcHJvZ3Jlc3MQKBImCiJleHRlcm5hbF9yZXF1ZXN0X3R5",
            "cGVfZHJpbmtfcG90aW9uECkyVAoKR2F0ZXdheUFwaRJGCgtTZW5kUmVxdWVz",
            "dBIZLmV4dGVybmFsLkV4dGVybmFsUmVxdWVzdBoaLmV4dGVybmFsLkV4dGVy",
            "bmFsUmVzcG9uc2UiAGIGcHJvdG8z"));
      descriptor = pbr::FileDescriptor.FromGeneratedCode(descriptorData,
          new pbr::FileDescriptor[] { global::Common.CommonReflection.Descriptor, },
          new pbr::GeneratedClrTypeInfo(new[] {typeof(global::External.ExternalRequestType), }, null, new pbr::GeneratedClrTypeInfo[] {
            new pbr::GeneratedClrTypeInfo(typeof(global::External.ExternalRequest), global::External.ExternalRequest.Parser, new[]{ "RequestType", "Payload" }, null, null, null, null),
            new pbr::GeneratedClrTypeInfo(typeof(global::External.ExternalResponse), global::External.ExternalResponse.Parser, new[]{ "RequestType", "Status", "Data" }, null, null, null, null)
          }));
    }
    #endregion

  }
  #region Enums
  public enum ExternalRequestType {
    [pbr::OriginalName("external_request_type_unknown")] Unknown = 0,
    [pbr::OriginalName("external_request_type_bind_facebook")] BindFacebook = 1,
    [pbr::OriginalName("external_request_type_start_facebook")] StartFacebook = 2,
    [pbr::OriginalName("external_request_type_unbind_facebook")] UnbindFacebook = 3,
    [pbr::OriginalName("external_request_type_set_weapon_skins")] SetWeaponSkins = 4,
    [pbr::OriginalName("external_request_type_set_skin_durabilities")] SetSkinDurabilities = 5,
    [pbr::OriginalName("external_request_type_equip_weapon_skin")] EquipWeaponSkin = 6,
    [pbr::OriginalName("external_request_type_get_user_settings")] GetUserSettings = 7,
    [pbr::OriginalName("external_request_type_loot_corpse")] LootCorpse = 8,
    [pbr::OriginalName("external_request_type_get_purchases")] GetPurchases = 9,
    [pbr::OriginalName("external_request_type_remove_inventory")] RemoveInventory = 10,
    [pbr::OriginalName("external_request_type_update_account_steps")] UpdateAccountSteps = 11,
    [pbr::OriginalName("external_request_type_get_account_ability_grade")] GetAccountAbilityGrade = 12,
    [pbr::OriginalName("external_request_type_save_account_ability_grade")] SaveAccountAbilityGrade = 13,
    [pbr::OriginalName("external_request_type_get_win_streak")] GetWinStreak = 14,
    [pbr::OriginalName("external_request_type_get_all_account_ability_grades")] GetAllAccountAbilityGrades = 15,
    [pbr::OriginalName("external_request_type_get_last_user_fingerprint")] GetLastUserFingerprint = 16,
    [pbr::OriginalName("external_request_type_get_blood_rewards")] GetBloodRewards = 17,
    [pbr::OriginalName("external_request_type_open_sack")] OpenSack = 18,
    [pbr::OriginalName("external_request_type_spend_pocket")] SpendPocket = 19,
    [pbr::OriginalName("external_request_type_equip_pocket")] EquipPocket = 20,
    [pbr::OriginalName("external_request_type_state_pocket")] StatePocket = 21,
    [pbr::OriginalName("external_request_type_add_inventory")] AddInventory = 22,
    [pbr::OriginalName("external_request_type_get_user_daily_quests")] GetUserDailyQuests = 23,
    [pbr::OriginalName("external_request_type_save_user_daily_quests")] SaveUserDailyQuests = 24,
    [pbr::OriginalName("external_request_type_get_guide_timestamps")] GetGuideTimestamps = 25,
    [pbr::OriginalName("external_request_type_upgrade_account_ability")] UpgradeAccountAbility = 26,
    [pbr::OriginalName("external_request_type_downgrade_account_ability")] DowngradeAccountAbility = 27,
    [pbr::OriginalName("external_request_type_get_guide")] GetGuide = 28,
    [pbr::OriginalName("external_request_type_harpooned")] Harpooned = 29,
    [pbr::OriginalName("external_request_type_account_statistic")] AccountStatistic = 30,
    [pbr::OriginalName("external_request_type_get_user_season_info")] GetUserSeasonInfo = 31,
    [pbr::OriginalName("external_request_type_get_league_info")] GetLeagueInfo = 32,
    [pbr::OriginalName("external_request_type_get_season_info")] GetSeasonInfo = 33,
    [pbr::OriginalName("external_request_type_get_league_award")] GetLeagueAward = 34,
    [pbr::OriginalName("external_request_type_get_kill_local_reward")] GetKillLocalReward = 35,
    [pbr::OriginalName("external_request_type_get_claimed_kill_local_rewards")] GetClaimedKillLocalRewards = 36,
    [pbr::OriginalName("external_request_type_get_telegram_token")] GetTelegramToken = 37,
    [pbr::OriginalName("external_request_type_bind_google")] BindGoogle = 38,
    [pbr::OriginalName("external_request_type_unbind_google")] UnbindGoogle = 39,
    [pbr::OriginalName("external_request_type_get_progress")] GetProgress = 40,
    [pbr::OriginalName("external_request_type_drink_potion")] DrinkPotion = 41,
  }

  #endregion

  #region Messages
  [global::System.Diagnostics.DebuggerDisplayAttribute("{ToString(),nq}")]
  public sealed partial class ExternalRequest : pb::IMessage<ExternalRequest>
  #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      , pb::IBufferMessage
  #endif
  {
    private static readonly pb::MessageParser<ExternalRequest> _parser = new pb::MessageParser<ExternalRequest>(() => new ExternalRequest());
    private pb::UnknownFieldSet _unknownFields;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public static pb::MessageParser<ExternalRequest> Parser { get { return _parser; } }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public static pbr::MessageDescriptor Descriptor {
      get { return global::External.ExternalGatewayReflection.Descriptor.MessageTypes[0]; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    pbr::MessageDescriptor pb::IMessage.Descriptor {
      get { return Descriptor; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public ExternalRequest() {
      OnConstruction();
    }

    partial void OnConstruction();

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public ExternalRequest(ExternalRequest other) : this() {
      requestType_ = other.requestType_;
      payload_ = other.payload_;
      _unknownFields = pb::UnknownFieldSet.Clone(other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public ExternalRequest Clone() {
      return new ExternalRequest(this);
    }

    /// <summary>Field number for the "request_type" field.</summary>
    public const int RequestTypeFieldNumber = 1;
    private global::External.ExternalRequestType requestType_ = global::External.ExternalRequestType.Unknown;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public global::External.ExternalRequestType RequestType {
      get { return requestType_; }
      set {
        requestType_ = value;
      }
    }

    /// <summary>Field number for the "payload" field.</summary>
    public const int PayloadFieldNumber = 2;
    private pb::ByteString payload_ = pb::ByteString.Empty;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public pb::ByteString Payload {
      get { return payload_; }
      set {
        payload_ = pb::ProtoPreconditions.CheckNotNull(value, "value");
      }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override bool Equals(object other) {
      return Equals(other as ExternalRequest);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public bool Equals(ExternalRequest other) {
      if (ReferenceEquals(other, null)) {
        return false;
      }
      if (ReferenceEquals(other, this)) {
        return true;
      }
      if (RequestType != other.RequestType) return false;
      if (Payload != other.Payload) return false;
      return Equals(_unknownFields, other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override int GetHashCode() {
      int hash = 1;
      if (RequestType != global::External.ExternalRequestType.Unknown) hash ^= RequestType.GetHashCode();
      if (Payload.Length != 0) hash ^= Payload.GetHashCode();
      if (_unknownFields != null) {
        hash ^= _unknownFields.GetHashCode();
      }
      return hash;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override string ToString() {
      return pb::JsonFormatter.ToDiagnosticString(this);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void WriteTo(pb::CodedOutputStream output) {
    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      output.WriteRawMessage(this);
    #else
      if (RequestType != global::External.ExternalRequestType.Unknown) {
        output.WriteRawTag(8);
        output.WriteEnum((int) RequestType);
      }
      if (Payload.Length != 0) {
        output.WriteRawTag(18);
        output.WriteBytes(Payload);
      }
      if (_unknownFields != null) {
        _unknownFields.WriteTo(output);
      }
    #endif
    }

    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    void pb::IBufferMessage.InternalWriteTo(ref pb::WriteContext output) {
      if (RequestType != global::External.ExternalRequestType.Unknown) {
        output.WriteRawTag(8);
        output.WriteEnum((int) RequestType);
      }
      if (Payload.Length != 0) {
        output.WriteRawTag(18);
        output.WriteBytes(Payload);
      }
      if (_unknownFields != null) {
        _unknownFields.WriteTo(ref output);
      }
    }
    #endif

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public int CalculateSize() {
      int size = 0;
      if (RequestType != global::External.ExternalRequestType.Unknown) {
        size += 1 + pb::CodedOutputStream.ComputeEnumSize((int) RequestType);
      }
      if (Payload.Length != 0) {
        size += 1 + pb::CodedOutputStream.ComputeBytesSize(Payload);
      }
      if (_unknownFields != null) {
        size += _unknownFields.CalculateSize();
      }
      return size;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void MergeFrom(ExternalRequest other) {
      if (other == null) {
        return;
      }
      if (other.RequestType != global::External.ExternalRequestType.Unknown) {
        RequestType = other.RequestType;
      }
      if (other.Payload.Length != 0) {
        Payload = other.Payload;
      }
      _unknownFields = pb::UnknownFieldSet.MergeFrom(_unknownFields, other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void MergeFrom(pb::CodedInputStream input) {
    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      input.ReadRawMessage(this);
    #else
      uint tag;
      while ((tag = input.ReadTag()) != 0) {
        switch(tag) {
          default:
            _unknownFields = pb::UnknownFieldSet.MergeFieldFrom(_unknownFields, input);
            break;
          case 8: {
            RequestType = (global::External.ExternalRequestType) input.ReadEnum();
            break;
          }
          case 18: {
            Payload = input.ReadBytes();
            break;
          }
        }
      }
    #endif
    }

    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    void pb::IBufferMessage.InternalMergeFrom(ref pb::ParseContext input) {
      uint tag;
      while ((tag = input.ReadTag()) != 0) {
        switch(tag) {
          default:
            _unknownFields = pb::UnknownFieldSet.MergeFieldFrom(_unknownFields, ref input);
            break;
          case 8: {
            RequestType = (global::External.ExternalRequestType) input.ReadEnum();
            break;
          }
          case 18: {
            Payload = input.ReadBytes();
            break;
          }
        }
      }
    }
    #endif

  }

  [global::System.Diagnostics.DebuggerDisplayAttribute("{ToString(),nq}")]
  public sealed partial class ExternalResponse : pb::IMessage<ExternalResponse>
  #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      , pb::IBufferMessage
  #endif
  {
    private static readonly pb::MessageParser<ExternalResponse> _parser = new pb::MessageParser<ExternalResponse>(() => new ExternalResponse());
    private pb::UnknownFieldSet _unknownFields;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public static pb::MessageParser<ExternalResponse> Parser { get { return _parser; } }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public static pbr::MessageDescriptor Descriptor {
      get { return global::External.ExternalGatewayReflection.Descriptor.MessageTypes[1]; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    pbr::MessageDescriptor pb::IMessage.Descriptor {
      get { return Descriptor; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public ExternalResponse() {
      OnConstruction();
    }

    partial void OnConstruction();

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public ExternalResponse(ExternalResponse other) : this() {
      requestType_ = other.requestType_;
      status_ = other.status_ != null ? other.status_.Clone() : null;
      data_ = other.data_;
      _unknownFields = pb::UnknownFieldSet.Clone(other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public ExternalResponse Clone() {
      return new ExternalResponse(this);
    }

    /// <summary>Field number for the "request_type" field.</summary>
    public const int RequestTypeFieldNumber = 1;
    private global::External.ExternalRequestType requestType_ = global::External.ExternalRequestType.Unknown;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public global::External.ExternalRequestType RequestType {
      get { return requestType_; }
      set {
        requestType_ = value;
      }
    }

    /// <summary>Field number for the "status" field.</summary>
    public const int StatusFieldNumber = 2;
    private global::Common.StatusResponse status_;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public global::Common.StatusResponse Status {
      get { return status_; }
      set {
        status_ = value;
      }
    }

    /// <summary>Field number for the "data" field.</summary>
    public const int DataFieldNumber = 3;
    private pb::ByteString data_ = pb::ByteString.Empty;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public pb::ByteString Data {
      get { return data_; }
      set {
        data_ = pb::ProtoPreconditions.CheckNotNull(value, "value");
      }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override bool Equals(object other) {
      return Equals(other as ExternalResponse);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public bool Equals(ExternalResponse other) {
      if (ReferenceEquals(other, null)) {
        return false;
      }
      if (ReferenceEquals(other, this)) {
        return true;
      }
      if (RequestType != other.RequestType) return false;
      if (!object.Equals(Status, other.Status)) return false;
      if (Data != other.Data) return false;
      return Equals(_unknownFields, other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override int GetHashCode() {
      int hash = 1;
      if (RequestType != global::External.ExternalRequestType.Unknown) hash ^= RequestType.GetHashCode();
      if (status_ != null) hash ^= Status.GetHashCode();
      if (Data.Length != 0) hash ^= Data.GetHashCode();
      if (_unknownFields != null) {
        hash ^= _unknownFields.GetHashCode();
      }
      return hash;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override string ToString() {
      return pb::JsonFormatter.ToDiagnosticString(this);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void WriteTo(pb::CodedOutputStream output) {
    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      output.WriteRawMessage(this);
    #else
      if (RequestType != global::External.ExternalRequestType.Unknown) {
        output.WriteRawTag(8);
        output.WriteEnum((int) RequestType);
      }
      if (status_ != null) {
        output.WriteRawTag(18);
        output.WriteMessage(Status);
      }
      if (Data.Length != 0) {
        output.WriteRawTag(26);
        output.WriteBytes(Data);
      }
      if (_unknownFields != null) {
        _unknownFields.WriteTo(output);
      }
    #endif
    }

    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    void pb::IBufferMessage.InternalWriteTo(ref pb::WriteContext output) {
      if (RequestType != global::External.ExternalRequestType.Unknown) {
        output.WriteRawTag(8);
        output.WriteEnum((int) RequestType);
      }
      if (status_ != null) {
        output.WriteRawTag(18);
        output.WriteMessage(Status);
      }
      if (Data.Length != 0) {
        output.WriteRawTag(26);
        output.WriteBytes(Data);
      }
      if (_unknownFields != null) {
        _unknownFields.WriteTo(ref output);
      }
    }
    #endif

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public int CalculateSize() {
      int size = 0;
      if (RequestType != global::External.ExternalRequestType.Unknown) {
        size += 1 + pb::CodedOutputStream.ComputeEnumSize((int) RequestType);
      }
      if (status_ != null) {
        size += 1 + pb::CodedOutputStream.ComputeMessageSize(Status);
      }
      if (Data.Length != 0) {
        size += 1 + pb::CodedOutputStream.ComputeBytesSize(Data);
      }
      if (_unknownFields != null) {
        size += _unknownFields.CalculateSize();
      }
      return size;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void MergeFrom(ExternalResponse other) {
      if (other == null) {
        return;
      }
      if (other.RequestType != global::External.ExternalRequestType.Unknown) {
        RequestType = other.RequestType;
      }
      if (other.status_ != null) {
        if (status_ == null) {
          Status = new global::Common.StatusResponse();
        }
        Status.MergeFrom(other.Status);
      }
      if (other.Data.Length != 0) {
        Data = other.Data;
      }
      _unknownFields = pb::UnknownFieldSet.MergeFrom(_unknownFields, other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void MergeFrom(pb::CodedInputStream input) {
    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      input.ReadRawMessage(this);
    #else
      uint tag;
      while ((tag = input.ReadTag()) != 0) {
        switch(tag) {
          default:
            _unknownFields = pb::UnknownFieldSet.MergeFieldFrom(_unknownFields, input);
            break;
          case 8: {
            RequestType = (global::External.ExternalRequestType) input.ReadEnum();
            break;
          }
          case 18: {
            if (status_ == null) {
              Status = new global::Common.StatusResponse();
            }
            input.ReadMessage(Status);
            break;
          }
          case 26: {
            Data = input.ReadBytes();
            break;
          }
        }
      }
    #endif
    }

    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    void pb::IBufferMessage.InternalMergeFrom(ref pb::ParseContext input) {
      uint tag;
      while ((tag = input.ReadTag()) != 0) {
        switch(tag) {
          default:
            _unknownFields = pb::UnknownFieldSet.MergeFieldFrom(_unknownFields, ref input);
            break;
          case 8: {
            RequestType = (global::External.ExternalRequestType) input.ReadEnum();
            break;
          }
          case 18: {
            if (status_ == null) {
              Status = new global::Common.StatusResponse();
            }
            input.ReadMessage(Status);
            break;
          }
          case 26: {
            Data = input.ReadBytes();
            break;
          }
        }
      }
    }
    #endif

  }

  #endregion

}

#endregion Designer generated code
