using System;
using System.Collections.Generic;
using External;
using Google.Protobuf;

namespace Client.Common.Network.ExternalEvents.EventReceivers
{
    public class ExternalEventReceivers
    {
        private readonly Dictionary<ExternalEventType, IExternalEventReceiver> _receivers;

        public ExternalEventReceivers()
        {
            _receivers = new Dictionary<ExternalEventType, IExternalEventReceiver>
            {
                {ExternalEventType.EventTypeChangeMainBalance, new ChangeMainBalanceReceiver()},
            };
        }


        public IExternalEventReceiver Get(ExternalEventType eventType)
        {
            return _receivers[eventType];
        }

        public IExternalEventReceiver<TReceiver> Get<TReceiver>(ExternalEventType eventType)
            where TReceiver : class, IMessage
        {
            return _receivers[eventType] as IExternalEventReceiver<TReceiver>;
        }

        public IEnumerable<IExternalEventReceiver> GetAll()
        {
            return _receivers.Values;
        }
    }

    public class ChangeMainBalanceReceiver : IExternalEventReceiver<ChangeMainBalance>
    {
         public event Action<ChangeMainBalance> Received;

         public void Receive(ExternalEvent externalEvent)
         {
            ChangeMainBalance data = ChangeMainBalance.Parser.ParseFrom(externalEvent.Data);
            Received?.Invoke(data);
         }
     }


}