using System.Collections.Generic;
using System.Linq;
using System.Threading;
using Client.Common.LocalLogic.ChangeData;
using Client.Common.Network.MetaNet;
using Client.Common.Player.Timers;
using Client.Common.TimeGiver.Implementations;
using Client.Common.UI.ScrollController.Controllers;
using Client.RegionResult.Components;
using Client.RegionResult.Services;
using Client.RegionResult.Systems;
using Client.RegionResult.UnityComponents;
using Client.Utils.Extensions;
using Client.Utils.ResourceLoading;
using Cysharp.Threading.Tasks;
using Leopotam.Ecs;
using UnityEngine;

namespace Client.RegionResult.Controllers
{
    internal class TraumasTabController: BaseTabController
    {
        private readonly ServerTimeService _serverTime;
        
        private EcsFilter<RegionOldTraumasData> _regionOldTraumasData;

        public TraumasTabController(ServerTimeService serverTime)
        {
            _serverTime = serverTime;
        }
        
        protected override void OnInit()
        {
            _regionOldTraumasData = _world.GetFilter<EcsFilter<RegionOldTraumasData>>();
        }

        public override async UniTask<TabConfig> TryCreate(ScrollGeneratorHelper scrollGenerator)
        {
            if (!_ftueProgress.Conditions.IsTraumaFeatureEnabled())
            {
                return default;
            }
            
            ResultTraumaView view = await TryAddTraumasView();
            if (!view)
            {
                return default;
            }

            TabConfig tabConfig = new()
            {
                Header = _localization.Get(LocalIdents.Localization.TRAUMA_TITLE),
                HeaderColor = _localVisual.PositiveHeaderColor,
                RectTransform = view.GetComponent<RectTransform>(),
                TabController = this
            };
            return tabConfig;
        }

        public override void OnShow(CancellationToken cancellationToken)
        {
            _view.SetRegularButton();
        }

        private async UniTask<ResultTraumaView> TryAddTraumasView()
        {
            if (!TryGetData(out List<PlayerTimer> traumas))
            {
                return null;
            }

            foreach (PlayerTimer oldTrauma in _regionOldTraumasData.Get1(0).TraumasData)
            {
                for (int i = 0; i < traumas.Count; i++)
                {
                    PlayerTimer trauma = traumas[i];
                    if (oldTrauma.ItemId != trauma.ItemId)
                    {
                        continue;
                    }

                    float remainingOldTraumaSeconds = oldTrauma.EndTime - _serverTime.UtcNow.GetUtcTimeInSeconds();
                    float remainingTraumaSeconds = trauma.EndTime - _serverTime.UtcNow.GetUtcTimeInSeconds();
                    if (remainingOldTraumaSeconds >= remainingTraumaSeconds)
                    {
                        traumas.Remove(trauma);
                    }
                }
            }

            foreach (int idx in _regionOldTraumasData)
            {
                _regionOldTraumasData.GetEntity(idx).Destroy();
            }

            if (traumas.Count == 0)
            {
                return null;
            }
            
            
            ResourceConfig config = new()
            {
                Path = string.Format(LocalIdents.Paths.VIEWS_PATH, LocalIdents.Paths.RESULT_TRAUMAS_VIEW_PATH), 
                LoadFrom = LoadFromType.Ressources
            };
            ResultTraumaView asset = await _resourceLoadingService.Get(config).LoadAsync<ResultTraumaView>();
            if (asset == null)
            {
#if DEBUG
                Debug.Log("ResultTraumaView asset not found: " + string.Format(LocalIdents.Paths.VIEWS_PATH, LocalIdents.Paths.RESULT_TRAUMAS_VIEW_PATH));
#endif
                return null;
            }

            ResultTraumaView traumasView = Object.Instantiate(asset, _view.Container, false);
            traumasView.Init(_serverTime);

            RegionTraumasData data = new()
            {
                TraumasView = traumasView,
                Timers = traumas.ToArray()
            };
            
            _world.NewEntity().Get<RegionTraumasData>() = data;
            
            return traumasView;
        }

        public override bool TryGetData<T>(out List<T> data)
        {
            if (!_ftueProgress.Conditions.IsTraumaFeatureEnabled())
            {
                data = null;
                return false;
            }
            
            data = _playerManager.Session.Timers.GetTimers(
                PlayerTimers.Query.Get().WithTimerType(global::Common.TimerType.Trauma)).Cast<T>().ToList();
            
            return data.Count > 0;
        }

        public override bool HasData() => TryGetData(out List<PlayerTimer> _);
    }
}