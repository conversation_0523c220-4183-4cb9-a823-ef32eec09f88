using System.Collections.Generic;
using Client.Common.Audio;
using Client.Common.Audio.Banks;
using Client.Common.Components.Unit;
using Client.Common.HumanoidBody.Converters;
using Client.Common.LocalLogic.FPV;
using Client.Common.Network.MetaNet;
using Client.Common.NpcTab.Timer;
using Client.Common.Player.Controllers;
using Client.Common.Player.Timers;
using Client.Common.Traumas.Data;
using Client.FPV.Battle.Components;
using Client.Utils.ECS.EcsTaskTools;
using Client.Utils.ECS.LocalWorld;
using Client.Utils.Haptics;
using Client.Utils.ResultTool.Results;
using Cysharp.Threading.Tasks;
using Leopotam.Ecs;
using UnityEngine;

namespace Client.FPV.Battle.Systems.Wounds
{
    public struct AddTraumaRequest
    {
        public int Id;
        public EcsEntity Unit;

        public void Construct(int id, EcsEntity unit)
        {
            Id = id;
            Unit = unit;
        }
    }

    internal sealed class TraumasSystem : IEcsInitSystem, IEcsRunSystem
    {
        private readonly LocalEcsWorld _world = default;
        private readonly BattleAudio _battleAudio = default;
        private readonly PlayerManager _playerManager = default;
        private readonly MetaNet _metaNet = default;
        private readonly HapticWrapper _haptics = default;

        private readonly EcsFilter<Effect> _effects = default;
        private readonly EcsFilter<AddTraumaRequest> _newTraumas = default;
        private readonly EcsFilter<TimerEndEvent> _timerEnds = default;
        private readonly EcsFilter<FPVUnit, PlayerFlag> _playerUnits = default;
        private readonly EcsFilter<Statistics> _statistics = default;

        private BodyPartIdToBodyPartConverter _bodyPartIdToBodyPartConverter;

        public void Init()
        {
            _bodyPartIdToBodyPartConverter = new BodyPartIdToBodyPartConverter();
        }

        public void Run()
        {
            AddTraumas();
            RemoveTraumas();
        }

        private void AddTraumas()
        {
            foreach (int index in _newTraumas)
            {
                AddTraumaRequest request = _newTraumas.Get1(index);
                _newTraumas.GetEntity(index).Del<AddTraumaRequest>();
                CreateTrauma(request.Id, request.Unit).Forget();
            }
        }

        private void RemoveTraumas()
        {
            RemoveTraumasByTimerEnd();
        }

        private void RemoveTraumasByTimerEnd()
        {
            foreach (int index in _timerEnds)
            {
                TimerEndEvent timerEndEvent = _timerEnds.Get1(index);
                if (timerEndEvent.Type == global::Common.TimerType.Trauma && TryGetPlayerUnitEntity(out EcsEntity playerEntity))
                {
                    RemoveEffect(timerEndEvent.ItemId, playerEntity);
                }
            }
        }


        private async UniTaskVoid CreateTrauma(int effectId, EcsEntity unit)
        {
            bool isPlayer = unit.Has<PlayerFlag>();
            bool isNewEffect = !IsEffectActive(effectId, unit);
            bool hasMaxEffects = GetEffectCount(unit) >= TraumaIdents.MAX_TRAUMAS_PER_UNIT;

            if (isPlayer)
            {
                await UpdatePlayerTraumas(effectId, unit, hasMaxEffects, isNewEffect);
            }
            else
            {
                UpdateOpponentTraumas(effectId, unit, hasMaxEffects, isNewEffect);
            }

            if (isNewEffect)
            {
                CreateBattleEffect(effectId, unit);
            }
        }

        private async UniTask UpdatePlayerTraumas(int effectId, EcsEntity unit, bool hasMaxEffects, bool isNewEffect)
        {
            PlayTraumaSfx();
            await AddTraumaWithOldestReplace(effectId, unit, hasMaxEffects, isNewEffect);
        }

        private void UpdateOpponentTraumas(int effectId, EcsEntity unit, bool hasMaxEffects, bool isNewEffect)
        {
            if (hasMaxEffects && isNewEffect)
            {
                RemoveRandomTraumaExcept(effectId, unit);
            }
        }

        private async UniTask AddTraumaWithOldestReplace(int effectId, EcsEntity unit, bool hasMaxEffects, bool isNewEffect)
        {
            if (hasMaxEffects && isNewEffect)
            {
                PlayerTimer traumaToRemove = GetOldestUserTrauma();
                RemoveEffect(traumaToRemove.ItemId, unit);
                await TryRemoveTraumaTimer(traumaToRemove);
            }

            if (await TryAddTraumaTimer(effectId))
            {
                _statistics.GetEntity(0).Get<NewTraumas>().AddTrauma(effectId);
            }
        }

        private void RemoveRandomTraumaExcept(int effectId, EcsEntity unit)
        {
            if (_effects.IsEmpty())
            {
                return;
            }
            
            EcsEntity effectEntityToDisable = default;
            
            foreach (int index in _effects)
            {
                ref Effect effect = ref _effects.Get1(index);
                if (effect.Unit != unit)
                {
                    continue;
                }

                //cash first effect
                if (effectEntityToDisable == default)
                {
                    effectEntityToDisable = _effects.GetEntity(index);
                }
                
                if (effect.FromPreviousBattles && effect.EffectId != effectId)
                {
                    effectEntityToDisable = _effects.GetEntity(index);
                    break;
                }
            }
            
            effectEntityToDisable.Get<EffectDisable>();
        }

        private async UniTask<bool> TryRemoveTraumaTimer(PlayerTimer traumaToRemove)
        {
            Result<ChangeTimerResponse> changeTimerResponse = await _metaNet.ChangeTimer(TimerType.Trauma, traumaToRemove.ItemId, (int) -traumaToRemove.Duration * 1000);
            if (changeTimerResponse.IsFailure)
            {
                return false;
            }

            _playerManager.Session.Timers.ChangeTimer(changeTimerResponse.Value.Timer);
            return true;
        }

        private async UniTask<bool> TryAddTraumaTimer(int effectId)
        {
            BodyPart bodyPart = _bodyPartIdToBodyPartConverter.Convert(effectId);
            Result<ClientTraumaAddResponse> addTrauma = await _metaNet.AddTrauma(bodyPart);
            if (addTrauma.IsFailure)
            {
                return false;
            }

            _playerManager.Session.Timers.ChangeTimer(addTrauma.Value.Timer);
            return true;
        }

        private void CreateBattleEffect(int effectId, EcsEntity unit)
        {
            EcsEntity entity = _world.NewEntity();
            ref Effect effect = ref entity.Get<Effect>();
            effect.EffectId = effectId;
            effect.FromPreviousBattles = false;
            effect.Unit = unit;
            entity.Get<EffectEnable>();
            if (unit.Has<PlayerFlag>())
            {
                entity.Get<PlayerFlag>();
            }
        }

        private PlayerTimer GetOldestUserTrauma()
        {
            List<PlayerTimer> traumas = _playerManager.Session.Timers.GetTimers(PlayerTimers.Query.Get().WithTimerType(global::Common.TimerType.Trauma));
            PlayerTimer oldestTimer = new();
            long oldestStartTime = long.MaxValue;
            foreach (PlayerTimer playerTimer in traumas)
            {
                if (playerTimer.StartTime < oldestStartTime)
                {
                    oldestStartTime = playerTimer.StartTime;
                    oldestTimer = playerTimer;
                }
            }

            Debug.Log($"=> Oldest trauma {oldestTimer.ItemId}");
            return oldestTimer;
        }

        private void RemoveEffect(int traumaToRemove, EcsEntity unit)
        {
            foreach (int idx in _effects)
            {
                ref Effect effect = ref _effects.Get1(idx);
                if (effect.Unit == unit && effect.EffectId == traumaToRemove)
                {
                    _effects.GetEntity(idx).Get<EffectDisable>();
                    return;
                }
            }
        }

        private void PlayTraumaSfx()
        {
            _haptics.Play(HapticWrapper.Presets.Warning);
            _world.NewEntity().Get<SoundPoint>().SoundAssetPath = _battleAudio.PlayerTraumaNotification;
        }

        private bool IsEffectActive(int effectId, EcsEntity unit)
        {
            foreach (int idx2 in _effects)
            {
                ref Effect effect = ref _effects.Get1(idx2);
                if (effect.EffectId == effectId && effect.Unit == unit)
                {
                    return true;
                }
            }

            return false;
        }

        private int GetEffectCount(EcsEntity unit)
        {
            int effectCount = 0;
            foreach (int idx2 in _effects)
            {
                ref Effect effect = ref _effects.Get1(idx2);
                if (effect.Unit == unit)
                {
                    effectCount++;
                }
            }

            return effectCount;
        }

        private bool TryGetPlayerUnitEntity(out EcsEntity unitEntity)
        {
            foreach (int index in _playerUnits)
            {
                unitEntity = _playerUnits.GetEntity(index);
                return true;
            }

            unitEntity = default;
            return false;
        }
    }
}