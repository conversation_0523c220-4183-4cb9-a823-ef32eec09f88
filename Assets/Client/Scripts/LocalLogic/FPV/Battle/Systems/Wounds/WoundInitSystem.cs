using System.Collections.Generic;
using Client.Common.Components.Unit;
using Client.Common.CSV;
using Client.Common.LocalLogic.FPV;
using Client.Common.Network.MetaNet;
using Client.Common.Player.Controllers;
using Client.Common.Player.Timers;
using Client.FPV.Battle.Components;
using Client.Utils.ECS.LocalWorld;
using Leopotam.Ecs;

namespace Client.FPV.Battle.Systems.Wounds
{
    internal sealed class WoundInitSystem : IEcsRunSystem
    {
        // auto-injected fields.
        private readonly LocalEcsWorld _world = default;
        private readonly PlayerManager _playerManager = default;
        private readonly EcsFilter<FPVUnit, PlayerFlag> _player = default;
        private readonly EcsFilter<FPVUnit>.Exclude<PlayerFlag> _opponent = default;

        private bool _initialized;

        public void Run()
        {
            if (_initialized)
            {
                return;
            }

            foreach (int index in _player)
            {
                _initialized = true;
                PlayerTimers timers = _playerManager.Session.Timers;
                List<PlayerTimer> activeTimers = timers.GetTimers(PlayerTimers.Query.Get()
                                                                              .WithTimerType(global::Common.TimerType.Trauma));
                foreach (PlayerTimer timerData in activeTimers)
                {
                    CreateEffect(_player.GetEntity(index), timerData.ItemId);
                }
            }

            List<int> opponentTraumas = _playerManager.Session.BattleResult.EnemyTraumas;
            foreach (int traumaId in opponentTraumas)
            {
                if (traumaId != 0)
                {
                    foreach (int index in _opponent)
                    {
                        CreateEffect(_opponent.GetEntity(index), traumaId);
                    }
                }
            }
            
        }

        private void CreateEffect(EcsEntity unit, int traumaId)
        {
            EcsEntity effectEntity = _world.NewEntity();
            ref Effect effectComponent = ref effectEntity.Get<Effect>();
            effectComponent.EffectId = traumaId;
            effectComponent.FromPreviousBattles = true;
            effectComponent.Unit = unit;
            effectEntity.Get<EffectEnable>();
            if (effectComponent.Unit.Has<PlayerFlag>())
            {
                effectEntity.Get<PlayerFlag>();
            }
        }
    }
}