using System.Collections.Generic;
using System.Linq;
using Client.Common.Analytics.Helpers;
using Client.Common.Analytics.Infrastructure.Events;
using Client.Common.Analytics.Trackers.Abstract;
using Client.Common.Components.Unit;
using Client.Common.HumanoidBody.Data;
using Client.Common.LocalLogic.FPV;
using Client.Common.Network.MetaNet;
using Client.Common.Player.Controllers;
using Client.Common.Player.Timers;
using Client.Common.SceneLoading.SceneData.Implementations;
using Client.FPV.Battle.Components;
using Client.FPV.Battle.QTE.Components;
using Client.FPV.Battle.QTE.Result;
using Client.FPV.Battle.Systems.ConditionSelection.Components;
using Client.Utils.ECS.LocalWorld;
using Common;
using Leopotam.Ecs;
using TimerType = Client.Common.Network.MetaNet.TimerType;

namespace Client.FPV.Battle.Systems.Analytics
{
    internal class BattleCommonResultAnalyticsTracker : IAnalyticTracker
    {
        private struct AnalyticsBattleResultApplied
        {
        }

        private readonly LocalEcsWorld _world;
        private readonly EcsFilter<BattleResultComponent> _battleResults;
        private readonly EcsFilter<Statistics> _stats;
        private readonly EcsFilter<FPVUnit, PlayerFlag> _player;
        private readonly EcsFilter<FPVUnit>.Exclude<PlayerFlag> _opponent;
        private readonly BattleFeatureConfig _featureConfig;
        private readonly EcsFilter<ConditionId> _condition;
        private readonly EcsFilter<PowerStonesUsageData> _powerStoneData;

        private readonly EcsFilter<AnalyticsBattleResultApplied> _appliedFilter;
        
        private readonly IAnalyticsHelper _analyticsHelper;

        private readonly AnalyticsEntriesCounterHelper _analyticsEntriesCounterHelper;
        private readonly PlayerManager _playerManager;

        public BattleCommonResultAnalyticsTracker(LocalEcsWorld world, BattleFeatureConfig featureConfig, PlayerManager playerManager, IAnalyticsHelper analyticsHelper)
        {
            _world = world;

            _appliedFilter = _world.GetFilter<EcsFilter<AnalyticsBattleResultApplied>>();
            _battleResults = _world.GetFilter<EcsFilter<BattleResultComponent>>();
            _stats = _world.GetFilter<EcsFilter<Statistics>>();
            _player = _world.GetFilter<EcsFilter<FPVUnit, PlayerFlag>>();
            _opponent = _world.GetFilter<EcsFilter<FPVUnit>.Exclude<PlayerFlag>>();
            _condition = _world.GetFilter<EcsFilter<ConditionId>>();
            _powerStoneData = _world.GetFilter<EcsFilter<PowerStonesUsageData>>();
            _featureConfig = featureConfig;

            _analyticsEntriesCounterHelper = new AnalyticsEntriesCounterHelper();
            _playerManager = playerManager;
            _analyticsHelper = analyticsHelper;
        }

        public void Track()
        {
            if (!_appliedFilter.IsEmpty())
            {
                return;
            }

            _world.NewEntity().Get<AnalyticsBattleResultApplied>();

            FireEvent();
        }

        private void FireEvent()
        {
            EcsEntity stats = _stats.GetEntity(0);
            FPVUnit player = _player.Get1(0);
            EcsEntity opponentEntity = _opponent.GetEntity(0);
            ShieldStats shieldStats = _player.GetEntity(0).Get<ShieldStats>();
            PlayerStamina playerStamina = stats.Get<PlayerStamina>();

            uint powerStoneUsed = _powerStoneData.Get1(0).StartCount - _playerManager.Session.Progress.Pockets[PocketType.PowerStone].Count;

            List<int> playerTraumas = _playerManager.Session.Timers.GetTimers(
                PlayerTimers.Query.Get()
                            .WithTimerType(global::Common.TimerType.Trauma)).Select(k => k.ItemId).ToList();
            
            EcsEntity analyticsEvent = _world.NewEntity();
            analyticsEvent.Get<AnalyticsCustomEvent>().Id = "battle_result";
            ref AnalyticsParameters analyticsCustomEvent = ref analyticsEvent.Get<AnalyticsParameters>();

            analyticsCustomEvent.Parameters = new Dictionary<string, string>()
            {
                ["Result"] = _battleResults.Get1(0).Result.ToString(),
                ["HP lost"] = $"{GetPercent((float)player.Health / player.MaxHealth)}",
                ["Shield lost"] = $"{GetPercent(shieldStats.UsedAmount / shieldStats.StartValue)}",
                ["Stamina lost"] = $"{GetPercent(playerStamina.UsedAmount / playerStamina.StartValue)}",
                ["Moved"] = $"{stats.Has<PlayerHasMoved>()}",
                ["Shield used"] = $"{shieldStats.UsedAmount > 0}",
                ["Skills used"] = $"{stats.Has<PlayerUsedSkills>()}",
                ["Traumas"] = $"{_analyticsHelper.GetJoinedIds(playerTraumas)}",
                ["Bot Type"] = $"{_playerManager.Session.BattleResult.EnemyType}",
                ["StoneType"] = $"{_playerManager.Session.Progress.Pockets[PocketType.PowerStone].Id}",
                ["StoneUsed"] = $"{powerStoneUsed}",
                ["Damage Apply"] = $"{player.DamageApply}",
                ["Damage Receive"] = $"{player.DamageReceive}",
                ["Condition"] = $"{_condition.Get1(0).Id}",
                ["Throws"] = $"{_playerManager.Session.BattleResult.PlayerShurikensThrownCount}",
                ["Throws_success"] = $"{opponentEntity.Get<UnitTakenHitsCount>().TotalHits()}",
                ["No_stamina"] = $"{playerStamina.NoStamina}",
                ["Rank"] = _playerManager.Session.Progress.Rank.ToString(),
                ["Region Entries"] = $"{_analyticsEntriesCounterHelper.GetRegionAnalyticsCounter()}",
                ["Battle Entries"] = $"{_analyticsEntriesCounterHelper.GetBattleAnalyticsCounter()}",
            };

            if (_featureConfig.Qte)
            {
                QteStatisticsEvent qteStatisticsEvent = stats.Get<QteStatisticsEvent>();

                if (qteStatisticsEvent.QteEndResults.Count <= 0)
                {
                    return;
                }

                EcsEntity analyticsQteEvent = _world.NewEntity();
                analyticsQteEvent.Get<AnalyticsCustomEvent>().Id = "battle_result_qte";
                ref AnalyticsParameters analyticsQteCustomEvent = ref analyticsQteEvent.Get<AnalyticsParameters>();
                analyticsQteCustomEvent.Parameters = new Dictionary<string, string>();
                analyticsQteCustomEvent.Parameters.Add("Qte started", $"{qteStatisticsEvent.QteStartCount}");
                analyticsQteCustomEvent.Parameters.Add("Qte ended", $"{qteStatisticsEvent.QteEndCount}");
                analyticsQteCustomEvent.Parameters.Add("Qte results", GetQteResults(qteStatisticsEvent.QteEndResults));
            }
        }

        private int GetPercent(float ratio)
        {
            return (int)(ratio * 100);
        }

        private string GetQteResults(List<QteResult> qteResults)
        {
            string result = "";

            foreach (QteResult qteResult in qteResults)
            {
                result += $"{qteResult} | ";
            }

            return result;
        }
    }
}