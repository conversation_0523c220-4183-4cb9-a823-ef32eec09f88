using System.Collections.Generic;
using System.Linq;
using Client.Common.Analytics.Helpers;
using Client.Common.Analytics.Infrastructure.Events;
using Client.Common.Analytics.Trackers.Abstract;
using Client.Common.Components.Unit;
using Client.Common.LocalLogic.FPV;
using Client.Common.Network.MetaNet;
using Client.Common.Player.Controllers;
using Client.Common.Player.Timers;
using Client.FPV.Battle.Components;
using Client.FPV.Battle.Components.AI;
using Client.Utils.ECS.LocalWorld;
using Common;
using Leopotam.Ecs;
using TimerType = Client.Common.Network.MetaNet.TimerType;

namespace Client.FPV.Battle.Systems.Analytics
{
    public class BattleStartAnalyticsTracker : IAnalyticTracker
    {
        private readonly LocalEcsWorld _world;

        private readonly EcsFilter<FPVUnit, ShurikenData, AmuletData, OutfitData, ShurikenSkinData>.Exclude<PlayerFlag> _opponents;

        private readonly AnalyticsEntriesCounterHelper _analyticsEntriesCounterHelper;
        private readonly PlayerManager _playerManager;
        private readonly IAnalyticsHelper _analyticsHelper;

        private bool _opponentsTracked;

        public BattleStartAnalyticsTracker(LocalEcsWorld world, PlayerManager playerManager, IAnalyticsHelper analyticsHelper)
        {
            _world = world;
            _opponents = _world.GetFilter<EcsFilter<FPVUnit, ShurikenData, AmuletData, OutfitData, ShurikenSkinData>.Exclude<PlayerFlag>>();
            _analyticsEntriesCounterHelper = new AnalyticsEntriesCounterHelper();
            _playerManager = playerManager;
            _analyticsHelper = analyticsHelper;
        }

        public void Track()
        {
            if (!_opponentsTracked)
            {
                TrackAllOpponents();
                _opponentsTracked = true;
            }
        }

        private void TrackAllOpponents()
        {
            foreach (int index in _opponents)
            {
                EcsEntity unitEntity = _opponents.GetEntity(index);
                ref FPVUnit unit = ref _opponents.Get1(index);
                ref ShurikenData shurikenData = ref _opponents.Get2(index);
                ref AmuletData amuletData = ref _opponents.Get3(index);
                ref OutfitData outfitData = ref _opponents.Get4(index);
                ref ShurikenSkinData shurikenSkinData = ref _opponents.Get5(index);

                int aiTypeId = -1;

                if (unitEntity.Has<BattleAITypeComponent>())
                {
                    aiTypeId = (int)unitEntity.Get<BattleAITypeComponent>().AIType;
                }

                FireEvent("battle_start", aiTypeId, unit.Rank, shurikenData.Id, amuletData.Id, outfitData.Id, shurikenSkinData.Id);
            }
        }

        private void FireEvent(string id, int type, int rank, int shurikenId, int amuletId, int outfitId, int skinId)
        {
            _analyticsEntriesCounterHelper.SetBattleAnalyticsCounter();
            _world.NewEntity().Get<PowerStonesUsageData>().StartCount = _playerManager.Session.Progress.Pockets[PocketType.PowerStone].Count;

            List<int> playerTraumas = _playerManager.Session.Timers.GetTimers(
                            PlayerTimers.Query.Get()
                            .WithTimerType(global::Common.TimerType.Trauma)).Select(k => k.ItemId).ToList();
            EcsEntity analyticsEvent = _world.NewEntity();
            analyticsEvent.Get<AnalyticsCustomEvent>().Id = id;

            analyticsEvent.Get<AnalyticsParameters>().Parameters = new Dictionary<string, string>
            {
                ["Type"] = $"{type}",
                ["Rank"] = $"{rank}",
                ["Shuriken"] = $"{shurikenId}",
                ["Amulet"] = $"{amuletId}",
                ["Outfit"] = $"{outfitId}",
                ["Skin"] = $"{skinId}",
                ["StonePockets"] = $"{_playerManager.Session.Progress.Pockets.PowerStone.Count}",
                ["StoneType"] = $"{_playerManager.Session.Progress.Pockets[PocketType.PowerStone].Id}",
                ["StoneCount"] = $"{_playerManager.Session.Progress.Pockets[PocketType.PowerStone].Count}",
                ["HookSide"] = $"{(_playerManager.Session.BattleResult.EnemyWasHooked ? "playerHookOpponent" : "opponentHookPlayer")}",
                ["Harpoons"] =
                    $"{(_playerManager.Session.BattleResult.EnemyWasHooked ? _playerManager.Session.Progress.Pockets[PocketType.Hook].Id : _playerManager.Session.BattleResult.EnemyHook)}",
                ["TraumasPlayer"] = $"{_analyticsHelper.GetJoinedIds(playerTraumas)}",
                ["TraumasBot"] = $"{_analyticsHelper.GetJoinedIds(_playerManager.Session.BattleResult.EnemyTraumas)}",
                ["Region Entries"] = $"{_analyticsEntriesCounterHelper.GetRegionAnalyticsCounter()}",
                ["Battle Entries"] = $"{_analyticsEntriesCounterHelper.GetBattleAnalyticsCounter()}"
            };
        }
    }
}