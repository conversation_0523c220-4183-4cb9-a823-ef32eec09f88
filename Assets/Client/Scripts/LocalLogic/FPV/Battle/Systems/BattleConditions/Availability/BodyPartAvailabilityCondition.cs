using Client.Common.CSV;
using Client.Common.Player.Controllers;
using Client.Utils.Conditions;

namespace Client.FPV.Battle.Systems.BattleConditions.Availability
{
    public class BodyPartAvailabilityCondition: ICondition
    {
        private const string _BODY_PARTS_CONDITION_LIMIT_ = "bodyparts.condition.limit";
        
        private readonly PlayerProgress _playerProgress;
        private readonly int _bodyPartsConditionLimit;

        public BodyPartAvailabilityCondition(PlayerProgress playerProgress, GoogleDocsData googleDocsData)
        {
            _playerProgress = playerProgress;
            _bodyPartsConditionLimit = (int)googleDocsData.GetNumber(_BODY_PARTS_CONDITION_LIMIT_);
        }


        public bool IsMet()
        {
            return _playerProgress.TotalBattles() > _bodyPartsConditionLimit;
        }
    }
}