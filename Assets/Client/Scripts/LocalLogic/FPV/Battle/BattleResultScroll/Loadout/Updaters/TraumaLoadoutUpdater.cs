using System.Collections.Generic;
using System.Linq;
using Client.Common.Loadout.Items;
using Client.Common.Loadout.Services;
using Client.Common.Network.MetaNet;
using Client.Common.Player.Controllers;
using Client.Common.Player.Timers;
using Client.FPV.Battle.Components;
using Client.Utils.ECS.LocalWorld;
using Leopotam.Ecs;

namespace Client.FPV.Battle.BattleResultScroll.Loadout.Updaters
{
    public class TraumaLoadoutUpdater : BaseLoadoutUpdater
    {
        private readonly PlayerManager _playerManager;
        private readonly LoadoutUiService _loadoutUiService;
        private readonly LoadoutBattleResultSave _loadoutBattleResultSave;
        
        private readonly EcsFilter<NewTraumas> _newTraumas;
        private bool _playerGotTrauma;

        public TraumaLoadoutUpdater(
            PlayerManager playerManager, 
            LoadoutUiService loadoutUiService, 
            LoadoutBattleResultSave loadoutBattleResultSave, 
            LocalEcsWorld world)
        {
            _playerManager = playerManager;
            _loadoutUiService = loadoutUiService;
            _loadoutBattleResultSave = loadoutBattleResultSave;
            _newTraumas = world.GetFilter<EcsFilter<NewTraumas>>();
        }

        protected override bool IsItemCritical()
        {
            _playerGotTrauma = PlayerHasTraumas() && !PlayerHadTraumasBeforeBattle();

            return _playerGotTrauma;
        }

        protected override bool CriticalUpdateWasShownBefore()
        {
            return _loadoutBattleResultSave.LastShownHadTrauma == _playerGotTrauma;
        }

        protected override void UpdateCritical()
        {
            _loadoutBattleResultSave.LastShownHadTrauma = _playerGotTrauma;
            _loadoutUiService.UpdateItem(LoadoutItemId.Trauma);
        }

        protected override void ResetCritical()
        {
            _loadoutBattleResultSave.LastShownHadTrauma = null;
        }

        private bool PlayerHasTraumas()
        {
            return _newTraumas.GetEntitiesCount() > 0;
        }

        private bool PlayerHadTraumasBeforeBattle()
        {
            PlayerTimers.Query traumasQuery =
                PlayerTimers.Query
                            .Get()
                            .WithTimerType(global::Common.TimerType.Trauma);

            List<PlayerTimer> allTraumas = _playerManager.Session.Timers.GetTimers(traumasQuery);

            return allTraumas.Any(IsOldTrauma);

            bool IsOldTrauma(PlayerTimer traumaTimer)
            {
                return !_newTraumas.Get1(0).Traumas.Contains(traumaTimer.ItemId);
            }
        }
    }
}