using Client.Common.Network.MetaNet;
using Client.Common.Player.Controllers;
using Client.Common.Player.Timers;
using Client.Common.Quests.Goals.Systems;
using Client.Common.Quests.Goals.Types;
using Client.FPV.Battle.Components;
using Leopotam.Ecs;

namespace Client.FPV.Battle.Quests.Goals
{
    internal class WinWithTraumaQuestProgressSystem : QuestProgressSystem<WinWithTraumaGoal>
    {
        private struct WinWithTraumaQuestApplied
        {
        }

        private readonly PlayerManager _playerManager = default;

        private readonly EcsFilter<BattleResultComponent> _battleResult = default;
        private readonly EcsFilter<WinWithTraumaQuestApplied> _appliedFilter = default;

        protected override void OnUpdate()
        {
            if (_battleResult.IsEmpty())
            {
                return;
            }

            if (_appliedFilter.GetEntitiesCount() > 0)
            {
                return;
            }

            if (_playerManager.Session.RegionId <= 0)
            {
                return;
            }

            Common.Network.MetaNet.BattleResult result = _playerManager.Session.RegionResultData.BattleResult;

            if (HaveTraumas() && result == Common.Network.MetaNet.BattleResult.Win)
            {
                foreach (int goalIndex in ActiveGoals)
                {
                    Progress.Add(ActiveGoals.GetEntity(goalIndex), 1);
                }

                _battleResult.GetEntity(0).Get<WinWithTraumaQuestApplied>();
            }
        }

        private bool HaveTraumas()
        {
            PlayerTimers.Query query = PlayerTimers.Query.Get().WithTimerType(global::Common.TimerType.Trauma);

            return _playerManager.Session.Timers.GetTimers(query).Count > 0;
        }
    }
}