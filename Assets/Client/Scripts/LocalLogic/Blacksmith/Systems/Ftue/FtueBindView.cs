using Client.Common.UI.Buttons;
using Client.Common.UI.Buttons.Texted;
using Client.Utils.StateSwitcher;
using Client.Utils.ViewService;
using UnityEngine;

namespace Client.Blacksmith.Systems.Ftue
{
    public class FtueBindView : View
    {
        [SerializeField] private TheTextedButton _signInGoogle;
        [SerializeField] private TheTextedButton _signInFacebook;

        [SerializeField] private ActionButton _close;

        public ActionButton CloseButton => _close;

        public TheTextedButton Google => _signInGoogle;
        public TheTextedButton Facebook => _signInFacebook;

        public void SetButtonsInteractable(TheActiveState state)
        {
            // _signInGoogle.SetState(state);
            _signInFacebook.SetState(state);
        }
    }
}