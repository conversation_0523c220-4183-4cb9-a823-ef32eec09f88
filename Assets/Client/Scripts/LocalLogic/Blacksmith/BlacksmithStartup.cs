using System.Threading;
using System.Threading.Tasks;
using Client.Blacksmith.Data;
using Client.Blacksmith.Systems;
using Client.Blacksmith.Systems.Analytics;
using Client.Blacksmith.Systems.Ftue.PowerStones;
using Client.Blacksmith.Systems.Generators;
using Client.Blacksmith.Systems.PopupStates.PowerStones;
using Client.Blacksmith.Systems.PopupStates.Shurikens;
using Client.Blacksmith.Systems.PopupStates.Skins;
using Client.Blacksmith.Systems.Tabs;
using Client.Blacksmith.UnityComponents;
using Client.Common.Analytics.Helpers;
using Client.Common.AsyncLoadHelper;
using Client.Common.Components.Equip;
using Client.Common.CraftReadyItems;
using Client.Common.Currency;
using Client.Common.Dialogs.Runtime.Systems;
using Client.Common.ECS.LocalWorld;
using Client.Common.Ftue.Infrastructure;
using Client.Common.HintSystem;
using Client.Common.Items;
using Client.Common.Network.MetaNet;
using Client.Common.Npc;
using Client.Common.NpcTab.Data;
using Client.Common.NpcTab.Data.TabTypes;
using Client.Common.NpcTab.Data.UI;
using Client.Common.NpcTab.ItemCounts;
using Client.Common.NpcTab.Pieces;
using Client.Common.NpcTab.Systems.Craft;
using Client.Common.NpcTab.Systems.Tabs;
using Client.Common.NpcTab.Systems.UpdateSystems;
using Client.Common.Player.Controllers;
using Client.Common.Player.ProgressItems;
using Client.Common.Quests.Goals.Systems;
using Client.Common.ResourcesTopPanel;
using Client.Common.ResourcesTopPanel.Configs;
using Client.Common.SceneLoading;
using Client.Common.ScreenTransitions;
using Client.Common.UI.BackButton;
using Client.Common.UI.ScrollController.Controllers;
using Client.Utils.Extensions.EcsExtensions.SystemExtensions;
using Client.Utils.ResourceLoading;
using UnityEngine;
using VContainer;

namespace Client.Blacksmith
{
    internal struct BlacksmithScope
    {
    }

    internal sealed class BlacksmithStartup : LocalWorld<BlacksmithScope>
    {
        [SerializeField] private BlacksmithLifeTimeScope _lifeTimeScope;
        [SerializeField] private BlacksmithTabView _view;
        [SerializeField] private BlueprintColorsConfig _blueprintColorsConfig;
        [SerializeField] private TabAnchorsBundle _anchorsBundle;
        [SerializeField] private HintAnchors _hintAnchors;
        [SerializeField] private ShurikenTabBundle _shurikenTabBundle;
        [SerializeField] private ShurikenSkinTabBundle _shurikenSkinTabBundle;
        [SerializeField] private PowerStonesTabBundle _powerStonesTabBundle;

        [Inject] private PlayerManager _playerManager;
        [Inject] private ItemData _itemData;
        [Inject] private MetaNet _metaNet;
        [Inject] private ResourceLoadingService _resourceLoadingService;
        [Inject] private ProgressItemsCache _progressItemsCache;
        [Inject] private FtueProgress _ftue;
        [Inject] private LocalAnalyticsHelper _analyticsHelper;
        [Inject] private TransitionService _transitionService;
        [Inject] private SceneLoader _sceneLoader;
        
        protected override async Task OnStart(CancellationToken cancellationToken)
        {
            ScrollGenerator scrollGenerator = await new ScrollGenerator().InitAsync(_resourceLoadingService, cancellationToken);
            
            Systems
                .RegisterLockController(null)
                .Add(new CraftReadyItemsUpdateSystem())
                .Add(new BlacksmithViewInitSystem(_view))
                
                .AddFeature(new ShurikenInitFeature(_view, _shurikenTabBundle, _progressItemsCache));

            if (_ftue.Conditions.IsFirstShurikenSkinUnlocked)
            {
                Systems
                    .AddFeature(new SkinInitFeature(_playerManager, _shurikenSkinTabBundle, _progressItemsCache))
                    ;
            }
            if (_ftue.Conditions.IsFirstPowerStoneUnlocked)
            {
                Systems
                    .AddFeature(new PowerStonesInitFeature( _powerStonesTabBundle, _progressItemsCache, _playerManager, _anchorsBundle))
                    ;
            }
            
            Systems
                .Add(new VisitNpcQuestProgressSystem(NpcType.Blacksmith))
                .Add(new CraftComponentUpdateSystem())
                
                .AddFeature(new DialogFeature(World, _analyticsHelper))
                
                .AddFeature(new BlacksmithNextRegionItemFeature(_playerManager, _anchorsBundle))
                .AddFeature(new TabStateMachine(World, scrollGenerator, _shurikenSkinTabBundle, _shurikenTabBundle, _powerStonesTabBundle))
                .AddFeature(new ShurikenStateMachine(_shurikenTabBundle))
                .AddFeature(new SkinStateMachine(_shurikenSkinTabBundle, _shurikenSkinTabBundle.DurabilityLabelView))
                .AddFeature(new PowerStonesStateMachine(_powerStonesTabBundle))
                .AddFeature(new PieceUiFeature())
                .AddFeature(new ItemCountUiFeature())
                
                .Add(new CraftSystem())
                .Add(new CraftForceCompleteSystem())
                .AddFeature(new ResourcesTopPanelFeature(ResourcesTopPanelConfig.WithPurchase))
                .Add(new FtuePowerStonesBlacksmith())
                .Add(new BackButtonWithGlintSystem(_view.CloseButton))
                // should be last.
                .Add(new BlacksmithCraftAnalyticsTrackSystem())
                .Add(new TabOpenSystem<BlacksmithTabType>())
                .Add(new AudioSystem())
                .Add(new FadeOutSystem())
                .OneFrame<EquippedEvent>()
                .OneFrame<UnequippedEvent>()
                .InjectFromContainer(_lifeTimeScope.Container)
                ;
        }
    }
}