using System.Collections.Generic;
using Client.Common.LocalLogic.ChangeData;
using Client.Common.Network.MetaNet;
using Client.Common.Player.Controllers;
using Client.Common.Player.Timers;
using Client.Utils.ECS.LocalWorld;
using Leopotam.Ecs;
using TimerType = Common.TimerType;

namespace Client.Map.Systems
{
    sealed class MapOldTraumasSystem : IEcsInitSystem
    {
        private readonly PlayerManager _playerManager = default;
        private readonly SessionScopeManager _persistentData = default;
        private readonly EcsFilter<RegionOldTraumasData> _dataFilter = default;

        public void Init()
        {
            ClearUnusedData();
            CacheExistingTraumas();
        }

        private void ClearUnusedData()
        {
            foreach (int index in _dataFilter)
            {
                _dataFilter.GetEntity(index).Destroy();
            }
        }

        private void CacheExistingTraumas()
        {
            List<PlayerTimer> traumas = _playerManager.Session.Timers.GetTimers(
                PlayerTimers.Query.Get()
                            .WithTimerType(TimerType.Trauma));
            _persistentData.NewEntity<RegionOldTraumasData>().Get<RegionOldTraumasData>().TraumasData = traumas;
        }
    }
}