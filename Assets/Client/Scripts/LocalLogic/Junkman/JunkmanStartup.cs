using System.Threading;
using System.Threading.Tasks;
using Client.Common.Abilities.Infrastructure.Data;
using Client.Common.Abilities.Meta.Systems;
using Client.Common.Analytics.Helpers;
using Client.Common.AsyncLoadHelper;
using Client.Common.Components.Equip;
using Client.Common.CraftReadyItems;
using Client.Common.Currency;
using Client.Common.Dialogs.Runtime.Systems;
using Client.Common.ECS.LocalWorld;
using Client.Common.Ftue.Helpers;
using Client.Common.Ftue.Infrastructure;
using Client.Common.HintSystem;
using Client.Common.Items;
using Client.Common.Network.MetaNet;
using Client.Common.Npc;
using Client.Common.NpcTab.Controllers;
using Client.Common.NpcTab.Controllers.InitalTabProvider;
using Client.Common.NpcTab.Data;
using Client.Common.NpcTab.Data.TabTypes;
using Client.Common.NpcTab.Data.UI;
using Client.Common.NpcTab.ItemCounts;
using Client.Common.NpcTab.Pieces;
using Client.Common.NpcTab.Systems.Craft;
using Client.Common.NpcTab.Systems.Tabs;
using Client.Common.NpcTab.Systems.UpdateSystems;
using Client.Common.Player.Controllers;
using Client.Common.Quests.Goals.Systems;
using Client.Common.ResourcesTopPanel;
using Client.Common.ResourcesTopPanel.Configs;
using Client.Common.ScreenTransitions;
using Client.Common.UI.BackButton;
using Client.Common.UI.ScrollController.Controllers;
using Client.Junkman.Data;
using Client.Junkman.Systems;
using Client.Junkman.Systems.Analytics;
using Client.Junkman.Systems.Ftue.Amulets;
using Client.Junkman.Systems.Ftue.Bombs;
using Client.Junkman.Systems.Ftue.Hooks;
using Client.Junkman.Systems.Ftue.Outfits;
using Client.Junkman.Systems.Generators;
using Client.Junkman.Systems.PopupStates.Amulets;
using Client.Junkman.Systems.PopupStates.Bombs;
using Client.Junkman.Systems.PopupStates.Hooks;
using Client.Junkman.Systems.PopupStates.Outfits;
using Client.Junkman.Systems.Tabs;
using Client.Junkman.UnityComponents;
using Client.Utils.AsyncLoadHelper;
using Client.Utils.CustomTweens.VFXService.Abstractions;
using Client.Utils.Extensions.EcsExtensions.SystemExtensions;
using Client.Utils.ResourceLoading;
using Cysharp.Threading.Tasks;
using UnityEngine;
using VContainer;

namespace Client.Junkman
{
    internal struct JunkmanScope
    {
    }
    
    internal class JunkmanStartup : LocalWorld<JunkmanScope>
    {
        [SerializeField] private JunkmanLifeTimeScope _lifeTimeScope;
        [SerializeField] private JunkmanTabView _view;
        [SerializeField] private TabAnchorsBundle _anchorsBundle;
        [SerializeField] private AmuletTabBundle _amuletTabBundle;
        [SerializeField] private OutfitTabBundle _outfitTabBundle;
        [SerializeField] private BombTabBundle _bombTabBundle;
        [SerializeField] private HookTabBundle _hookTabBundle;

        [Inject] private ITweenManager _tweenManager;
        [Inject] private MetaNet _metaNet;
        [Inject] private PlayerManager _playerManager;
        [Inject] private ItemData _itemData;
        [Inject] private ResourceLoadingService _resourceLoadingService;
        [Inject] private LockController _lockController;
        [Inject] private EcsTabLoadController<JunkmanTabType> _tabLoadController;
        [Inject] private FtueProgress _ftue;
        [Inject] private LocalAnalyticsHelper _analyticsHelper;
        [Inject] private AbilityByItemProvider _abilityByItemProvider;
        [Inject] private IInitialTabProvider<JunkmanTabType> _initialTabProvider;

        protected override async Task OnStart(CancellationToken cancellationToken)
        {
            cancellationToken.Register(_tweenManager.InstantCompleteAndDispose);

            ScrollGenerator scrollGenerator = await new ScrollGenerator().InitAsync(_resourceLoadingService, cancellationToken);

            Systems
                .RegisterLockController(_lockController)
                .Add(new CraftReadyItemsUpdateSystem())
                .AddFeature(new JunkmanInitFeature(_tabLoadController));

            if (_ftue.Conditions.IsFirstOutfitUnlocked)
            {
                Systems
                    .AddFeature(new OutfitFtueFeature(_view, _outfitTabBundle, _anchorsBundle, _tabLoadController))
                    .AddFeature(new OutfitsRuntimeFeature(_outfitTabBundle));
            }

            Systems
                .Add(new VisitNpcQuestProgressSystem(NpcType.Junkman))
                .Add(new CraftComponentUpdateSystem())
                .Add(new FtuePart1System())
                .AddFeature(new DialogFeature(World, _analyticsHelper))
                .AddFeature(new TabStateMachine(World, scrollGenerator, _amuletTabBundle, _outfitTabBundle, _bombTabBundle, _hookTabBundle))
                .AddFeature(new AmuletStateMachine(_amuletTabBundle))
                .AddFeature(new OutfitStateMachine(_outfitTabBundle, _outfitTabBundle.DurabilityLabelView))
                .AddFeature(new BombStateMachine(_bombTabBundle))
                .AddFeature(new HookStateMachine(_hookTabBundle))
                .AddFeature(new JunkmanNextRegionItemFeature(_playerManager, _anchorsBundle))
                .AddFeature(new PieceUiFeature())
                .AddFeature(new ItemCountUiFeature())
                .Add(new CraftSystem())
                .Add(new CraftForceCompleteSystem())
                .AddFeature(new ResourcesTopPanelFeature(ResourcesTopPanelConfig.WithPurchase))
                .Add(new FtueHooksJunkman())
                .Add(new FtueBombsJunkman())
                .Add(new BackButtonWithGlintSystem(_view.CloseButton))

                // abilities
                .Add(new ItemEquipAbilityCreateSystem())
                .Inject(_abilityByItemProvider)

                // should be last.
                .Add(new JunkmanAnalyticsTrackSystem())
                .Add(new AudioSystem())
                .Add(new TabOpenSystem<JunkmanTabType>(_tabLoadController, _initialTabProvider))
                .Add(new FadeOutSystem())
                .OneFrame<EquippedEvent>()
                .OneFrame<UnequippedEvent>()
                .InjectFromContainer(_lifeTimeScope.Container)
                ;
        }
    }
}