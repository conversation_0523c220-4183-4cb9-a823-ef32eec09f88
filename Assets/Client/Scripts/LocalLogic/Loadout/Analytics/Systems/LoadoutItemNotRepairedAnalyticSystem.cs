using System.Collections.Generic;
using Client.Common.Analytics.Infrastructure.Events;
using Client.Common.Configs;
using Client.Common.Configs.Components;
using Client.Common.Configs.Components.Items;
using Client.Common.Configs.Components.Items.TypeMarkers;
using Client.Common.Network.MetaNet;
using Client.Common.Player.Controllers;
using Client.Common.UI.Buttons;
using Client.Utils.ECS.LocalWorld;
using Leopotam.Ecs;
using SkinDurability = Common.SkinDurability;
using WeaponSkin = Common.WeaponSkin;

namespace Client.Scripts.Loadout.Analytics.Systems
{
    public class LoadoutItemNotRepairedAnalyticSystem : IEcsInitSystem, IEcsDestroySystem
    {
        private readonly LocalEcsWorld _world;
        private readonly PlayerManager _playerManager;
        private readonly ConfigService _configService;

        private EcsFilter<ConfigComponent, ItemIdData, ShurikenItemMarker> _shurikenConfigs;

        private readonly ActionButton _nextButton;

        public LoadoutItemNotRepairedAnalyticSystem(ActionButton nextButton)
        {
            _nextButton = nextButton;
        }

        public void Init()
        {
            _shurikenConfigs = _configService.GetFilter<EcsFilter<ConfigComponent, ItemIdData, ShurikenItemMarker>>();

            _nextButton.Clicked += Track;
        }

        public void Destroy()
        {
            _nextButton.Clicked -= Track;
        }

        private void Track()
        {
            TrackOutfits();
            TrackShurikenSkins();
        }

        private void TrackOutfits()
        {
            foreach (SkinDurability outfit in _playerManager.Session.Progress.OutfitDurabilities)
            {
                if (outfit.Durability <= 0)
                {
                    SendEvent((int)outfit.Id);
                }
            }
        }

        private void TrackShurikenSkins()
        {
            foreach (int i in _shurikenConfigs)
            {
                int itemId = _shurikenConfigs.Get2(i).Id;
                if (_playerManager.Session.Progress.BrokenWeaponSkinStorage.TryGetSkinData(itemId, out WeaponSkin _))
                {
                    SendEvent(itemId);
                }
            }
        }

        private void SendEvent(int itemId)
        {
            EcsEntity analyticsEvent = _world.NewEntity();
            analyticsEvent.Get<AnalyticsCustomEvent>().Id = "item_not_repaired";

            analyticsEvent.Get<AnalyticsParameters>().Parameters = new Dictionary<string, string>
            {
                ["ItemId"] = itemId.ToString(),
            };
        }
    }
}