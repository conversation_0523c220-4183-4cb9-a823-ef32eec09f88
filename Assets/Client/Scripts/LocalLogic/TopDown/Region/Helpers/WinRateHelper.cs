using Client.Common.CSV;
using Client.Common.Player.Controllers;
using Client.TopDown.Region.Data;
using UnityEngine;

namespace Client.TopDown.Region.Helpers
{
    public class WinRateHelper
    {
        private readonly PlayerProgress _playerProgress;
        private readonly GoogleDocsData _googleDocsData;
        
        public WinRateHelper(PlayerProgress playerProgress, GoogleDocsData googleDocsData)
        {
            _playerProgress = playerProgress;
            _googleDocsData = googleDocsData;
        }
        
        public int GetWinRateType()
        {
            int mmrBorder = (int)_googleDocsData.GetNumber("bot.mmr.winrate.border", LocalIdents.Mmr.BORDER_FIGHTS_MMR);
            int winRate = LocalIdents.Mmr.WIN_RATE_BORDER_PERCENT_GRADE0;
            if (_playerProgress.TotalBattles() > mmrBorder)
            {
                winRate = Mathf.Clamp((int)Mathf.Ceil(100 * (_playerProgress.Kills / _playerProgress.TotalBattles())), LocalIdents.Mmr.WIN_RATE_BORDER_PERCENT_GRADE0, LocalIdents.Mmr.WIN_RATE_BORDER_PERCENT_GRADE3);
            }

            return (int)GetWinRateIndex(winRate);
        }

        private WinRateGrades GetWinRateIndex(int winRate)
        {
            return winRate switch
            {
                <= LocalIdents.Mmr.WIN_RATE_BORDER_PERCENT_GRADE0 => WinRateGrades.Grade0,
                <= LocalIdents.Mmr.WIN_RATE_BORDER_PERCENT_GRADE1 => WinRateGrades.Grade1,
                <= LocalIdents.Mmr.WIN_RATE_BORDER_PERCENT_GRADE2 => WinRateGrades.Grade2,
                >= LocalIdents.Mmr.WIN_RATE_BORDER_PERCENT_GRADE3 => WinRateGrades.Grade3
            };
        }
    }
}