using System.Collections.Generic;
using System.Threading;
using Client.Common.AI.BehaviourTree.Runtime.Scripts.Components;
using Client.Common.AI.BehaviourTree.Runtime.Scripts.Runtime;
using Client.Common.AI.Data;
using Client.Common.AI.Loaders;
using Client.Common.Components.Unit;
using Client.Common.Configs;
using Client.Common.Configs.Components.Unit;
using Client.Common.CSV;
using Client.Common.Durability;
using Client.Common.Ftue.Helpers;
using Client.Common.Ftue.Infrastructure;
using Client.Common.Items;
using Client.Common.Loadout.Items;
using Client.Common.Loadout.Items.Factories;
using Client.Common.Loadout.Services;
using Client.Common.LocalLogic.TopDown.Unit;
using Client.Common.Network.MetaNet;
using Client.Common.Player.Controllers;
using Client.Common.Player.PocketFeature.Pockets;
using Client.Common.Player.Timers;
using Client.TopDown.Region.Components;
using Client.TopDown.Region.Components.AI;
using Client.TopDown.Region.Components.UI;
using Client.TopDown.Region.Data;
using Client.TopDown.Region.Gear.Bombs.Components.Unit;
using Client.TopDown.Region.UnityComponents;
using Client.TopDown.Region.VisibilityBehaviour.Cover;
using Client.TopDown.Region.VisibilityBehaviour.Cover.Components;
using Client.TopDown.Region.VisibilityBehaviour.General.Components;
using Client.TopDown.Region.VisibilityBehaviour.Overlap;
using Client.TopDown.Region.VisibilityBehaviour.Overlap.Components;
using Client.TopDown.SpawnSystem.Components;
using Client.TopDown.SpawnSystem.DataProviders.PointProviders;
using Client.Utils.CustomTweens.VFXService.Abstractions;
using Client.Utils.ECS.ECSAsyncInitSystem;
using Client.Utils.ECS.LocalWorld;
using Client.Utils.Extensions;
using Client.Utils.ResourceLoading;
using Common;
using Cysharp.Threading.Tasks;
using Leopotam.Ecs;
using UnityEngine;
using Object = UnityEngine.Object;
using TimerType = Client.Common.Network.MetaNet.TimerType;

namespace Client.TopDown.Region.Systems
{
    /// <summary>
    /// Unit spawn / remove processor.
    /// </summary>
    sealed class UnitInitSystem : IEcsAsyncInitSystem, IEcsRunSystem, IEcsDestroySystem
    {
        private const string _SKINS_PATH_ = "Units/Region/{0:D2}";
        private const float _HOOK_PARRY_DEFAULT_CHANCE_BACK_ = 0.05f;
        private const float _HOOK_PARRY_DEFAULT_CHANCE_SIDE_ = 0.1f;
        private const float _HOOK_PARRY_DEFAULT_CHANCE_FRONT_ = 0.25f;

        private readonly LocalEcsWorld _world = default;
        private readonly RegionSupport _region = default;
        private readonly PlayerManager _playerManager = default;
        private readonly GoogleDocsData _googleDocsData = default;
        private readonly ConfigService _configService = default;
        private readonly ResourceLoadingService _resourceLoadingService = default;
        private readonly ITweenManager _vfxManager = default;
        private readonly RegionVisualData _regionVisualData = null;
        private readonly MetaNet _metaNet = default;
        private readonly LoadoutUiService _loadoutUiService = default;
        private readonly FtueProgress _ftueProgress = default;
        private readonly ItemData _itemData = default;
        private readonly DurabilityService _durabilityService = default;

        private readonly EcsFilter<UnitSpawn, AiSpawnData> _spawns = default;
        private readonly EcsFilter<TopDownUnit, UnitRemove> _removes = default;

        private BotBehaviorTreeLoader _botBehaviorTreeLoader;
        private readonly CancellationTokenSource _systemCts;

        private float _hookParryChanceBack;
        private float _hookParryChanceSide;
        private float _hookParryChanceFront;
        private float _hookParryChanceBackBot;
        private float _hookParryChanceSideBot;
        private float _hookParryChanceFrontBot;

        private float _movementSpeed = 3f;

        private Dictionary<PocketType, IPocketBinder> _pocketBinders;

        public UnitInitSystem()
        {
            _systemCts = new CancellationTokenSource();
        }

        public async UniTask InitAsync(CancellationToken cancellationToken)
        {
            SetParryParameters();

            _movementSpeed = _googleDocsData.GetNumber(LocalIdents.Keys.REGION_MOVEMENT_SPEED);

            Vector3 spawnPos = _region.CurrentPlayerSpawnPosition;
            Vector3 spawnDir = _region.CurrentPlayerSpawnDirection;

            if (_playerManager.Session.RegionSessionData.EnteredFromMap && _region.CurrentRegionIsBattleRegion())
            {
                NearExitZonePointProvider pointProvider = new(_world, _region);
                pointProvider.SetExitId(_playerManager.Session.RegionExitId);
                spawnPos = pointProvider.GetSpawnPosition();
                spawnDir = pointProvider.GetSpawnRotation() * Vector3.forward;
            }

            PlayerProgress sessionProgress = _playerManager.Session.Progress;

            InitPocketBinders();

            UnitSpawn unitSpawn = new()
            {
                Rank = sessionProgress.Rank,
                OutfitId = sessionProgress.OutfitId,
                ShurikenId = sessionProgress.WeaponId,
                ShurikenSkinId = sessionProgress.WeaponSkinId,
                AmuletId = sessionProgress.AmuletId,
                SpawnDirection = spawnDir,
                SpawnPosition = spawnPos,
                TraumasId = new List<int>()
            };

            unitSpawn.AddPocketItems(_playerManager.Session.Progress.Pockets);

            SpawnPlayer(unitSpawn);

            await SetEnterRegion(cancellationToken);

            _botBehaviorTreeLoader = new BotBehaviorTreeLoader(_resourceLoadingService);
        }

        public void Destroy()
        {
            _systemCts.CancelAndDispose();
        }

        public void Run()
        {
            foreach (int spawnIndex in _spawns)
            {
                ref UnitSpawn spawn = ref _spawns.Get1(spawnIndex);
                ref AiSpawnData aiSpawnData = ref _spawns.Get2(spawnIndex);
                ref EcsEntity spawnEntity = ref _spawns.GetEntity(spawnIndex);
                SpawnBot(spawn, aiSpawnData, spawnEntity);
                spawnEntity.Destroy();
            }

            foreach (int removeIndex in _removes)
            {
                ref UnitRemove remove = ref _removes.Get2(removeIndex);

                if (remove.Timestamp <= Time.time)
                {
                    RemoveUnit(ref _removes.Get1(removeIndex), remove.Timestamp < 0f);
                }
            }
        }

        private void InitPocketBinders()
        {
            _pocketBinders = new Dictionary<PocketType, IPocketBinder>()
            {
                { PocketType.Bomb, new BombDataPocketBinder(_configService) },
                { PocketType.PowerStone, new PowerStonePocketBinder(_configService) },
                { PocketType.Hook, new HookPocketBinder(_configService) },
            };
        }

        private void SetParryParameters()
        {
            if (_region.CurrentRegionIsFtueRegion())
            {
                SetParryParametersFtue();
            }
            else
            {
                SetParryParametersDefault();
            }
        }

        private void SetParryParametersDefault()
        {
            int mmrBorderEdge = (int)_googleDocsData.GetNumber("bot.mmr.winrate.border", LocalIdents.Mmr.BORDER_FIGHTS_MMR);
            bool mmrBorder = mmrBorderEdge >= _playerManager.Session.Progress.TotalBattles();

            _hookParryChanceFront = _googleDocsData.GetNumber(
                mmrBorder ? LocalIdents.Keys.HOOK_PARRY_CHANCE_FRONT_PLAYER_FTUE : LocalIdents.Keys.HOOK_PARRY_CHANCE_FRONT_PLAYER, _HOOK_PARRY_DEFAULT_CHANCE_FRONT_);

            _hookParryChanceSide = _googleDocsData.GetNumber(
                mmrBorder ? LocalIdents.Keys.HOOK_PARRY_CHANCE_SIDE_PLAYER_FTUE : LocalIdents.Keys.HOOK_PARRY_CHANCE_SIDE_PLAYER, _HOOK_PARRY_DEFAULT_CHANCE_SIDE_);

            _hookParryChanceBack = _googleDocsData.GetNumber(
                mmrBorder ? LocalIdents.Keys.HOOK_PARRY_CHANCE_BACK_PLAYER_FTUE : LocalIdents.Keys.HOOK_PARRY_CHANCE_BACK_PLAYER, _HOOK_PARRY_DEFAULT_CHANCE_BACK_);

            _hookParryChanceBackBot = _googleDocsData.GetNumber(
                mmrBorder ? LocalIdents.Keys.HOOK_PARRY_CHANCE_FRONT_BOT_FTUE : LocalIdents.Keys.HOOK_PARRY_CHANCE_FRONT_BOT, _HOOK_PARRY_DEFAULT_CHANCE_FRONT_);

            _hookParryChanceSideBot = _googleDocsData.GetNumber(
                mmrBorder ? LocalIdents.Keys.HOOK_PARRY_CHANCE_SIDE_BOT_FTUE : LocalIdents.Keys.HOOK_PARRY_CHANCE_SIDE_BOT, _HOOK_PARRY_DEFAULT_CHANCE_SIDE_);

            _hookParryChanceFrontBot = _googleDocsData.GetNumber(
                mmrBorder ? LocalIdents.Keys.HOOK_PARRY_CHANCE_BACK_BOT_FTUE : LocalIdents.Keys.HOOK_PARRY_CHANCE_BACK_BOT, _HOOK_PARRY_DEFAULT_CHANCE_BACK_);
        }

        private void SetParryParametersFtue()
        {
            _hookParryChanceFront = 0f;
            _hookParryChanceSide = 0f;
            _hookParryChanceBack = 0f;
            _hookParryChanceBackBot = 0f;
            _hookParryChanceSideBot = 0f;
            _hookParryChanceFrontBot = 0f;
        }

        private void SpawnPlayer(in UnitSpawn unitSpawn)
        {
            EcsEntity entity = _world.NewEntity();
            ref TopDownUnit unit = ref entity.Get<TopDownUnit>();

            InitUnit(unitSpawn, ref unit, entity);

            // we can control this unit.
            entity.Get<PlayerFlag>();
            entity.Get<Unit>();

            // weak / wounded
            var weak = _playerManager.Session.Timers
                                     .GetTimers(PlayerTimers.Query.Get().WithTimerType(global::Common.TimerType.Trauma))
                                     .Count > 0;
            unit.Visual.SetWeakFlag(weak);

            unit.Visual.SetSilhouetteState(true);
            unit.Visual.Circle.InitByService(_loadoutUiService, CreateLoadoutDataProvider());

            ConfigureHookParryDataPlayer(ref unit);

            ConfigurePosition(in unitSpawn, entity);

            ConfigureVisibility(ref unit);
        }

        private void SpawnBot(UnitSpawn unitSpawn, AiSpawnData aiSpawnData, EcsEntity spawnEntity)
        {
            EcsEntity entity = _world.NewEntity();

            if (!aiSpawnData.DisableAi)
            {
                entity.Get<Ai>();
            }

            ref TopDownUnit unit = ref entity.Get<TopDownUnit>();
            entity.Get<Unit>();

            entity.Get<HookInvincibility>();
            InitUnit(unitSpawn, ref unit, entity);

            unit.Position = unitSpawn.SpawnPosition;
            unit.Direction = unitSpawn.SpawnDirection;
            // weak / wounded
            unit.TraumasId = unitSpawn.TraumasId;

            UnityUnit unitVisual = unit.Visual;
            unitVisual.SetWeakFlag(unitSpawn.TraumasId.Count != 0);

            unitVisual.SetSilhouetteState(false);

            ConfigureLoadout(unitVisual.Circle, unitSpawn);

            ConfigureHookParryDataBot(ref unit);

            ConfigurePosition(unitSpawn, entity);

            ConfigureVisibility(ref unit);

            ConfigureGuid(ref unit, spawnEntity);

            unitVisual.FadeIn(
                () =>
                {
                    RegionAIType regionAIType = aiSpawnData.AiType;
                    InitBehaviourTree(entity.Ref<TopDownUnit>(), regionAIType, _systemCts.Token).Forget();
                    entity.Del<HookInvincibility>();
                }, _systemCts.Token);
        }

        private void ConfigureLoadout(UnitCircle unitVisualCircle, UnitSpawn unitSpawn)
        {
            unitVisualCircle.SetFang(LoadoutItemId.Trauma, unitSpawn.TraumasId.Count == 0);
            unitVisualCircle.SetFang(LoadoutItemId.Hook, unitSpawn.GetPocketItem(PocketType.Hook).Id != PlayerInventory.Items.DEFAULT_HOOK);
            unitVisualCircle.SetFang(LoadoutItemId.Bomb, unitSpawn.GetPocketItem(PocketType.Bomb).Id != PlayerInventory.Items.DEFAULT_BOMB);
            unitVisualCircle.SetFang(LoadoutItemId.PowerStone, unitSpawn.GetPocketItem(PocketType.PowerStone).Id != PlayerInventory.Items.POWER_STONE_ABSENT);
            unitVisualCircle.SetFang(LoadoutItemId.ShurikenSkin, unitSpawn.ShurikenSkinId != PlayerInventory.Items.DEFAULT_SHURIKEN_SKIN_ID);
        }

        private void InitUnit(in UnitSpawn spawn, ref TopDownUnit unit, EcsEntity entity)
        {
            _world.NewEntity().Get<UnitSpawnEventHelper>().Construct(spawn.SpawnPosition, spawn.SpawnDirection);

            unit.SetMovementSpeed(_movementSpeed);
            unit.Entity = entity;

            unit.Rank = spawn.Rank;
            unit.OutfitId = spawn.OutfitId;
            unit.ShurikenId = spawn.ShurikenId;
            unit.ShurikenSkinId = spawn.ShurikenSkinId;
            unit.AmuletId = spawn.AmuletId;

            unit.Visual = LoadSkin(unit.OutfitId);
            unit.Position = spawn.SpawnPosition;
            unit.Direction = spawn.SpawnDirection;
            unit.Direction.y = 0f;
            unit.Direction.Normalize();
            unit.Visual.SetPosition(unit.Position);
            unit.Visual.SetDirection(unit.Direction);

            AddPocketComponent(spawn, unit.Entity);

            // breathing.
            unit.Visual.SetBreathing(_regionVisualData.BreathPosition, _regionVisualData.BreathRotation, _region.LocalVisualData.BreathColor);

            UnitHeadUISequence sequence = unit.Entity.Get<UnitHeadUISequence>().Construct(_vfxManager);
            sequence.Sequence.Completed += () => { sequence.Sequence.Dispose(); };
        }

        private void AddPocketComponent(in UnitSpawn spawnData, EcsEntity unitEntity)
        {
            PocketsStorageComponent storageComponent = unitEntity.Get<PocketsStorageComponent>().Construct(unitEntity, _pocketBinders);
            PocketsStorage storage = storageComponent.Storage;
            storage.Set(spawnData.PocketItems());
        }

        private void ConfigureHookParryDataPlayer(ref TopDownUnit unit)
        {
            unit.HookParryDataOrigin.Construct(_hookParryChanceFront, _hookParryChanceSide, _hookParryChanceBack);
            SetHookParryData(ref unit);
        }

        private void ConfigureHookParryDataBot(ref TopDownUnit unit)
        {
            unit.HookParryDataOrigin.Construct(_hookParryChanceFrontBot, _hookParryChanceSideBot, _hookParryChanceBackBot);
            SetHookParryData(ref unit);
        }

        private void SetHookParryData(ref TopDownUnit unit)
        {
            unit.HookParryData = unit.HookParryDataOrigin;
        }

        private void ConfigurePosition(in UnitSpawn spawn, EcsEntity entity)
        {
            // teleport unit to start point.
            ref UnitMoveTo moveTo = ref entity.Get<UnitMoveTo>();
            moveTo.Point = spawn.SpawnPosition;
            moveTo.Instant = true;
            moveTo.WorkedOut = false;
        }

        private void RemoveUnit(ref TopDownUnit unit, bool instant = false)
        {
            if (unit.Entity.Has<BehaviourTreeComponent>())
            {
                unit.Entity.Get<BehaviourTreeComponent>().Tree.Dispose();
            }

            if (unit.Entity.Has<UnitHeadUISequence>())
            {
                unit.Entity.Get<UnitHeadUISequence>().Sequence.InstantComplete(true);
            }

            if (instant)
            {
                Object.Destroy(unit.Visual.gameObject);
                unit.Visual = null;
            }
            else
            {
                unit.Visual.FadeOut();
                Object.Destroy(unit.Visual.gameObject, 1.0f);
                unit.Visual = null;
            }

            unit.Entity.Destroy();
        }

        private static UnityUnit LoadSkin(int skinId)
        {
            var asset = Resources.Load<GameObject>(string.Format(_SKINS_PATH_, skinId));

            if (!asset)
            {
                return null;
            }

            return Object.Instantiate(asset).GetComponent<UnityUnit>();
        }

        private async UniTaskVoid InitBehaviourTree(EcsComponentRef<TopDownUnit> unit, RegionAIType regionAIType, CancellationToken token)
        {
            BehaviourTreeController tree = await _botBehaviorTreeLoader.LoadFromRegionAIType(regionAIType.ToString(), token);
            token.ThrowIfCancellationRequested();

            unit.Unref().Entity.Get<AiType>().Type = regionAIType;
            _world.NewEntity().Get<BehaviourTreeCreate>().Construct(unit.Unref().Visual.gameObject, tree, unit.Unref().Entity, unit.Unref().Rank);

            Debug.Log($"[BT] Region AI Init: {regionAIType}");
        }

        private void ConfigureVisibility(ref TopDownUnit unit)
        {
            float overlapVisibilityUpdateTimeout = _googleDocsData.GetNumber(OverlapVisibilityIdents.Keys.OVERLAP_VISIBILITY_UPDATE_TIMEOUT_KEY);
            float unitCoverDistance = _googleDocsData.GetNumber(CoverIdents.Keys.COVER_VISIBILITY_UNCOVER_DISTANCE_KEY);

            unit.Entity.Get<Visibility>();

            unit.Entity.Get<OverlapVisibility>();
            unit.Entity.Get<OverlapVisibilityTimeout>().DefaultUpdateValueTimeout = overlapVisibilityUpdateTimeout;
            unit.Entity.Get<OverlapVisibilityRenderer>().Renderer = unit.Visual.GetFirstSkin();

            unit.Entity.Get<CoverVisibility>();
            unit.Entity.Get<UnitCoverDistance>().Value = unitCoverDistance;
        }

        private async UniTask SetEnterRegion(CancellationToken token)
        {
            if (!_region.CurrentRegionIsBattleRegion() || _region.CurrentRegionIsFtueRegion())
            {
                return;
            }

            await _metaNet.EnterRegion(token);
        }

        private void ConfigureGuid(ref TopDownUnit unit, EcsEntity spawnEntity)
        {
            if (!spawnEntity.Has<GuidComponent>())
            {
                return;
            }

            unit.Entity.Get<GuidComponent>().Guid = spawnEntity.Get<GuidComponent>().Guid;
        }

        private ILoadoutItemDataProvider CreateLoadoutDataProvider()
        {
            if (!_ftueProgress.Conditions.IsLoadoutUnlocked())
            {
                return new FtueLoadoutDataProvider();
            }

            LoadoutTraumaDataProvider traumaDataProvider = new(_playerManager, _ftueProgress);
            LoadoutItemConsumableDataProvider itemConsumableDataProvider = new(_playerManager, _configService, _resourceLoadingService, _ftueProgress, _googleDocsData, _itemData);

            LoadoutItemShurikenSkinDataProvider itemShurikenSkinDataProvider = new(
                _playerManager, _configService, _resourceLoadingService, _ftueProgress, _durabilityService, _googleDocsData);
            LoadoutItemDataProvider loadoutItemDataProvider = new(traumaDataProvider, itemConsumableDataProvider, itemShurikenSkinDataProvider);

            return loadoutItemDataProvider;
        }
    }
}