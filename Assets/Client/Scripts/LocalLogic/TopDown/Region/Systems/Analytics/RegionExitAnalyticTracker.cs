using System.Collections.Generic;
using Client.Common.Analytics.Helpers;
using Client.Common.Analytics.Infrastructure.Events;
using Client.Common.Analytics.Trackers.Abstract;
using Client.Common.Components.Unit;
using Client.Common.Player.Controllers;
using Client.Common.Player.Timers;
using Client.TopDown.Region.Components;
using Client.Utils.ECS.LocalWorld;
using Common;
using Leopotam.Ecs;

namespace Client.TopDown.Region.Systems.Analytics
{
    internal class RegionExitAnalyticTracker : IAnalyticTracker
    {
        private readonly LocalEcsWorld _world;
        private readonly PlayerManager _playerManager;
        
        private readonly EcsFilter<PlayerFlag, UnitTeleportedEvent, UnitExitFromRegion> _unitExitedFilter;
        private readonly AnalyticsEntriesCounterHelper _analyticsEntriesCounterHelper;

        public RegionExitAnalyticTracker(LocalEcsWorld world, PlayerManager playerManager)
        {
            _world = world;
            _playerManager = playerManager;

            _unitExitedFilter = world.GetFilter<EcsFilter<PlayerFlag, UnitTeleportedEvent, UnitExitFromRegion>>();
            _analyticsEntriesCounterHelper = new AnalyticsEntriesCounterHelper();
        }
        
        public void Track()
        {
            if (_unitExitedFilter.IsEmpty())
            {
                return;
            }

            FireEvent();
        }

        private void FireEvent()
        {
            EcsEntity analyticsEvent = _world.NewEntity();
            RegionResultData regionResult = _playerManager.Session.RegionResultData;
            List<PlayerTimer> traumas = _playerManager.Session.Timers.GetTimers(PlayerTimers.Query.Get().WithTimerType(TimerType.Trauma));
            string exitMethod = regionResult.BattleResult == global::Client.Common.Network.MetaNet.BattleResult.Win ? "SpawnPoint" : "Death";
            
            analyticsEvent.Get<AnalyticsCustomEvent>().Id = "region_exit";
            analyticsEvent.Get<AnalyticsParameters>().Parameters = new Dictionary<string, string>
            {
                ["Region"] = $"{_playerManager.Session.RegionId}",
                ["Method"] = $"{exitMethod}",
                ["Spawn point"] = $"{ _unitExitedFilter.Get3(0).Id}",
                ["Kills"] = $"{regionResult.Kills}",
                ["Trauma"] = $"{traumas.Count > 0}",
                ["Blood"] = $"{regionResult.RegionStorage.Blood}",
                ["Currency Hard"] = $"{regionResult.RegionStorage.HardCurrency}",
                ["Currency Soft"] = $"{regionResult.RegionStorage.SoftCurrency}",
                ["StoneType"] = $"{_playerManager.Session.Progress.Pockets[PocketType.PowerStone].Id}",
                ["StoneCount"] = $"{_playerManager.Session.Progress.Pockets[PocketType.PowerStone].Count}",
                ["Rank"] = _playerManager.Session.Progress.Rank.ToString(),
                ["Region Entries"] = $"{_analyticsEntriesCounterHelper.GetRegionAnalyticsCounter()}",
            };
        }
    }
}