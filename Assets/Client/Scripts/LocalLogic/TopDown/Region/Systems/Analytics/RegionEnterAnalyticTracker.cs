using System.Collections.Generic;
using Client.Common.Analytics.Helpers;
using Client.Common.Analytics.Infrastructure.Events;
using Client.Common.Analytics.Trackers.Abstract;
using Client.Common.Player.Controllers;
using Client.Common.Player.Timers;
using Client.Utils.ECS.LocalWorld;
using Common;
using Leopotam.Ecs;

namespace Client.TopDown.Region.Systems.Analytics
{
    internal class RegionEnterAnalyticTracker : IAnalyticTracker
    {
        private struct AnalyticsEnterApplied { }

        private readonly LocalEcsWorld _world;
        private readonly PlayerManager _playerManager;
        private readonly EcsFilter<RegionScope> _scopeFilter;
        private readonly EcsFilter<AnalyticsEnterApplied> _enterAppliedFilter;

        private readonly AnalyticsEntriesCounterHelper _analyticsEntriesCounterHelper;

        public RegionEnterAnalyticTracker(LocalEcsWorld world, PlayerManager playerManager)
        {
            _world = world;
            _playerManager = playerManager;

            _scopeFilter = world.GetFilter<EcsFilter<RegionScope>>();
            _enterAppliedFilter = world.GetFilter<EcsFilter<AnalyticsEnterApplied>>();
            _analyticsEntriesCounterHelper = new AnalyticsEntriesCounterHelper();
        }

        public void Track()
        {
            if (_scopeFilter.IsEmpty())
            {
                foreach (int index in _enterAppliedFilter)
                {
                    _enterAppliedFilter.GetEntity(index).Destroy();
                }

                return;
            }

            if (!_enterAppliedFilter.IsEmpty())
            {
                return;
            }

            _world.NewEntity().Get<AnalyticsEnterApplied>();

            if (_playerManager.Session.RegionResultData.Kills > 0)
            {
                return;
            }

            FireEvent();
        }

        private void FireEvent()
        {
            _analyticsEntriesCounterHelper.DelBattleAnalyticsCounter();
            _analyticsEntriesCounterHelper.SetRegionAnalyticsCounter();
            List<PlayerTimer> traumas = _playerManager.Session.Timers.GetTimers(PlayerTimers.Query.Get().WithTimerType(TimerType.Trauma));
            EcsEntity analyticsEvent = _world.NewEntity();
            analyticsEvent.Get<AnalyticsCustomEvent>().Id = "region_enter";
            analyticsEvent.Get<AnalyticsParameters>().Parameters = new Dictionary<string, string>()
            {
                ["Enter"] = $"{_playerManager.Session.RegionId}",
                ["Spawn point"] = $"{_playerManager.Session.RegionExitId + 1}",
                ["Shuriken"] = $"{_playerManager.Session.Progress.WeaponId}",
                ["Amulet"] = $"{_playerManager.Session.Progress.AmuletId}",
                ["Outfit"] = $"{_playerManager.Session.Progress.OutfitId}",
                ["Skin"] = $"{_playerManager.Session.Progress.WeaponSkinId}",
                ["Trauma"] = $"{traumas.Count > 0}",
                ["StoneType"] = $"{_playerManager.Session.Progress.Pockets[PocketType.PowerStone].Id}",
                ["StoneCount"] = $"{_playerManager.Session.Progress.Pockets[PocketType.PowerStone].Count}",
                ["Rank"] = _playerManager.Session.Progress.Rank.ToString(),
                ["Region Entries"] = $"{_analyticsEntriesCounterHelper.GetRegionAnalyticsCounter()}",
            };
        }
    }
}