using System;
using System.Collections.Generic;
using System.Linq;
using Client.Common.CSV;
using Client.Common.Ftue.Helpers;
using Client.Common.Ftue.Infrastructure;
using Client.Common.NpcTab.Timer;
using Client.Common.Player.Controllers;
using Client.Common.Player.Timers;
using Client.Common.Traumas.Data;
using Client.TopDown.Region.UnityComponents;
using Client.Utils.ConfigHelper.Abstractions;
using Client.Utils.ConfigHelper.Implementations;
using Common;
using Cysharp.Threading.Tasks;
using Leopotam.Ecs;
using Leopotam.Ecs.Ui.Components;
using Leopotam.Ecs.Ui.Systems;
using Leopotam.Localization;
using UnityEngine;
using Object = UnityEngine.Object;

namespace Client.TopDown.Region.Systems 
{
    internal sealed class UserTimersViewSystem : IEcsInitSystem, IEcsRunSystem 
    {
        private const float _TIMER_UPDATE_INTERVAL_IN_SECS_ = 1f;
        private const int _EXPAND_LIMIT_ = 3;
        private const int _EXPANDED_TIME_ = 3;
        private const int _PADDING_BETWEEN_TIMERS_ = 16;
        private const int _MINUTES_IN_DAY_ = 1440;
        private const int _START_PULSE_ANIMATION_EDGE_ = 30;

        // path.
        private const string _USER_TIMER_PATH_ = "Images/UI/Region/Timers/UserTimer";
        private const string _USER_TIMER_EXPANDER_PATH_ = "Images/UI/Region/Timers/UserTimerExpander";
        // ui.
        private const string _USER_TIMER_EXPANDER_UI_ = "UserTimerExpander";
        
        private const string _TIMERS_SAVE_KEY_ = "timers.data.{0}";
        
        private readonly PlayerManager _playerManager = default;
        private readonly CsvLocalization _localization = default;
        private readonly GoogleDocsData _googleDocsData = default;
        private readonly TraumasDurationCache _traumasDurations = default;
        private readonly FtueProgress _ftueProgress = default;
        
        private readonly EcsFilter<EcsUiClickEvent> _clicks = default;
        private readonly EcsFilter<TimerStartEvent> _startFilter = default;
        private readonly EcsFilter<TimerEndEvent> _endFilter = default;
        
        [EcsUiNamed ("Timers")] private readonly Transform _timersRoot = default;
        
        private readonly Dictionary<int, UserTimerView> _timerIdToView = new(5);
        private UserTimerExpanderView _expander;
        private float _spaceBetweenTimers;
        private float _nextUpdateTime = -1;
        private int _maxTimerInMinutes;
        private UserTimerView _userTimerPrefab;
        
        private ConfigHelper<List<int>> _timersConfigHelper;
        
        public void Init()
        {
            _timersConfigHelper = new PlayerPrefsConfigHelper<List<int>>(_TIMERS_SAVE_KEY_);
            CreateViews();
            UpdateExpansion(true);
        }
        
        public void Run ()
        {
            ProcessClicks();
            ProcessStartedTimers();
            ProcessEndedTimers();
            UpdateProgress();
        }

        private void ProcessEndedTimers()
        {
            foreach (int index in _endFilter)
            {
                TimerType type = _endFilter.Get1(index).Type;
                if (type != TimerType.Boost && type != TimerType.Trauma)
                {
                    continue;
                }
                int id = _endFilter.Get1(index).ItemId;
                if (_timerIdToView.TryGetValue(id, out UserTimerView view))
                {
                    DeleteTimerFromCached(type, id);
                    view.SetTimerAnimation(TimersAnimType.End).ContinueWith(() =>
                    {
                        Object.Destroy(view.gameObject);
                        UpdateExpansion(false);
                    });
                    _timerIdToView.Remove(id);
                }
            }
        }

        private void DeleteTimerFromCached(TimerType type, int id)
        {
            PlayerTimer data = new()
            {
                TimerType = type,
                ItemId = id
            };
            if (_timersConfigHelper.TryLoad(out List<int> savedTimers) &&
                savedTimers.Contains(data.GetHashCode()))
            {
                savedTimers.Remove(data.GetHashCode());
                _timersConfigHelper.Save(savedTimers);
            }
        }

        private void ProcessStartedTimers()
        {
            foreach (int index in _startFilter)
            {
                TimerType type = _startFilter.Get1(index).Type;
                if (type == TimerType.Boost || type == TimerType.Trauma)
                {
                    CreateViews();
                    ExpandList(true);
                    return;
                }
            }
        }

        private void CreateViews()
        {
            foreach (UserTimerView view in _timerIdToView.Values)
            {
                Object.Destroy(view.gameObject);
            }
            _timerIdToView.Clear();
            _nextUpdateTime = 0f;

            _maxTimerInMinutes = (int) _googleDocsData.GetNumber("region.timer.max", _MINUTES_IN_DAY_);
            _userTimerPrefab = Resources.Load<UserTimerView>(_USER_TIMER_PATH_);
            IOrderedEnumerable<PlayerTimer> timers = _playerManager.Session.Timers
                                                                   .GetTimers(PlayerTimers.Query.Get())
                                                                   .Where(x => x.TimerType is TimerType.Boost or TimerType.Trauma)
                                                                   .OrderBy(timer => timer.GetRemainingUtcTimeInSeconds());
            
            bool timersCacheExists = _timersConfigHelper.TryLoad(out List<int> timersCache);
            List<int> newTimersData = new List<int>();
                
            foreach (PlayerTimer timeProcess in timers)
            {
                bool newTimer = true;
                if (timersCacheExists)
                {
                    newTimer = !timersCache.Contains(timeProcess.GetHashCode());
                }

                if (TryCreateTimerView(timeProcess, newTimer))
                {
                    newTimersData.Add(timeProcess.GetHashCode());
                }
            }
            
            _timersConfigHelper.Save(newTimersData);

            UserTimerExpanderView expanderPrefab = Resources.Load<UserTimerExpanderView> (_USER_TIMER_EXPANDER_PATH_);
            _expander ??= Object.Instantiate (expanderPrefab, _timersRoot, false);
            _expander.Set ();
            _spaceBetweenTimers = _userTimerPrefab.GetComponent<RectTransform> ().sizeDelta.y + _PADDING_BETWEEN_TIMERS_;
        }

        private bool TryCreateTimerView(PlayerTimer timeProcess, bool firstTime)
        {
            if (_timerIdToView.ContainsKey(timeProcess.ItemId))
            {
                return false;
            }
            _nextUpdateTime = 0f;
            UserTimerView view = Object.Instantiate(_userTimerPrefab, _timersRoot, false);

            long duration = timeProcess.Duration;
            long value = 0;
            UserTimerView.TimerColor timerViewColor = timeProcess.TimerType == TimerType.Trauma ? UserTimerView.TimerColor.Negative : UserTimerView.TimerColor.Positive;
            if (timeProcess.TimerType == TimerType.Trauma)
            {
                duration = _traumasDurations[timeProcess.ItemId];
                value = duration-timeProcess.GetRemainingUtcTimeInSeconds();
            }
            
            //TODO: HAT-3923: highlight timer if there is another instance of the same effect type on the player
            else if (timeProcess.ItemId == PlayerInventory.Items.BOTTLE_FOR_HARPOON_EVADE &&
                     _playerManager.Session.Progress.OutfitId == PlayerInventory.Items.OUTFIT_FOR_HARPOON_EVADE)
            {
                timerViewColor = UserTimerView.TimerColor.Highlight;
            }

            view.Set(timeProcess.ItemId, _maxTimerInMinutes, duration, _localization, firstTime, value, timerViewColor);
            view.Clicked += () => UserTimerOnClicked(view, timeProcess.TimerType);
            _timerIdToView.Add(timeProcess.ItemId, view);
            return true;
        }

        private void UserTimerOnClicked(UserTimerView timerView, TimerType timerType)
        {
            if (timerType == TimerType.Trauma && !_ftueProgress.Conditions.IsTraumaCalculationEnabled())
            {
                return;
            }

            timerView.Show(true);
        }

        private void UpdateProgress()
        {
            if (_nextUpdateTime > Time.time)
            {
                return;
            }

            _nextUpdateTime = Time.time + _TIMER_UPDATE_INTERVAL_IN_SECS_;

            foreach (KeyValuePair<int, UserTimerView> pair in _timerIdToView)
            {
                List<PlayerTimer> timers = _playerManager.Session.Timers.GetTimers(PlayerTimers.Query.Get().WithItemId(pair.Key));
                foreach (PlayerTimer playerTimer in timers)
                {
                    if (playerTimer.TimerType is not (TimerType.Boost or TimerType.Trauma))
                    {
                        continue;
                    }
                    long timerLeftTime = playerTimer.GetRemainingUtcTimeInSeconds();
                    pair.Value.UpdateProgress(timerLeftTime);
                    if (timerLeftTime <= _START_PULSE_ANIMATION_EDGE_)
                    {
                        pair.Value.SetGlow(true);
                    }
                }
            }
        }

        private void ProcessClicks()
        {
            foreach (int idx in _clicks)
            {
                ref EcsUiClickEvent e = ref _clicks.Get1(idx);
                switch (e.WidgetName)
                {
                    case _USER_TIMER_EXPANDER_UI_:
                        TimerExpanderClicked().Forget();
                        break;
                }
            }
        }

        private async UniTaskVoid TimerExpanderClicked()
        {
            if (_expander.IsExpanded())
            {
                ExpandList(false);
            }
            else
            {
                ExpandList(true);
                await UniTask.Delay(TimeSpan.FromSeconds(_EXPANDED_TIME_));
                ExpandList(false);
            }
        }

        private void UpdateExpansion(bool instant)
        {
            if (_timerIdToView.Count > _EXPAND_LIMIT_)
            {
                ExpandList(_expander.IsExpanded(), instant);
            }
            else
            {
                ExpandList(false, instant);
            }
        }

        private void ExpandList(bool state, bool instant = false)
        {
            int allCounter = 0;
            int visibleCounter = 0;
            foreach (UserTimerView view in _timerIdToView.Values)
            {
                bool newState = state || allCounter < _EXPAND_LIMIT_;
                view.SetLayout(new Vector3(0f, -visibleCounter * _spaceBetweenTimers, 0f), newState ? 1f : 0f, instant);
                if (!newState)
                {
                    view.Show(false);
                }
                else
                {
                    visibleCounter++;
                }
                allCounter++;
            }

            int diff = _timerIdToView.Count - _EXPAND_LIMIT_;
            if (diff > 0)
            {
                _expander.SetLayout(new Vector3(0f, -visibleCounter * _spaceBetweenTimers, 0f), 1f, instant);
                _expander.UpdateValue(diff);
                _expander.Show(state);
            }
            else
            {
                _expander.Show(false);
                _expander.SetLayout(new Vector3(0f, -visibleCounter * _spaceBetweenTimers, 0f), 0f, instant);
            }
        }
    }
}