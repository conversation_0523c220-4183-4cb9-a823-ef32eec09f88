using System.Threading;
using Client.Common.Components.Equip;
using Client.Common.Durability;
using Client.Common.Network.MetaNet;
using Client.Common.NpcTab.Data;
using Client.Common.NpcTab.Systems.UpdateSystems;
using Client.Common.Player.Controllers;
using Client.Common.Player.Data;
using Client.Utils.ConfigHelper.Implementations;
using Client.Utils.ECS.LocalWorld;
using Client.Utils.ResultTool.Results;
using Common;
using Cysharp.Threading.Tasks;

namespace Client.TopDown.Region.Durability
{
    public class DurabilityDefaultItemsEquipper
    {
        private readonly LocalEcsWorld _world;
        private readonly MetaNet _metaNet;
        private readonly PlayerManager _playerManager;
        private readonly DurabilityService _durabilityService;
        private readonly PlayerPrefsConfigHelper<EquippedItemDurabilityCache> _playerPrefs;
        private readonly EquipperHelper _equipperHelper;

        private PlayerProgress PlayerProgress => _playerManager.Session.Progress;
        
        public DurabilityDefaultItemsEquipper(
            LocalEcsWorld world,
            MetaNet metaNet, 
            PlayerManager playerManager,
            DurabilityService durabilityService)
        {
            _world = world;
            _metaNet = metaNet;
            _playerManager = playerManager;
            _durabilityService = durabilityService;
            string prefKey = string.Format(NpcIdents.Keys.LAST_OBSERVED_EQUIPPED_ITEM_DURABILITY, ItemType.Outfit.ToString().ToLowerInvariant());
            _playerPrefs = new PlayerPrefsConfigHelper<EquippedItemDurabilityCache>(prefKey);
            _equipperHelper = new EquipperHelper(_world);
        }

        public async UniTask<Result> TryEquipDefaultOutfit(CancellationToken cancellationToken = default)
        {
            int defaultOutfitId = PlayerInventory.Items.DEFAULT_OUTFIT_ID;
            if (_durabilityService.IsBrokenOutfit(PlayerProgress.OutfitId))
            {
                UniTask<Result<ClientItemEquipResponse>> equipItemTask = _metaNet.EquipItem(defaultOutfitId, cancellationToken);
                UniTask<Result> removeItemTask = _metaNet.RemoveItemFromInventory(PlayerProgress.OutfitId, token: cancellationToken);
                (Result<ClientItemEquipResponse> equipItem, Result removeItem) = await UniTask.WhenAll(equipItemTask, removeItemTask);

                if (equipItem.IsFailure)
                {
                    return Result.Failure(equipItem.Error);
                }
                if (removeItem.IsFailure)
                {
                    return Result.Failure(removeItem.Error);
                }

                _equipperHelper.Equip(defaultOutfitId, PlayerProgress.OutfitId);
                DeleteDurabilityCache();
            }

            return Result.Success();
        }

        public async UniTask<Result> TryEquipDefaultShurikenSkin(CancellationToken cancellationToken)
        {
            int defaultShurikenSkinId = PlayerInventory.Items.DEFAULT_SHURIKEN_SKIN_ID;
            if (_durabilityService.IsBrokenShurikenSkin(PlayerProgress.WeaponId))
            {
                PlayerProgress.WeaponStorage.TryGet(PlayerProgress.WeaponId, out Weapon weapon);
                Result equipWeaponSkin = await _metaNet.EquipWeaponSkin(PlayerProgress.WeaponId, defaultShurikenSkinId, cancellationToken);
                if (equipWeaponSkin.IsFailure)
                {
                    return Result.Failure(equipWeaponSkin.Error);
                }
                
                PlayerProgress.BrokenWeaponSkinStorage.SetSkinData(weapon.Skin);

                _equipperHelper.Equip(defaultShurikenSkinId, PlayerProgress.WeaponSkinId);
            }

            return Result.Success();
        }

        private void DeleteDurabilityCache()
        {
            _playerPrefs.Delete();
        }
    }
}