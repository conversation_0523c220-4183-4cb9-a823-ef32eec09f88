using System.Collections.Generic;
using System.Globalization;
using System.Linq;
using Client.Common.CSV;
using Client.Common.Player.Controllers;
using Client.Common.Ranks;
using Client.TopDown.Region.Data;
using Client.Utils.Extensions;
using UnityEngine;

namespace Client.TopDown.SpawnSystem.DataProviders
{
    public class StandardBotRankProvider : IRankProvider
    {
        private const int _MIN_RANK_ = 0;
        private const int _FIGHTS_EXCLUDE_ = 4;

        private readonly RanksInfoProvider _ranksInfoProvider;
        private readonly PlayerProgress _playerProgress;
        private readonly int _winRate;

        private int _currentRank;
        
        private Dictionary<int, Dictionary<int, float>> _fightsExcludeData = new();

        public StandardBotRankProvider(RanksInfoProvider ranksInfoProvider, PlayerProgress playerProgress, GoogleDocsData googleDocsData, int winRate = (int)WinRateGrades.Grade0)
        {
            _ranksInfoProvider = ranksInfoProvider;
            _playerProgress = playerProgress;
            _winRate = winRate;
            GetExcludeData(googleDocsData.GetString("bot.first.fights.excludes"));
        }

        public int GetRankId()
        {
            return _ranksInfoProvider.TryGetRankInfo(_playerProgress.Rank, out RankInfo rankInfoData) ? GetRank(rankInfoData) : _MIN_RANK_;
        }

        private int GetRank(RankInfo rankInfo)
        {
            float playerFights = _playerProgress.TotalBattles() - 1;
            int excludeRank = (int)Mathf.Clamp(playerFights, 0f, playerFights);
            return GetCalculatedRank(excludeRank < _FIGHTS_EXCLUDE_ ? _fightsExcludeData[excludeRank] : rankInfo.RankEnemiesMatch[_winRate]);
        }

        private int GetCalculatedRank(Dictionary<int, float> data)
        {
            int compatibleIndex = RandomX.GetRandomProbabilityIndex(data.Values);
            int rank = data.Keys.ToArray()[compatibleIndex];
            return rank;
        }
        
        private void GetExcludeData(string rawData)
        {
            _fightsExcludeData = new Dictionary<int, Dictionary<int, float>>();
            string[] parseRanksData = rawData.Split(';');
            for (int i = 0; i < parseRanksData.Length; i++)
            {
                _fightsExcludeData[i] = new Dictionary<int, float>();
                string[] parseRankData = parseRanksData[i].Split(',');

                for (int j = 0; j < parseRankData.Length; j += 2)
                {
                    int rank = int.Parse(parseRankData[j]);
                    float percent = float.Parse(parseRankData[j + 1], NumberStyles.Float, CultureInfo.InvariantCulture);
                    _fightsExcludeData[i].Add(rank, percent);
                }
            }
        }
    }
}