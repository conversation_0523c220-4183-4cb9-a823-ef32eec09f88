using Client.Common.Analytics.Helpers;
using Client.Common.Analytics.Trackers.Ftue;
using Client.Common.CodeFlow;
using Client.Common.Ftue.Infrastructure;
using Client.Common.Ftue.Infrastructure.Data;
using Client.Common.Network.MetaNet;
using Client.Common.Npc;
using Client.Common.Player.Controllers;
using Client.TopDown.Common.SpeechTooltip.Controllers;
using Client.TopDown.Cutscene.Controllers;
using Client.TopDown.Region.Systems.Ftue;
using Common;
using SpeechIdents = Client.TopDown.Market.Data.SpeechIdents;
using TimerType = Client.Common.Network.MetaNet.TimerType;

namespace Client.TopDown.Market.Systems.Ftue
{
    internal sealed class FtueUnlockAlchemistSystem : FtueSystemBase
    {
        private const string _CUTSCENE_KEY_ = "AlchemistUnlock";
        
        private readonly RegionFtueHelper _regionHelper = default;
        private readonly NpcFtueHelper _npcHelper = default;
        private readonly SpeechHelper _speechHelper = default;
        private readonly PlayerManager _playerManager = default;
        private readonly CutsceneController _cutsceneController = default;
        private readonly IAnalyticsHelper _analyticsHelper = default;
        
        protected override AccountStep FtueStep => AccountStep.AlchemistCraftTutorialFinished;
        protected override string LogDescription => "market: alchemist unlock";

        protected override bool TutorialStartCondition()
        {
            if (!Ftue.Conditions.IsHealBottleUnlocked)
            {
                return false;
            }
            if (!IsCompleted(FtueProgressKeys.AlchemistCraftHealBottle))
            {
                return true;
            }
            bool isCrafting = _playerManager.Session.Timers.GetTimer(PlayerInventory.Items.BOTTLE_FOR_HEAL, global::Common.TimerType.Craft) != null;
            if (!isCrafting)
            {
                Ftue.Complete(FtueProgressKeys.AlchemistCompleteCraftHealBottle);
                return false;
            }
            return true;
        }

        protected override void OnInit(CodeFlow<FtueProgress> flow)
        {
            flow.Entry
                .Then(_analyticsHelper.FireContextualAnalyticsRoutine(FtueSteps.AlchemistTutorialStart))
                .Then(_regionHelper.BlockExit(SpeechIdents.ExitBlocks.NPC_EXIT_BLOCK_KEY))
                .Then(_npcHelper.EnableNpc(NpcType.Alchemist))
                .Then(_npcHelper.AttractNpc(NpcType.Alchemist))
                .Then(_cutsceneController.Cutscene(NpcType.Alchemist, _CUTSCENE_KEY_, SpeechIdents.NpcSpeechPhrases.ALCHEMIST_UNLOCK_PHRASE))
                .Then(_npcHelper.SetTargetToNpcRoutine(NpcType.Alchemist))
                .Then(_speechHelper.ShowHintIfUserIsIdle(3.5f))
                ;
        }
    }
}