using Client.Common.DailyBonus.Systems;
using Client.Common.NpcTab.Timer;
using Client.TopDown.Market.Popups.BloodRewards;
using Client.TopDown.Market.Popups.Maintenance.Systems;
using Client.TopDown.Market.Popups.VersionValidation;
using Client.TopDown.Market.Systems;
using Client.Utils.ECS.LocalWorld;
using Client.Utils.Extensions.EcsExtensions;
using Leopotam.Ecs;
using UnityEngine;

namespace Client.TopDown.Market.Actualization
{
    /// <summary>
    /// Systems that should work on game start and only once per session
    /// </summary>
    public class ProgressActualizationFeature: IEcsFeature
    {
        private readonly Transform _popupRoot;
        private readonly bool _sessionStart; 
        
        public ProgressActualizationFeature(LocalEcsWorld world, Transform popupRoot)
        {
            _popupRoot = popupRoot;
            _sessionStart = world.GetFilter<EcsFilter<SessionStarted>>().IsEmpty();
        }
        public void Build(EcsSystems systems)
        {
            if (!_sessionStart)
            {
                return;
            }
            systems.Add(new MaintenanceNotificationPopupSystem(_popupRoot))
                   .Add(new UpdateAppVersionRewardSystem())
                   .Add(new BloodRewardUnlocksPopupSystem())
                   .Add(new MarkSessionStartSystem())
                   
                   .Add(new DailyBonusInitSystem())
                   ;
        }
    }
}