using System.Linq;
using System.Threading;
using System.Threading.Tasks;
using Client.Common.Abilities.Meta.Systems;
using Client.Common.Achievements.Generators;
using Client.Common.Analytics.Helpers;
using Client.Common.Audio;
using Client.Common.BloodColba;
using Client.Common.Components.Equip;
using Client.Common.CSV;
using Client.Common.Dialogs.Runtime.Systems;
using Client.Common.Durability;
using Client.Common.ECS.LocalWorld;
using Client.Common.Items;
using Client.Common.Network.Connection.Disconnect;
using Client.Common.Network.MetaNet;
using Client.Common.NpcTab.Components;
using Client.Common.Player.Controllers;
using Client.Common.Quests.Systems;
using Client.Common.ResourcesTopPanel;
using Client.Common.ResourcesTopPanel.Configs;
using Client.Common.ScreenTransitions;
using Client.Common.Traumas.Data;
using Client.Common.UI.Buttons;
using Client.Common.AltarRace;
using Client.TopDown.Common.SpeechTooltip.Systems;
using Client.TopDown.Cutscene.Controllers;
using Client.TopDown.Market.Actualization;
using Client.TopDown.Market.AdsLantern;
using Client.TopDown.Market.AltarRace;
using Client.TopDown.Market.DailyBonus;
using Client.TopDown.Market.Leagues;
using Client.TopDown.Market.Npcs.Bank;
using Client.TopDown.Market.Npcs.Bank.Celebration;
using Client.TopDown.Market.Quests;
using Client.TopDown.Market.Quests.Goal;
using Client.TopDown.Market.Systems;
using Client.TopDown.Market.Systems.Analytics;
using Client.TopDown.Market.Systems.Ftue;
using Client.TopDown.Market.Systems.Ftue.Bombs;
using Client.TopDown.Market.Systems.Ftue.Hooks;
using Client.TopDown.Market.Systems.Ftue.Prizes;
using Client.TopDown.Market.UI;
using Client.TopDown.Market.UnityComponents;
using Client.TopDown.Region.Components;
using Client.TopDown.Region.Durability;
using Client.TopDown.Region.Systems;
using Client.TopDown.Region.Systems.UI;
using Client.TopDown.Region.Tooltips.Systems;
using Client.TopDown.Region.UnitHeadNotification.Systems;
using Client.Utils.Extensions;
using Client.Utils.Extensions.EcsExtensions.SystemExtensions;
using Client.Utils.ResultTool.Results;
using Client.Utils.Visible;
using Common;
using Cysharp.Threading.Tasks;
using External;
using Leopotam.Ecs;
using UnityEngine;
using UnityEngine.Serialization;
using VContainer;
using BloodColbaSystem = Client.TopDown.Market.Systems.BloodColbaSystem;
using DuelBeginSystem = Client.TopDown.Market.Systems.DuelBeginSystem;
using FtuePart02System = Client.TopDown.Market.Systems.Ftue.FtuePart02System;
using FtuePart04System = Client.TopDown.Market.Systems.Ftue.FtuePart04System;
using ItemQuantity = Client.Common.Network.MetaNet.ItemQuantity;
using UiInteractionSystem = Client.TopDown.Market.Systems.UiInteractionSystem;

namespace Client.TopDown.Market
{
    internal struct MarketScope
    {
    }

    internal sealed class MarketStartup : LocalWorld<MarketScope>
    {
        [SerializeField] private MarketLifeTimeScope _marketLifetimeScope;
        [SerializeField] private RectTransform _rootTransform;
        [SerializeField] private ProfileButtonView _profileButton;
        [SerializeField] private ActionButton _settingsButton;
        [SerializeField] private PrizeButtonView _prizesButton;
        [SerializeField] private LeagueButtonView _leagueButtonView;
        [SerializeField] private AltarRaceButtonView _altarRaceButton;
        [SerializeField] private QuestCompleteToastView _questCompleteToastView;
        
        [Header("Speech")]
        [SerializeField] private Transform _speechRoot;
        [SerializeField] private RectTransform _defaultCanvas;
        [SerializeField] private RectTransform _popupCanvas;

        [Inject] private LocalAnalyticsHelper _localAnalyticsHelper;
        [Inject] private TransitionService _transitionService;
        [Inject] private SoundManager _soundManager;
        [Inject] private MetaNet _metaNet;
        [Inject] private PlayerManager _playerManager;
        [Inject] private ItemData _itemData;
        [Inject] private DurabilityService _durability;
        [Inject] private VisibilityController _hudVisibilityController;
        [Inject] private GoogleDocsData _googleDocsData;

        protected override async Task OnStart(CancellationToken cancellationToken)
        {
            if (!await TryUpdateStateData(_metaNet, _playerManager, cancellationToken, _durability))
            {
                _soundManager.StopCurrentInterSceneSound();
                _transitionService.FadeOut();
                return;
            }

            Systems
                .AddFeature(new ProgressActualizationFeature(World, _popupCanvas))
                
                .AddFeature(new DailyBonusFeature(_popupCanvas))
                
                //leagues
                .Add(new LeagueInfoSystem())
                .Add(new LeagueButtonSystem(_leagueButtonView))
                .Add(new LeagueSeasonResultsPopupSystem())
                .Add(new LeagueRewardPopupSystem())
                
                .Add(new AltarRaceResultsPopupSystem())
                .Add(new AltarRaceStartEventSystem())
                
                .Add(new EnvironmentInitSystem())
                
                //lantern
                .Add(new AdsLanternSystem())
                .Add(new AdsLanternPopupSystem())
                .Add(new PhysicsClickSystem())
                
                // .Add (new FakeNetworkSystem ())
                .Add(new UiCutsceneSystem())
                .Add(new VisualFxSystem())
                .Add(new InitNpcSystem())
                .Add(new UserSpawnFixSystem())
                .Add(new UnitInitSystem())
                .Add(new UnitResetIdleViewSystem())
                .Add(new UserTimersViewSystem())
                .Add(new UiInteractionSystem(_profileButton, _settingsButton, _prizesButton))
                .Add(new UserInputSystem())
                .Add(new UnitInputSystem())
                .Add(new NpcGoToSystem())

                // connect
                .Add(new DisconnectedUnitMarkerSystem())

                // abilities
                .Add(new MetaAbilityCreateSystem())

                // ftue by priority
                .Add(new FtueKarasQteSystemComplete(_hudVisibilityController))
                .Add(new FtueKarasParrySystemComplete(_hudVisibilityController))
                .Add(new FtueKarasPowerStonesSystemComplete(_hudVisibilityController))
                .Add(new FtuePowerStonesBlacksmithStart(_hudVisibilityController))
                .Add(new FtueHooksJunkmanStart(_hudVisibilityController))
                .Add(new FtueBombsJunkmanStart(_hudVisibilityController))
                .Add(new FtueBloodAltarSystem(_defaultCanvas, _hudVisibilityController))
                .Add(new FtueDailyBonusUnlockSystem(_prizesButton))
                .Add(new FtuePart02System(_hudVisibilityController))
                .Add(new FtuePart04System())
                .Add(new FtueKarasQteSystemStart(_hudVisibilityController))
                .Add(new FtueKarasParrySystemStart(_hudVisibilityController))
                .Add(new FtueKarasPowerStonesSystemStart(_hudVisibilityController))
                .Add(new FtueUnlockAlchemistSystem())
                .Add(new FirstShurikenCraftFtueSystem())
                .Add(new FirstShurikenEquipFtueSystem(_profileButton, _rootTransform))
                .Add(new FtueFirstTraumaSystem())
                .Add(new FtueJunkmanUnlockSystem())
                .Add(new FtueUnlockShurikenSkinSystem())
                .Add(new FtueUnlockOutfitSystem())
                .Add(new FtueFirstAbilityUpgradeSystem(_profileButton, _rootTransform))
                .Add(new DirectToRegionTooltipSystem())

                .AddFeature(new FtuePrizesUnlocks(_prizesButton))

                .Add(new MarketNpcExitToolTipsGenerator())
                .Add(new MarketNpcExitToolTipsExistingSystem())
                .Add(new MarketNpcExitTooltipsSystem())
                .Add(new BankCelebrationSystem())
                .Add(new BankToolTipReminderSystem())
                .AddFeature(new DialogFeature(World, _localAnalyticsHelper))
                // AchievementsCheck
                .Add(new AchievementGenerateSystem())
                //
                .Add(new ProcessUnitExitFromMarketSystem())
                .Add(new UnitTeleportationSystem(UnityNpc.ENTER_DISTANCE))
                .Add(new NpcInteractSystem())
                .Add(new UnitMoveSystem())
                .Add(new UnitFootstepSystem())
                .Add(new CameraSystem())
                .Add(new DuelBeginSystem())
                .Add(new MarketUserInitSystem())
                .Add(new UserInputViewUpdateSystem())
                .Add(new FtueTargetSystem())
                .Add(new NpcAttractSystem())
                .Add(new TeleportInteractViewSystem(_googleDocsData.GetNumber("market.exit-to-npc.delay")))
                .AddFeature(new ResourcesTopPanelFeature(ResourcesTopPanelConfig.FullAnimated))
                .Add(new CrowSystem())

                //prizes and profile
                .Add(new ProfileButtonInitSystem(_profileButton))
                .Add(new DailyQuestRefreshSystem())
                .Add(new DrainBloodQuestProgressSystem())
                .Add(new PrizeIconSystem(_prizesButton))
                .Add(new QuestCompletionToastsSystem(_questCompleteToastView, _prizesButton))
                // tooltips
                .Add(new UnitSpeechSystem(_defaultCanvas, _speechRoot))
                .Add(new LockedNpcTooltipSystem())
                .AddFeature(new TooltipsFeature(_defaultCanvas))
                .AddFeature(new UnitHeadNotificationFeature())

                // BloodColba
                .Add(new BloodColbaMaxSizeAbilityRemoveSystem())
                .Add(new BloodColbaMaxSizeApplySystem(_playerManager.Session))
                .Add(new BloodColbaSystem())
                // BloodAltarEffect
                .Add(new BloodAltarAnimationSystem(_altarRaceButton))
                .Add(new AltarRaceButtonSystem(_altarRaceButton))
                // should be last.
                .Add(new MarketAudioSystem())
                .Add(new MarketAnalyticsTrackSystem())
                .Add(new FadeOutSystem())
                .Add(new GoToRegionSystem())
                .OneFrame<UnitInput>()
                .OneFrame<UnitTeleportedEvent>()
                .OneFrame<BloodColbaDebugAfterShow>()
                .OneFrame<BloodColbaHide>()
                .OneFrame<BloodDrainEvent>()
                .OneFrame<LanternClicked>()
                .OneFrame<AdsLanternOpened>()
                .OneFrame<AdsLanternClosed>()
                .OneFrame<AdsLanternAdsWatched>()
                .OneFrame<EquippedEvent>()
                .OneFrame<UnequippedEvent>()
                .InjectFromContainer(_marketLifetimeScope.Container)
                ;
        }

        private async UniTask<bool> TryUpdateStateData(MetaNet metaNet, PlayerManager playerManager, CancellationToken cancellationToken, DurabilityService durabilityService)
        {
            playerManager.Session.RegionId = 1;

            UniTask<Result>[] tasks = {
                TryEquipAvailableAmulet(metaNet, playerManager, cancellationToken),
                TryEquipDurabilityDefaultItems(metaNet, playerManager, cancellationToken, durabilityService),
            };
            
            Result[] results = await UniTask.WhenAll(tasks);
            return !results.AnyFailure();
        }

        private async UniTask<Result> TryEquipAvailableAmulet(MetaNet metaNet, PlayerManager playerManager,  CancellationToken cancellationToken)
        {
            if (playerManager.Session.Progress.AmuletId == 0)
            {
                ItemQuantity inventoryAmulet = playerManager.Session.Inventory
                                                            .GetItemsByType(ItemType.Amulet)
                                                            .Select(index => playerManager.Session.Inventory.GetData(index))
                                                            .FirstOrDefault(item => item.Count != 0);

                if (inventoryAmulet.Count != 0)
                {
                    Result<ClientItemEquipResponse> equipItem = await metaNet.EquipItem(inventoryAmulet.Id, cancellationToken);
                    if (equipItem.IsFailure)
                    {
                        return Result.Failure(equipItem.Error);
                    }
                    World.NewEntity().Get<EquippedEvent>().ItemId = inventoryAmulet.Id;
                }
            }

            return Result.Success();
        }

        private async UniTask<Result> TryEquipDurabilityDefaultItems(MetaNet metaNet, PlayerManager playerManager, CancellationToken cancellationToken, DurabilityService durabilityService)
        {
            DurabilityDefaultItemsEquipper durabilityEquipper = new(
                World,
                metaNet,
                playerManager,
                durabilityService
            );

            Result outfitResult = await durabilityEquipper.TryEquipDefaultOutfit(cancellationToken);
            if (outfitResult.IsFailure)
            {
                return Result.Failure(outfitResult.Error);
            }

            Result skinResult = await durabilityEquipper.TryEquipDefaultShurikenSkin(cancellationToken);
            if (skinResult.IsFailure)
            {
                return Result.Failure(skinResult.Error);
            }

            return Result.Success();
        }
    }
}