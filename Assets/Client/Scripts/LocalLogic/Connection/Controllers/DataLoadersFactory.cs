using System.Collections.Generic;
using Client.Common.Abilities.Infrastructure.Data;
using Client.Common.Abilities.Infrastructure.Loaders;
using Client.Common.Abilities.Meta.Loaders;
using Client.Common.Abilities.Modification.Item.Loaders;
using Client.Common.AI.Givers;
using Client.Common.AI.Loaders;
using Client.Common.Audio;
using Client.Common.Configs.Loaders;
using Client.Common.Configs.Providers.Abilities;
using Client.Common.Countries;
using Client.Common.CraftReadyItems;
using Client.Common.CSV;
using Client.Common.DailyBonus.Givers;
using Client.Common.Ftue.Infrastructure;
using Client.Common.GameSettings;
using Client.Common.Items;
using Client.Common.Items.ItemContentProviders;
using Client.Common.Items.QuantityProviders;
using Client.Common.Network.Loaders;
using Client.Common.Network.MetaNet;
using Client.Common.Player.Controllers;
using Client.Common.Quests;
using Client.Common.Ranks;
using Client.Common.TelegramBot;
using Client.Common.TimeGiver.Abstractions;
using Client.Common.TimeGiver.Implementations;
using Client.Utils.Loaders;
using Client.Utils.ResourceLoading;
using Leopotam.Localization;

namespace Client.Connection.Controllers
{
    public class DataLoadersFactory
    {
        private readonly MetaNet _metaNet;
        private readonly GoogleDocsDataLoader _googleDocsDataLoader;
        private readonly PlayerSession _playerSession;
        private readonly CsvLocalization _localization;
        private readonly TimeService _timeService;
        private readonly ServerTimeService _serverTimeService;
        private readonly GoogleDocsData _googleDocsData;
        private readonly BotDifficultyData _botDifficultyData;
        private readonly BotAITypeGiver _botAITypeGiver;
        private readonly CountryData _countryData;
        private readonly BotNameData _botNameData;
        private readonly AbilityByItemProvider _abilityByItemProvider;
        private readonly AbilityGradesProvider _abilityGradesProvider;
        private readonly AbilityModifierDataByItemProvider _abilityModifiersByItems;
        private readonly FtueProgress _ftueProgress;
        private readonly ItemData _itemData;
        private readonly RanksInfoProvider _ranksInfoProvider;
        private readonly QuantifiedIdProvider _quantifiedIdProvider;
        private readonly QuantifiedLocalizationService _quantifiedLocalization;
        private readonly GameSettingsGiver _gameSettingsGiver;
        private readonly QuestsData _questsData;
        private readonly BloodRewardDataProvider _bloodRewardDataProvider;
        private readonly BotDifficultyMmrGiver _botDifficultyMmr;
        private readonly CraftReadyItemsService _craftReadyItemsService;
        private readonly AbilityGradeService _abilityGradeService;
        private readonly DailyBonusGiver _dailyBonusGiver;
        private readonly TelegramNet _telegramNet;
        private readonly ResourceLoadingService _resourceLoadingService;
        private readonly SoundManager _soundManager;

        public DataLoadersFactory(MetaNet metaNet, GoogleDocsDataLoader googleDocsDataLoader, PlayerSession playerSession, CsvLocalization localization, TimeService timeService,
                                  ServerTimeService serverTimeService, GoogleDocsData googleDocsData, BotDifficultyData botDifficultyData, BotAITypeGiver botAITypeGiver, CountryData countryData,
                                  BotNameData botNameData, AbilityByItemProvider abilityByItemProvider, AbilityGradesProvider abilityGradesProvider,
                                  AbilityModifierDataByItemProvider abilityModifiersByItems, FtueProgress ftueProgress, ItemData itemData, RanksInfoProvider ranksInfoProvider,
                                  QuantifiedIdProvider quantifiedIdProvider, QuantifiedLocalizationService quantifiedLocalization, GameSettingsGiver gameSettingsGiver, QuestsData questsData,
                                  BloodRewardDataProvider bloodRewardDataProvider, BotDifficultyMmrGiver botDifficultyMmr, CraftReadyItemsService craftReadyItemsService,
                                  AbilityGradeService abilityGradeService, DailyBonusGiver dailyBonusGiver, TelegramNet telegramNet, ResourceLoadingService resourceLoadingService,
                                  SoundManager soundManager)
        {
            _metaNet = metaNet;
            _googleDocsDataLoader = googleDocsDataLoader;
            _playerSession = playerSession;
            _localization = localization;
            _timeService = timeService;
            _serverTimeService = serverTimeService;
            _googleDocsData = googleDocsData;
            _botDifficultyData = botDifficultyData;
            _botAITypeGiver = botAITypeGiver;
            _countryData = countryData;
            _botNameData = botNameData;
            _abilityByItemProvider = abilityByItemProvider;
            _abilityGradesProvider = abilityGradesProvider;
            _abilityModifiersByItems = abilityModifiersByItems;
            _ftueProgress = ftueProgress;
            _itemData = itemData;
            _ranksInfoProvider = ranksInfoProvider;
            _quantifiedIdProvider = quantifiedIdProvider;
            _quantifiedLocalization = quantifiedLocalization;
            _gameSettingsGiver = gameSettingsGiver;
            _questsData = questsData;
            _bloodRewardDataProvider = bloodRewardDataProvider;
            _botDifficultyMmr = botDifficultyMmr;
            _craftReadyItemsService = craftReadyItemsService;
            _abilityGradeService = abilityGradeService;
            _dailyBonusGiver = dailyBonusGiver;
            _telegramNet = telegramNet;
            _resourceLoadingService = resourceLoadingService;
            _soundManager = soundManager;
        }

        public List<BaseLoader> LoadAll()
        {
            List<BaseLoader> loaders = new();

            loaders.Add(CreateTimeLoader(_serverTimeService, _metaNet));
            loaders.Add(CreateGameSettingsLoader(_gameSettingsGiver));
            loaders.Add(CreateLocalizationLoader(_localization, _googleDocsDataLoader, _gameSettingsGiver, _resourceLoadingService));
            loaders.AddRange(
                CreateTableDataLoaders(
                    _metaNet,
                    _googleDocsDataLoader,
                    _playerSession,
                    _googleDocsData,
                    _botDifficultyData,
                    _botAITypeGiver,
                    _countryData,
                    _botNameData,
                    _abilityByItemProvider,
                    _abilityGradesProvider,
                    _abilityModifiersByItems,
                    _itemData,
                    _ranksInfoProvider,
                    _quantifiedIdProvider,
                    _quantifiedLocalization,
                    _bloodRewardDataProvider,
                    _botDifficultyMmr,
                    _dailyBonusGiver));

            loaders.AddRange(CreatePlayerProgressLoaders(_itemData, _ftueProgress, _metaNet, _playerSession, _timeService, _questsData, _googleDocsData, _craftReadyItemsService, _telegramNet));
            loaders.Add(new AbilityGradeServiceLoader(_abilityGradeService));
            loaders.Add(CreateTgBotEventsScheduleLoader());

            return loaders;
        }

        public LocalizationLoader CreateLocalizationLoader(CsvLocalization localization, GoogleDocsDataLoader googleDocsDataLoader, GameSettingsGiver gameSettingsGiver,
                                                           ResourceLoadingService resourceLoadingService)
        {
            return new LocalizationLoader(googleDocsDataLoader, localization, gameSettingsGiver, resourceLoadingService);
        }

        public ServerTimeServiceLoader CreateTimeLoader(ServerTimeService timeGiver, MetaNet metaNet)
        {
            return new ServerTimeServiceLoader(timeGiver, metaNet);
        }

        public BaseLoader[] CreatePlayerProgressLoaders(ItemData itemData, FtueProgress ftueProgress, MetaNet metaNet, PlayerSession playerSession,
            TimeService timeService, QuestsData questsData, GoogleDocsData googleDocsData, CraftReadyItemsService craftReadyItemsService, TelegramNet telegramNet)
        {
            return new BaseLoader[]
            {
                new PlayerProgressLoader(metaNet, playerSession),
                new FtueLoader(ftueProgress),
                new PlayerInventoryLoader(metaNet, playerSession, itemData),
                new TimersLoader(metaNet, playerSession.Timers),
                new QuestDataLoader(metaNet, timeService, ftueProgress, questsData, googleDocsData, telegramNet, playerSession)
            };
        }

        public List<BaseLoader> CreateTableDataLoaders(MetaNet metaNet, GoogleDocsDataLoader googleDocsDataLoader,
            PlayerSession playerSession,
            GoogleDocsData googleDocsData,
            BotDifficultyData botDifficultyData,
            BotAITypeGiver botAITypeGiver,
            CountryData countryData,
            BotNameData botNameData,
            AbilityByItemProvider abilityByItemProvider,
            AbilityGradesProvider abilityGradesProvider,
            AbilityModifierDataByItemProvider abilityModifierDataByItemProvider,
            ItemData itemData,
            RanksInfoProvider ranksInfoSata,
            QuantifiedIdProvider quantifiedIdProvider,
            QuantifiedLocalizationService quantifiedLocalization,
            BloodRewardDataProvider bloodRewardDataProvider,
            BotDifficultyMmrGiver botDifficultyMmrGiver,
            DailyBonusGiver dailyBonusGiver) =>
            new List<BaseLoader>
            {
                new GoogleDocsLoader(googleDocsDataLoader, googleDocsData),
                new ServerPreferencesLoader(metaNet, googleDocsData),
                new ItemDataLoader(metaNet, itemData),
                new BloodRewardsLoader(metaNet, bloodRewardDataProvider),
                new QuantityRangesLoader(googleDocsDataLoader, quantifiedIdProvider, quantifiedLocalization),
                new BotDifficultyLoader(googleDocsDataLoader, botDifficultyData),
                new AITypesDataLoader(googleDocsDataLoader, botAITypeGiver),
                new CountriesLoader(googleDocsDataLoader, countryData),
                new BotNamesLoader(googleDocsDataLoader, botDifficultyData, botNameData),
                new AbilityByItemLoader(googleDocsDataLoader, abilityByItemProvider),
                new AbilityDataLoader(googleDocsDataLoader, abilityGradesProvider),
                new MetaAbilitiesLoader(metaNet, playerSession, googleDocsData),
                new AbilityModifiersByItemsLoader(googleDocsDataLoader, abilityModifierDataByItemProvider),
                new RanksInfoDataLoader(googleDocsDataLoader, ranksInfoSata, googleDocsData),
                new BotDifficultyMmrLoader(googleDocsDataLoader, botDifficultyMmrGiver),
                new DailyRewardStreakLoader(googleDocsDataLoader, dailyBonusGiver),
                new DailyRewardCoeffLoader(googleDocsDataLoader, dailyBonusGiver),
                new DailyRewardBonusLoader(googleDocsDataLoader, dailyBonusGiver),
            };

        private GameSettingsLoader CreateGameSettingsLoader(GameSettingsGiver gameSettingsGiver) => new(gameSettingsGiver, _soundManager);

        private TgBotEventsScheduleLoader CreateTgBotEventsScheduleLoader() => new(_googleDocsDataLoader, _questsData);
    }
}