using System.Threading;
using System.Threading.Tasks;
using Client.Common.Audio.Banks;
using Client.Common.Components.Equip;
using Client.Common.ECS.LocalWorld;
using Client.Common.Items;
using Client.Common.Network.MetaNet;
using Client.Common.Player.Controllers;
using Client.Common.Profile.Audio;
using Client.Common.SceneLoading.SceneData.Implementations;
using Client.Common.ScreenTransitions;
using Client.Profile.Common.Consumables;
using Client.Profile.Common.Consumables.Screen;
using Client.Scripts.LocalLogic.Profile.ProfilePowerStones.Data;
using Client.Utils.Extensions.EcsExtensions.SystemExtensions;
using Client.Utils.ViewService;
using UnityEngine;
using VContainer;

namespace Client.Scripts.LocalLogic.Profile.ProfilePowerStones
{
    public struct ProfilePowerStonesScope
    {
    }

    public class ProfilePowerStonesStartup : LocalWorld<ProfilePowerStonesScope>
    {
        [SerializeField] private ProfilePowerStonesLifeTimeScope _lifeTimeScope;
        [SerializeField] private Transform _screenRoot;

        [Inject] private ProfilePowerStonesSceneLoadData _sceneLoadData;
        [Inject] private PlayerManager _playerManager;
        [Inject] private MetaNet _metaNet;
        [Inject] private ItemData _itemData;
        [Inject] private PowerStoneScreenPresenterFactory _powerStoneScreenPresenterFactory;
        [Inject] private ViewService _viewService;

        protected override async Task OnStart(CancellationToken cancellationToken)
        {
            _sceneLoadData.ItemIdToOpen = _sceneLoadData.ItemIdToOpen <= 0 ? _playerManager.Session.Progress.Pockets.PowerStone.Id : _sceneLoadData.ItemIdToOpen;
            ProfilePowerStonesScreenPresenter presenter = await _powerStoneScreenPresenterFactory.CreateScreenPresenter(cancellationToken);
            await _viewService.Open<ProfilePowerStonesScreenPresenter, ProfileConsumableScreenView, ProfileConsumableScreenModel>(presenter, root: _screenRoot);

            Systems
                .Add(new ProfileAudioSystem())
                .Add(new ProfileConsumableAnalyticsTrackSystem<ProfilePowerStonesScope>(LocalIdents.Analytics.PROFILE_POWER_STONES))
                .Add(new FadeOutSystem())
                .OneFrame<UnequippedEvent>()
                .OneFrame<EquippedEvent>()
                .InjectFromContainer(_lifeTimeScope.Container)
                ;
        }
    }
}