using System.Threading;
using System.Threading.Tasks;
using Client.Common.Components.Equip;
using Client.Common.ECS.LocalWorld;
using Client.Common.Items;
using Client.Common.Network.MetaNet;
using Client.Common.Player.Controllers;
using Client.Common.Profile.Audio;
using Client.Common.SceneLoading.SceneData.Implementations;
using Client.Common.ScreenTransitions;
using Client.Profile.Common.Consumables;
using Client.Profile.Common.Consumables.Screen;
using Client.Scripts.LocalLogic.Profile.ProfileHooks.Data;
using Client.Utils.Extensions.EcsExtensions.SystemExtensions;
using Client.Utils.ViewService;
using UnityEngine;
using VContainer;

namespace Client.Scripts.LocalLogic.Profile.ProfileHooks
{
    public struct ProfileHookScope
    {
    }

    public class ProfileHookStartup : LocalWorld<ProfileHookScope>
    {
        [SerializeField] private ProfileHookLifeTimeScope _lifeTimeScope;
        [SerializeField] private Transform _screenRoot;

        [Inject] private ViewService _viewService;
        [Inject] private ProfileHooksSceneLoadData _sceneLoadData;
        [Inject] private PlayerManager _playerManager;
        [Inject] private MetaNet _metaNet;
        [Inject] private ItemData _itemData;
        [Inject] private HookScreenPresenterScreenFactory _hookScreenPresenterScreenFactory;

        protected override async Task OnStart(CancellationToken cancellationToken)
        {
            _sceneLoadData.ItemIdToOpen = _sceneLoadData.ItemIdToOpen <= 0 ? _playerManager.Session.Progress.Pockets.Hook.Id : _sceneLoadData.ItemIdToOpen;
            ProfileHooksScreenPresenter presenter = await _hookScreenPresenterScreenFactory.CreateScreenPresenter(cancellationToken);
            await _viewService.Open<ProfileHooksScreenPresenter, ProfileHooksScreenView, ProfileConsumableScreenModel>(presenter, root: _screenRoot, cancellationToken: cancellationToken);

            Systems
                .Add(new ProfileAudioSystem())
                .Add(new ProfileConsumableAnalyticsTrackSystem<ProfileHookScope>(LocalIdents.Analytics.PROFILE_HOOKS))
                .Add(new FadeOutSystem())
                .OneFrame<UnequippedEvent>()
                .OneFrame<EquippedEvent>()
                .InjectFromContainer(_lifeTimeScope.Container)
                ;
        }
    }
}