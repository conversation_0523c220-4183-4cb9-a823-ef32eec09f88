using System.Threading;
using System.Threading.Tasks;
using Client.Common.Audio.Banks;
using Client.Common.Components.Equip;
using Client.Common.ECS.LocalWorld;
using Client.Common.Items;
using Client.Common.Network.MetaNet;
using Client.Common.Player.Controllers;
using Client.Common.Profile.Audio;
using Client.Common.SceneLoading.SceneData.Implementations;
using Client.Common.ScreenTransitions;
using Client.Profile.Common.Consumables;
using Client.Profile.Common.Consumables.Screen;
using Client.Utils.Extensions.EcsExtensions.SystemExtensions;
using Client.Utils.ViewService;
using UnityEngine;
using VContainer;

namespace Client.Profile.ProfileBombs
{
    public struct ProfileBombsScope
    {
    }
    
    public class ProfileBombsStartup: LocalWorld<ProfileBombsScope>
    {
        [SerializeField] private ProfileBombsLifeTimeScope _lifetimeScope;

        [Inject] private ProfileBombSceneLoadData _sceneLoadData;
        [Inject] private PlayerManager _playerManager;
        [Inject] private MetaNet _metaNet;
        [Inject] private ItemData _itemData;
        [Inject] private BombScreenPresenterFactory _bombScreenPresenterFactory;
        [Inject] private ViewService _viewService;
        
        protected override async Task OnStart(CancellationToken cancellationToken)
        {
            _sceneLoadData.ItemIdToOpen = _sceneLoadData.ItemIdToOpen <= 0 ? _playerManager.Session.Progress.Pockets.Bomb.Id : _sceneLoadData.ItemIdToOpen;
            ProfileBombScreenPresenter presenter = await _bombScreenPresenterFactory.CreateScreenPresenter(cancellationToken);
            await _viewService.Open<ProfileBombScreenPresenter, ProfileConsumableScreenView, ProfileConsumableScreenModel>(presenter, cancellationToken: cancellationToken);

            Systems
                .Add(new ProfileAudioSystem())
                .Add(new ProfileConsumableAnalyticsTrackSystem<ProfileBombsScope>(LocalIdents.Analytics.PROFILE_BOMBS))
                .Add(new FadeOutSystem())
                .OneFrame<UnequippedEvent>()
                .OneFrame<EquippedEvent>()
                .InjectFromContainer(_lifetimeScope.Container)
                ;
        }
    }
}