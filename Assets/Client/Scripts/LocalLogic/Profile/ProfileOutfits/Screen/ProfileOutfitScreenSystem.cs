using System.Collections.Generic;
using System.Linq;
using System.Threading;
using System.Threading.Tasks;
using Client.Common.Abilities.Helpers.Info;
using Client.Common.Configs;
using Client.Common.Configs.Components;
using Client.Common.Configs.Components.Items;
using Client.Common.Configs.Components.Items.TypeMarkers;
using Client.Common.Controllers.EquipHelper;
using Client.Common.Durability;
using Client.Common.ItemParts;
using Client.Common.Items;
using Client.Common.Network.MetaNet;
using Client.Common.Player.Controllers;
using Client.Common.Profile.Placeholder;
using Client.Common.SceneLoading;
using Client.Common.SceneLoading.SceneData.Implementations;
using Client.Common.UI.ScrollController.Controllers;
using Client.Profile.ProfileOutfits.DurabilityBlock;
using Client.Profile.ProfileOutfits.Item;
using Client.Profile.ProfileOutfits.Screen.StateMachine;
using Client.Profile.ProfileOutfits.Scroll;
using Client.Utils.CustomTweens.VFXService;
using Client.Utils.CustomTweens.VFXService.Abstractions;
using Client.Utils.ECS.ECSAsyncInitSystem;
using Client.Utils.ResourceLoading;
using Client.Utils.ViewService;
using Cysharp.Threading.Tasks;
using Leopotam.Ecs;
using Leopotam.Localization;
using UnityEngine;
using SkinDurability = Common.SkinDurability;

namespace Client.Profile.ProfileOutfits.Screen
{
    public class ProfileOutfitScreenSystem : IEcsAsyncInitSystem
    {
        private readonly ConfigService _configService = default;
        private readonly ViewService _viewService = default;
        private readonly ResourceLoadingService _resourceLoadingService = default;
        private readonly PlayerManager _playerManager = default;
        private readonly CsvLocalization _localization = default;
        private readonly ProfileOutfitSceneLoadData _sceneData = default;
        private readonly MetaNet _metaNet;
        private readonly AnimatedSceneLoader _sceneLoader = default;
        private readonly ItemEquipHelper _equipHelper = default;
        private readonly DurabilityService _durabilityService = default;
        private readonly AbilityDescriptionProvider _abilityDescriptionProvider = default;

        private EcsFilter<ConfigComponent, ItemIdData, ResourceConfigsData, OutfitItemMarker> _itemConfigs;

        private ProfileOutfitItemPresenterFactory _itemPresenterFactory;

        private ITweenManager _tweenManager;

        private readonly Transform _screenRoot;
        private CancellationToken _cancellationToken;

        private PlayerProgress PlayerProgress => _playerManager.Session.Progress;
        private PlayerInventory PlayerInventory => _playerManager.Session.Inventory;

        public ProfileOutfitScreenSystem(Transform screenRoot)
        {
            _screenRoot = screenRoot;
        }

        public async UniTask InitAsync(CancellationToken cancellationToken)
        {
            _cancellationToken = cancellationToken;
            _itemConfigs = _configService.GetFilter<EcsFilter<ConfigComponent, ItemIdData, ResourceConfigsData, OutfitItemMarker>>();

            _tweenManager = new TweenManager();

            _itemPresenterFactory = new ProfileOutfitItemPresenterFactory(_configService, _viewService, _resourceLoadingService, _durabilityService);
            
            await CreateScreen();
        }

        private async UniTask CreateScreen()
        {
            ResourceConfig screenResourceConfig = new()
            {
                Path = "UI/Profile/Outfits/ProfileOutfitScreen",
                LoadFrom = LoadFromType.Ressources,
            };

            ProfileOutfitScrollFactory scrollFactory = await CreateScrollFactory();
            DurabilityBlockController durabilityBlockController = CreateDurabilityBlockController();
            ProfileOutfitsScreenFsm screenFsm = CreateScreenFsm();

            ProfileOutfitScreenPresenter screenPresenter = await CreateScreenPresenter();

            await _viewService.Open<ProfileOutfitScreenPresenter, ProfileOutfitScreenView, ProfileOutfitScreenModel>(screenPresenter, screenResourceConfig, _screenRoot, cancellationToken: _cancellationToken);
            return;

            async Task<ProfileOutfitScrollFactory> CreateScrollFactory()
            {
                ScrollGenerator scrollGenerator = await new ScrollGenerator().InitAsync(_resourceLoadingService, _cancellationToken);
                ProfileOutfitScrollFactory profileOutfitScrollFactory = new(scrollGenerator, _cancellationToken);

                return profileOutfitScrollFactory;
            }
            
            DurabilityBlockController CreateDurabilityBlockController()
            {
                return new DurabilityBlockController(_localization, _durabilityService);
            }

            ProfileOutfitsScreenFsm CreateScreenFsm()
            {
                ItemPartsChecker itemPartsChecker = new(_configService, PlayerInventory);

                return new ProfileOutfitsScreenFsm(PlayerProgress, _equipHelper, _localization, _sceneLoader, itemPartsChecker);
            }

            async UniTask<ProfileOutfitScreenPresenter> CreateScreenPresenter()
            {
                List<ProfileOutfitItemPresenter> itemPresenters = (await CreateItemPresenters()).ToList();
                ItemPlaceholderPresenter placeholderPresenter = await _itemPresenterFactory.CreatePlaceholder(cancellationToken: _cancellationToken);
                
                ProfileOutfitScreenModel screenModel = new(itemPresenters, placeholderPresenter, _sceneData.ItemIdToOpen);
                ProfileOutfitScreenPresenter outfitScreenPresenter = new(screenModel, screenFsm, scrollFactory, durabilityBlockController, _abilityDescriptionProvider, _localization, _tweenManager);
                outfitScreenPresenter.CloseClicked += Close;

                return outfitScreenPresenter;
            }
        }

        private UniTask<ProfileOutfitItemPresenter[]> CreateItemPresenters()
        {
            List<int> brokenOutfits = new List<int>();
            foreach (SkinDurability outfit in PlayerProgress.OutfitDurabilities)
            {
                if (outfit.Durability > 0)
                {
                    continue;
                }
                brokenOutfits.Add((int)outfit.Id);
            }

            List<UniTask<ProfileOutfitItemPresenter>> createTasks = new();

            foreach (int i in _itemConfigs)
            {
                int itemId = _itemConfigs.Get2(i).Id;

                if (PlayerInventory.Contains(itemId) || brokenOutfits.Contains(itemId))
                {
                    string configKey = _itemConfigs.Get1(i).Key;
                    UniTask<ProfileOutfitItemPresenter> presenterPrepareTask = _itemPresenterFactory.CreateItem(configKey, cancellationToken: _cancellationToken);

                    createTasks.Add(presenterPrepareTask);
                }
            }

            return UniTask.WhenAll(createTasks);
        }

        private void Close()
        {
            _sceneLoader.BackInHistory();
        }
    }
}