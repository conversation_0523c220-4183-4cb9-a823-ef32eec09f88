using System.Threading;
using System.Threading.Tasks;
using Client.Common.Audio;
using Client.Common.ECS.LocalWorld;
using Client.Common.HintSystem;
using Client.Common.ScreenTransitions;
using Client.Common.UI.BackButton;
using Client.Common.UI.Buttons;
using Client.Profile.ProfileProgress.Systems;
using Client.Profile.ProfileProgress.Systems.Analytics;
using Client.Profile.ProfileProgress.Systems.Ftue;
using Client.Utils.Extensions.EcsExtensions.SystemExtensions;
using UnityEngine;

namespace Client.Profile.ProfileProgress
{
    internal struct ProfileProgressScope
    {
    }

    internal sealed class ProfileProgressStartup : LocalWorld<ProfileProgressScope>
    {
        [SerializeField] private ProfileProgressLifeTimeScope _lifeTimeScope;
        [SerializeField] private ActionButton _backButton;

        protected override Task OnStart(CancellationToken cancellationToken)
        {
            Systems
                .Add(new UiInitSystem())
                .Add(new FtueBloodAltarFirstEnter(_backButton))
                .Add(new BloodProgressSystem())
                .Add(new BloodLineSystem())
                
                .Add(new BackButtonWithGlintSystem(_backButton))
                .Add(new BasicAudioSystem())
                .Add(new ProfileProgressAnalyticsTrackSystem())
                .Add(new FadeOutSystem())
                .InjectFromContainer(_lifeTimeScope.Container)
                ;
            
            return Task.CompletedTask;
        }
    }
}
