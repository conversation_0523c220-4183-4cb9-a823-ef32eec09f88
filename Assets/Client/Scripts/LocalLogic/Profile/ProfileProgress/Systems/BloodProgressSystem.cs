using System.Threading;
using Client.Common.Audio;
using Client.Common.Audio.Banks;
using Client.Common.BloodProgress.BloodRewards;
using Client.Common.LocalLogic.BloodAltar.Components;
using Client.Common.Network.MetaNet;
using Client.Common.Player.Controllers;
using Client.Common.UI.InputLockService;
using Client.Common.UI.Rewards.Core;
using Client.Profile.ProfileProgress.Items;
using Client.Profile.ProfileProgress.Services;
using Client.Profile.ProfileProgress.UnityComponents;
using Client.Utils.ECS.LocalWorld;
using Client.Utils.ECS.ECSAsyncInitSystem;
using Client.Utils.Extensions;
using Client.Utils.ResultTool.Results;
using Common;
using Cysharp.Threading.Tasks;
using External;
using Leopotam.Ecs;
using UnityEngine;

namespace Client.Profile.ProfileProgress.Systems
{
    public class BloodProgressSystem : IEcsRunSystem, IEcsAsyncInitSystem
    {
        private const int _RULER_SPEED_ = 180;

        private readonly LocalEcsWorld _world = default;
        private readonly MetaNet _metaNet = default;
        private readonly PlayerManager _playerManager = default;
        private readonly FullScreenLocker _screenLocker = default;
        private readonly BazarAudio _audio = default;
        private readonly LocalSharedState _sharedState = default;
        private readonly RewardPopupFactory _rewardPopupFactory = default;
        private readonly BloodAltarUiBundle _ui = default;
        
        private readonly EcsFilter<BloodAltarRaceAnimation> _bloodAltarRaceAnimation = default;
        private readonly EcsFilter<CachedBloodDrainData> _cache = default;

        private int _targetStep;
        private float _rulerTargetPosition;
        private bool _animateRuler;

        private int _scrollViewOffset;
        private int _initialBlood;
        private EcsEntity _bloodFillSound;
        private int[] _cachedUnlocks;
        private CancellationToken _cancellationToken;

        public async UniTask InitAsync(CancellationToken cancellationToken)
        {
            _cancellationToken = cancellationToken;
            _scrollViewOffset = -BloodProgressItemView.ITEM_HEIGHT * 3 + BloodProgressItemView.ITEM_HEIGHT / 2;
            BloodProgressItemModel currentModel = _sharedState.Current.Model;

            if (_playerManager.Session.Inventory.BloodBalance.Amount >= currentModel.Price)
            {
                await BloodDrainRequest(_cancellationToken);
                _initialBlood = 0;
            }
            else if (!_cache.IsEmpty())
            {
                _initialBlood = _cache.Get1(0).LastBloodBalance;
            }
            else
            {
                _initialBlood = _playerManager.Session.Inventory.BloodBalance.Amount;
            }

            if (!_bloodAltarRaceAnimation.IsEmpty())
            {
                _bloodAltarRaceAnimation.Get1(0).Type = AltarRaceAnimationType.End;
            }

            RealignRuler(true);
            _ui.Ruler.Init(currentModel.Price, currentModel.TotalShards);
            UpdateBloodProgress();
        }

        public void Run()
        {
            float timeInterval = Time.deltaTime;
            AnimateRuler(timeInterval);
        }

        private void AnimateRuler(float interval)
        {
            if (!_animateRuler)
            {
                return;
            }

            int cursorPosition = -(int) (_ui.Ruler.Transform.anchoredPosition.y - _RULER_SPEED_ * interval);

            if (Mathf.Abs(cursorPosition - _rulerTargetPosition) <= _RULER_SPEED_ * interval)
            {
                _animateRuler = false;
                UpdateBloodProgress();

                return;
            }

            UpdateScrollView(cursorPosition);
        }

        private void RealignRuler(bool instant = false)
        {
            int cursorPosition = _sharedState.CurrentStep * BloodProgressItemView.ITEM_HEIGHT + BloodProgressItemView.SELECTED_ITEM_HEIGHT / 2;

            if (instant)
            {
                UpdateScrollView(cursorPosition);
            }
            else
            {
                _rulerTargetPosition = cursorPosition;
                _animateRuler = true;
            }
        }

        private void UpdateBloodProgress()
        {
            _targetStep = _playerManager.Session.Progress.BloodProgress;

            if (CheckUnlockCache() && _targetStep >= _sharedState.Items.Count)
            {
                UnlockLastItem();
            }
            else if (_targetStep > _sharedState.CurrentStep && _targetStep < _sharedState.Items.Count)
            {
                _initialBlood = 0;
                BloodDrainRequest(_cancellationToken).Forget();
                ChangeBloodStep();
            }
            else
            {
                AnimateItemProgress();
            }
        }

        private bool CheckUnlockCache()
        {
            if (_cache.IsEmpty())
            {
                return false;
            }

            foreach (int index in _cache)
            {
                _cachedUnlocks = _cache.Get1(index).Rewards;
                _cache.GetEntity(index).Del<CachedBloodDrainData>();

                return _cachedUnlocks != null && _cachedUnlocks.Length > 0;
            }

            return false;
        }

        private void UnlockLastItem()
        {
            _ui.Ruler.FillToEnd(_initialBlood);
            _initialBlood = 0;
            _ui.Ruler.OnAnimationComplete += ShowRewardScreen;
        }

        private void AnimateItemProgress()
        {
            int targetTotal = _playerManager.Session.Inventory.BloodBalance.Amount;
            BloodProgressItemModel currentModel = _sharedState.Current.Model;
            _ui.Ruler.Init(currentModel.Price, currentModel.TotalShards);
            _ui.Ruler.FillTo(_initialBlood, targetTotal);

            if (_initialBlood != targetTotal)
            {
                _bloodFillSound = _world.NewEntity();
                _bloodFillSound.Get<StartSound>().Reference = _audio.BloodAltarFillItem;
                _ui.Ruler.OnAnimationComplete += OnRulerAnimationComplete;
            }
        }

        private void OnRulerAnimationComplete()
        {
            _bloodFillSound.Get<StopSound>();
            _ui.Ruler.OnAnimationComplete -= OnRulerAnimationComplete;
        }

        private void ChangeBloodStep()
        {
            if (_sharedState.CurrentStep >= _targetStep)
            {
                return;
            }

            _sharedState.Current.Activate();
            _world.NewEntity().Get<SoundPoint>().SoundAssetPath = _audio.BloodAltarReward;

            if (_sharedState.CurrentStep < _sharedState.Items.Count - 2)
            {
                _sharedState.Current.TransitTo(ViewStates.Unlocked);
                _sharedState.CurrentStep++;
                _sharedState.Current.TransitTo(ViewStates.Selected);
                RealignRuler();
            }
            else
            {
                //TODO: unlock next region
            }
        }

        private async void ShowRewardScreen()
        {
            _ui.Ruler.OnAnimationComplete -= ShowRewardScreen;

            RewardScreenConfig rewardScreenConfig = new()
            {
                RewardDescKey = LocalIdents.Localization.REWARD_TEXT_KEY,
                ButtonCaptionKey = LocalIdents.Localization.REWARD_BUTTON_KEY,
            };

            Result<ExternalOpenSackResponse> openSack = await _metaNet.OpenSack(_cachedUnlocks[0], _cancellationToken);
            ItemComponent rewardContent = openSack.Value.Item;
            DefaultRewardPresenter view = await _rewardPopupFactory.Open(rewardContent, rewardScreenConfig, cancellationToken: _cancellationToken);
            view.Closed += AnimateItemProgress;
        }


        private async UniTask BloodDrainRequest(CancellationToken cancellationToken)
        {
            _screenLocker.Lock(cancellationToken);

            try
            {
                await _metaNet.DrainBlood(cancellationToken);
            }
            finally
            {
                _screenLocker.Unlock(cancellationToken);
            }
        }

        private void UpdateScrollView(int cursorY)
        {
            cursorY = -cursorY;
            _ui.Ruler.Transform.anchoredPosition = _ui.Ruler.Transform.anchoredPosition.WithY(cursorY);

            if (cursorY > _scrollViewOffset)
            {
                _ui.ScrollView.verticalNormalizedPosition = 1;
            }
            else if (_sharedState.ContentHeight + cursorY < -(_scrollViewOffset + _ui.ScrollView.viewport.rect.height))
            {
                _ui.ScrollView.verticalNormalizedPosition = 0;
            }
            else
            {
                _ui.ScrollView.content.anchoredPosition = _ui.ScrollView.content.anchoredPosition.WithY(_scrollViewOffset - cursorY);
            }
        }
    }
}