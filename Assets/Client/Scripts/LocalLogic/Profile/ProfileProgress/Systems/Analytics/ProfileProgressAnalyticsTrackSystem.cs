using Client.Common.Analytics.Trackers.Abstract;
using Client.Common.NpcTab.Analytics;
using Client.Utils.ECS.LocalWorld;
using Leopotam.Ecs;

namespace Client.Profile.ProfileProgress.Systems.Analytics
{
    public class ProfileProgressAnalyticsTrackSystem : IEcsInitSystem, IEcsRunSystem
    {
        private readonly LocalEcsWorld _world = default;

        private IAnalyticTracker[] _trackers;

        public void Init()
        {
            _trackers = new IAnalyticTracker[]
            {
                new VisitViewAnalyticTracker<ProfileProgressScope>(_world, "bloody_altar"),
            };
        }

        public void Run()
        {
            foreach (IAnalyticTracker tracker in _trackers)
            {
                tracker.Track();
            }
        }
    }
}