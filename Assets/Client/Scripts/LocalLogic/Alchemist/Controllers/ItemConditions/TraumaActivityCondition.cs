using Client.Common.Network.MetaNet;
using Client.Common.Player.Timers;
using Client.Utils.Conditions;
using TimerType = Common.TimerType;

namespace Client.Alchemist.Controllers.ItemConditions
{
    public class TraumaActivityCondition : IPayloadedCondition<int>
    {
        private readonly PlayerTimers _playerTimers;
        private readonly bool _checkValue;

        public TraumaActivityCondition(PlayerTimers playerTimers, bool activeCheck)
        {
            _playerTimers = playerTimers;
            _checkValue = activeCheck;
        }
        
        public bool IsMet(int id)
        {
            bool activeTimer =  _playerTimers.TryGetTimer(id, TimerType.Trauma, out PlayerTimer boost)
                   && boost.GetRemainingUtcTimeInSeconds() > 0;

            return activeTimer == _checkValue;
        }
    }
}