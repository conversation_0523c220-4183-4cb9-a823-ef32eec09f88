using Client.Common.Network.MetaNet;
using Client.Common.Player.Timers;
using Client.Utils.Conditions;
using TimerType = Common.TimerType;

namespace Client.Alchemist.Controllers.ItemConditions
{
    public class BoostActivityCondition : IPayloadedCondition<int>
    {
        private readonly PlayerTimers _playerTimers;
        private readonly bool _checkValue;

        public BoostActivityCondition(PlayerTimers playerTimers, bool activeCheck)
        {
            _playerTimers = playerTimers;
            _checkValue = activeCheck;
        }
        
        public bool IsMet(int id)
        {
            bool activeTimer = _playerTimers.TryGetTimer(id, TimerType.Boost, out PlayerTimer boost)
                   && boost.GetRemainingUtcTimeInSeconds() > 0;

            return _checkValue == activeTimer;
        }
    }
}