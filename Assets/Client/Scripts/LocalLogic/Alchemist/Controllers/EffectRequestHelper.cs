using Client.Alchemist.Components.Requests;
using Client.Common.Configs.Components;
using Client.Common.Configs.Components.Items;
using Client.Common.Network.MetaNet;
using Client.Common.Player.Timers;
using Client.Common.UI.ScrollController.Components;
using Client.Utils.ECS.EcsTaskTools;
using Client.Utils.ECS.LocalWorld;
using Leopotam.Ecs;

namespace Client.Alchemist.Controllers
{
    public static class EffectRequestHelper
    {
        public static EcsTask CreateAddEffectRequest<TTabMarker>(LocalEcsWorld world, PlayerTimer timer)
            where TTabMarker : struct, IScrollableMarker
        {
            return CreateAddEffectRequest<TTabMarker>(world, timer.ItemId, timer.TimerType, (int) timer.Duration, (int) timer.EndTime);
        }

        public static EcsTask CreateAddEffectRequest<TTabMarker>(LocalEcsWorld world, int id, global::Common.TimerType timerType, int duration, int endTime)
            where TTabMarker : struct, IScrollableMarker
        {
            EcsEntity request = world.NewEntity();
            request.Get<AddEffectRequest>().Construct(duration, endTime);
            request.Get<TimerTypeData>().Type = timerType;
            return FillDefaultRequest<TTabMarker>(request, id);
        }

        public static EcsTask CreateRemoveEffectRequest<TTabMarker>(LocalEcsWorld world, int id)
            where TTabMarker : struct, IScrollableMarker
        {
            EcsEntity request = world.NewEntity();
            request.Get<RemoveEffectRequest>();
            return FillDefaultRequest<TTabMarker>(request, id);
        }

        private static EcsTask FillDefaultRequest<TTabMarker>(EcsEntity request, int id)
            where TTabMarker : struct, IScrollableMarker
        {
            request.Get<ItemIdData>().Id = id;
            request.Get<TTabMarker>();
            return request.Get<EcsTaskConfig>().Construct();
        }
    }
}