using Client.Alchemist.Components.Markers;
using Client.Alchemist.Controllers;
using Client.Common.Configs.Components.Items;
using Client.Common.NpcTab.Timer;
using Client.Common.UI.ScrollController.Components;
using Client.Utils.ECS.LocalWorld;
using Common;
using Leopotam.Ecs;

namespace Client.Alchemist.Systems
{
    public class ExpiredConfigCleanupSystem<TTabMarker>  : IEcsRunSystem
        where TTabMarker : struct, IScrollableMarker
    {
        private readonly LocalEcsWorld _world = default;
        
        private readonly EcsFilter<ItemIdData, ScrollIndex, TTabMarker, EffectMarker>.Exclude<CurrentItem> _effectConfigs;
        private readonly EcsFilter<TimerEndEvent> _timerEnds;
        
        public void Run()
        {
            foreach (int timerIndex in _timerEnds)
            {
                ref TimerEndEvent timerEndEvent = ref _timerEnds.Get1(timerIndex);
                if (timerEndEvent.Type != TimerType.Boost && timerEndEvent.Type != TimerType.Trauma)
                {
                    continue;
                }

                TryCleanupConfigs(timerEndEvent.ItemId);
            }
        }

        private void TryCleanupConfigs(int endedId)
        {
            foreach (int configIndex in _effectConfigs)
            {
                if (_effectConfigs.Get1(configIndex).Id != endedId)
                {
                    continue;
                }

                EffectRequestHelper.CreateRemoveEffectRequest<TTabMarker>(_world, endedId);
            }
        }
    }
}