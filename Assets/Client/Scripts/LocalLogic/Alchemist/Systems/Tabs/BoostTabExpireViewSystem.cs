using Client.Alchemist.Components.Markers;
using Client.Alchemist.Data;
using Client.Common.Components.Common;
using Client.Common.Configs.Components;
using Client.Common.Configs.Components.Items;
using Client.Common.CSV;
using Client.Common.Network.MetaNet;
using Client.Common.NpcTab.Components;
using Client.Common.NpcTab.Controllers;
using Client.Common.NpcTab.Data.Tabs;
using Client.Common.NpcTab.Data.TabTypes.Markers;
using Client.Common.Player.Controllers;
using Client.Common.Player.Timers;
using Client.Utils.GameLogger;
using Leopotam.Ecs;
using UnityEngine;
using TimerType = Common.TimerType;

namespace Client.Alchemist.Systems.Tabs
{
    public class BoostTabExpireViewSystem : IEcsInitSystem, IEcsRunSystem, IEcsDestroySystem
    {
        private const float _UPDATE_FREQUENCY_ = 1f;
        
        private readonly PlayerManager _playerManager = default;

        private readonly EcsFilter<ItemIdData, BoostMarker> _effects = default;
        private readonly EcsFilter<TabButtonComponent, BoostsTabMarker> _icons = default;
        
        private float _timer;
        
        public void Init()
        {
            foreach (int i in _icons)
            {
                _icons.Get1(i).ButtonContext.Controller.StateChanged += OnIconStateChanged;
            }
        }
        
        public void Destroy()
        {
            foreach (int i in _icons)
            {
                _icons.Get1(i).ButtonContext.Controller.StateChanged -= OnIconStateChanged;
            }
        }
        
        public void Run()
        {
            _timer -= Time.deltaTime;
            if (_timer > 0)
            {
                return;
            }
            
            ProcessUpdateViews();
            _timer = _UPDATE_FREQUENCY_;
        }

        private void ProcessUpdateViews()
        {
            if (_icons.IsEmpty())
            {
                GameLogger.LogWarning($"[Alchemist] There is no icon for boost tab in filter {_icons}");
                return;
            }

            TabButtonController buttonController = _icons.Get1(0).ButtonContext.Controller;
            EcsEntity entity = _icons.GetEntity(0);
            
            if (buttonController.State == ButtonState.Inactive && IsAnyEffectExpiring())
            {
                buttonController.SetInactiveButtonHighlight();
            }
        }

        private bool IsAnyEffectExpiring()
        {
            foreach (int i in _effects)
            {
                if (!_playerManager.Session.Timers.TryGetTimer(_effects.Get1(i).Id, TimerType.Boost, out PlayerTimer timer))
                {
                    continue;
                }

                if (timer.GetRemainingUtcTimeInSeconds() <= _UPDATE_FREQUENCY_)
                {
                    return true;
                }
            }

            return false;
        }

        private void OnIconStateChanged(ButtonState state)
        {
            ProcessUpdateViews();
        }
    }
}