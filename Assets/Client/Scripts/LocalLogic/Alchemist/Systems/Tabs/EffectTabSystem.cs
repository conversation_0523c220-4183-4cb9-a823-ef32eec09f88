using System.Collections.Generic;
using Client.Alchemist.Components.Markers;
using Client.Alchemist.Components.Requests;
using Client.Common.Configs.Components;
using Client.Common.Configs.Components.Items;
using Client.Common.Configs.Extensions;
using Client.Common.HintSystem.Components.Events;
using Client.Common.Network.MetaNet;
using Client.Common.NpcTab.Components;
using Client.Common.NpcTab.Controllers.ScrollHintControllers;
using Client.Common.NpcTab.Controllers.ScrollIndexProviders;
using Client.Common.NpcTab.Systems.Tabs;
using Client.Common.NpcTab.Views;
using Client.Common.UI.InputLockService;
using Client.Common.UI.ScrollController.Components;
using Client.Common.UI.ScrollController.UnityComponents;
using Client.Utils.ECS.EcsTaskTools;
using Client.Utils.Extensions.EcsExtensions;
using Client.Utils.GameLogger;
using Cysharp.Threading.Tasks;
using Leopotam.Ecs;
using TimerType = Common.TimerType;

namespace Client.Alchemist.Systems.Tabs
{
    internal class EffectTabSystem<TEffectMarker, TTabMarker, TStateSelector, TItemStateMachine, TTabStateMachine>
        : TabSystem<TEffectMarker, TTabMarker, TStateSelector, TItemStateMachine, TTabStateMachine>
        where TEffectMarker : struct
        where TTabMarker : struct, IScrollableMarker
        where TItemStateMachine : IEcsFeature
        where TTabStateMachine : IEcsFeature
    {
        private readonly FullScreenLocker _screenLocker = default;

        private readonly EcsFilter<ItemIdData, EcsTaskConfig, TimerTypeData, AddEffectRequest, TTabMarker> _addEffectRequests;
        private readonly EcsFilter<ItemIdData, EcsTaskConfig, RemoveEffectRequest, TTabMarker> _removeEffectRequests;
        private readonly EcsFilter<ViewComponent<ItemView>, TEffectMarker, NoItemMarker> _noItemIcons;
        private readonly EcsFilter<ItemIdData, TEffectMarker, ItemMarker> _items;

        protected override IScrollIndexProvider ScrollIndexProvider => new FirstIndexProvider();
        protected override float GetScrollOffset() => 256f;

        private bool _enabled;
        private ScrollHintForNextController _scrollHintController;

        protected override void OnInit()
        {
            base.OnInit();
            _scrollHintController = new ScrollHintForNextController(ScrolledItems.GetEntitiesArray(), InputContext.RightArrow.gameObject);
        }

        protected override void OnEnable()
        {
            base.OnEnable();

            _scrollHintController.ShowOneTime();
            _enabled = true;
        }

        protected override void OnDisable()
        {
            base.OnDisable();

            _scrollHintController.Stop();
            _enabled = false;
        }

        public override void Run()
        {
            ProcessAddRequests();
            ProcessRemoveRequests();
            
            base.Run();
        }

        private void ProcessAddRequests()
        {
            foreach (int index in _addEffectRequests)
            {
                EcsTask task = _addEffectRequests.Get2(index).Task;

                if (task.IsComplete)
                {
                    _addEffectRequests.GetEntity(index).Destroy();
                    continue;
                }

                if (task.Status == EcsTask.States.Processed)
                {
                    continue;
                }

                task.Status = EcsTask.States.Processed;

                AddEffectRequest requestData = _addEffectRequests.Get4(index);
                PerformAddEffect(task, _addEffectRequests.Get1(index).Id, _addEffectRequests.Get3(index).Type, requestData.Duration, requestData.EndTime).Forget();
            }
        }

        private void ProcessRemoveRequests()
        {
            foreach (int index in _removeEffectRequests)
            {
                EcsTask task = _removeEffectRequests.Get2(index).Task;

                if (task.IsComplete)
                {
                    _removeEffectRequests.GetEntity(index).Destroy();
                    continue;
                }

                if (task.Status == EcsTask.States.Processed)
                {
                    continue;
                }

                task.Status = EcsTask.States.Processed;

                PerformRemoveEffect(task, _removeEffectRequests.Get1(index).Id).Forget();
            }
        }

        private async UniTaskVoid PerformAddEffect(EcsTask task, int id, TimerType timerType, int duration, int endTime)
        {
            _screenLocker.Lock();

            EcsEntity request = World.NewEntity();
            request.Get<TEffectMarker>();
            await request.Get<GenerateEffectRequest>().Construct(id, timerType, duration, endTime).IsDone();

            TryDeleteNoItemIcon();

            foreach (int index in TabView)
            {
                EcsEntity entity = TabView.GetEntity(index);

                if (TryInitScroll(entity, TabView.Get1(index).View))
                {
                    continue;
                }
                
                AddItem(entity, id);
            }

            task.Status = EcsTask.States.Done;

            _screenLocker.Unlock();
        }

        private async UniTaskVoid PerformRemoveEffect(EcsTask task, int id)
        {
            _screenLocker.Lock();

            TryRemoveItem(id);

            if (ScrolledItems.IsEmpty())
            {
                foreach (int index in TabView)
                {
                    EcsEntity tabEntity = TabView.GetEntity(index);
                    TryDeleteScroll(tabEntity);
                }

                await TryGenerateNoItemIcon();
            }
            
            if (_enabled)
            {
                World.NewEntity().Get<SelectStateRequest<TStateSelector>>();
            }
            
            task.Status = EcsTask.States.Done;
            _screenLocker.Unlock();
        }

        private void AddItem(EcsEntity tabEntity, int id)
        {
            var newItems = new List<EcsEntity>();
            foreach (int index in _items)
            {
                if (_items.Get1(index).Id != id)
                {
                    continue;
                }

                newItems.Add(_items.GetEntity(index));

                break;
            }
            
            EcsScrollGenerator.AddScrollItems(tabEntity, newItems.ToArray());
        }
        
        private void TryRemoveItem(int id)
        {
            foreach (int index in ScrolledItems)
            {
                if (ScrolledItems.Get3(index).Id != id)
                {
                    continue;
                }
                
                foreach (int tabIndex in TabView)
                {
                    EcsScrollGenerator.RemoveScrollItems(TabView.GetEntity(tabIndex), ScrolledItems.Get2(index).Index);
                }

                break;
            }
        }

        private bool TryInitScroll(EcsEntity tabEntity, ContainerView containerView)
        {
            if (!tabEntity.Has<ScrollControllerComponent>())
            {
                InitScroll(tabEntity, containerView.ScrollContainer);
                return true;
            }

            return false;
        }

        private void TryDeleteScroll(EcsEntity tabEntity)
        {
            if (tabEntity.Has<ScrollControllerComponent>())
            {
                tabEntity.Get<ScrollControllerComponent>().ScrollController.Dispose();
                tabEntity.Del<ScrollControllerComponent>();
            }
        }

        private async UniTask TryGenerateNoItemIcon()
        {
            if (!_noItemIcons.IsEmpty())
            {
                return;
            }

            EcsEntity request = World.NewEntity();
            request.Get<TEffectMarker>();
            await request.Get<GenerateNoEffectIconRequest>().Construct().IsDone();
        }

        private void TryDeleteNoItemIcon()
        {
            foreach (int index in _noItemIcons)
            {
                EcsEntity iconConfig = _noItemIcons.GetEntity(index);
                iconConfig.TryDestroyConfigViews();
                iconConfig.Destroy();

                if (_enabled)
                {
                    World.NewEntity().Get<SelectStateRequest<TStateSelector>>();
                }
            }
        }

        public override void Destroy()
        {
            BaseCts?.Dispose();
        }
    }
}