using System;
using Client.Alchemist.Components.Markers;
using Client.Alchemist.Data;
using Client.Alchemist.Systems.PopupStates.Boosts;
using Client.Alchemist.Systems.PopupStates.Traumas;
using Client.Alchemist.Systems.Tabs;
using Client.Alchemist.UnityComponents;
using Client.Common.Ftue.Infrastructure;
using Client.Common.Network.MetaNet;
using Client.Common.NpcTab.Controllers.Generators;
using Client.Common.NpcTab.Data.Tabs;
using Client.Common.NpcTab.Data.TabTypes;
using Client.Common.NpcTab.Data.TabTypes.Markers;
using Client.Common.NpcTab.Systems.Tabs;
using Client.Common.Visual;
using Client.Utils.ECS.LocalWorld;
using Common;
using Leopotam.Ecs;
using TimerType = Client.Common.Network.MetaNet.TimerType;

namespace Client.Alchemist.Systems
{
    public class AlchemistViewInitSystem : IEcsInitSystem
    {
        private readonly LocalEcsWorld _world = default;
        private readonly TabSwitcher _tabSwitcher = default;
        private readonly GlobalVisual _globalVisual = default;
        private readonly FtueProgress _ftueProgress = default;
        
        private readonly AlchemistTabView _view;
        
        public AlchemistViewInitSystem(AlchemistTabView view)
        {
            _view = view;
        }
        
        public void Init()
        {
            ButtonColors buttonColors = new(_globalVisual.UiActivePageIconColor, _globalVisual.UiUnlockedPageIconColor, _globalVisual.UiLockedPageIconColor);
            
            Type traumaTabSystemType = typeof(EffectTabSystem<TraumaMarker, TraumasTabMarker, TraumaStateSelectorSystem, TraumasStateMachine, TabStateMachine>);
            Type boostTabSystemType = typeof(EffectTabSystem<BoostMarker, BoostsTabMarker, BoostStateSelectorSystem, BoostsStateMachine, TabStateMachine>);
            
            TabButtonContext bottlesButtonContext = new(new TabButtonController(_view.BottlesMenuButton, false, buttonColors), typeof(BottlesTabSystem));
            TabButtonContext traumasButtonContext = new(new TabButtonController(_view.TraumasMenuButton, 
                                                                                !_ftueProgress.IsCompleted(AccountStep.AlchemistCraftTutorialFinished), 
                                                                                buttonColors), 
                                                       traumaTabSystemType);
            TabButtonContext boostsButtonContext = new(new TabButtonController(_view.BoostsMenuButton, 
                                                                               !_ftueProgress.IsCompleted(AccountStep.AlchemistCraftTutorialFinished), 
                                                                               buttonColors),
                                                       boostTabSystemType);
            
            _tabSwitcher
                  .AddButton((int) AlchemistTabType.Bottles, bottlesButtonContext)
                  .AddButton((int) AlchemistTabType.Traumas, traumasButtonContext)
                  .AddButton((int) AlchemistTabType.Boosts, boostsButtonContext);
            
            new TabButtonComponentGenerator(_world)
                .Generate<BottlesTabMarker>(bottlesButtonContext)
                .Generate<TraumasTabMarker>(traumasButtonContext, global::Common.TimerType.Trauma)
                .Generate<BoostsTabMarker>(boostsButtonContext, global::Common.TimerType.Boost)
                ;
        }
    }
}