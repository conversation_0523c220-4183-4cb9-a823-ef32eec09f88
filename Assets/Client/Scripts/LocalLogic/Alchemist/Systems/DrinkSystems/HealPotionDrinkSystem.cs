using System;
using System.Threading;
using Client.Alchemist.Components;
using Client.Alchemist.Components.Markers;
using Client.Common.Configs.Components.Items;
using Client.Common.Network.MetaNet;
using Client.Common.NpcTab.Craft;
using Client.Common.Player.Controllers;
using Client.Common.Traumas;
using Client.Common.UI.InputLockService;
using Client.Utils.ECS.EcsTaskTools;
using Client.Utils.ECS.LocalWorld;
using Client.Utils.FireBase;
using Client.Utils.GameLogger;
using Common;
using Cysharp.Threading.Tasks;
using Leopotam.Ecs;

namespace Client.Alchemist.Systems.DrinkSystems
{
    public class HealPotionDrinkSystem  : IEcsInitSystem, IEcsRunSystem
    {
        private readonly LocalEcsWorld _world = default;
        private readonly MetaNet _metaNet = default;
        private readonly FullScreenLocker _screenLocker;
        private readonly PlayerManager _playerManager;
        private readonly CancellationToken _cancellationToken = default;

        private readonly EcsFilter<DrinkBottleEvent, ItemIdData, EcsTaskConfig, TraumaMarker> _drinkEvents;
        
        private HealTraumaHelper _healTraumaHelper;
        
        public void Init()
        {
            _healTraumaHelper = new HealTraumaHelper(_metaNet, _world, _playerManager.Session.Timers);
        }
        
        public void Run()
        {
            foreach (int index in _drinkEvents)
            {
                EcsTask task = _drinkEvents.Get3(index).Task;

                if (task == null)
                {
                    try
                    {
                        throw new CrashlyticsException("HealPotionDrinkSystem Run task in null");
                    }
                    catch (Exception e)
                    {
                        GameLogger.LogErrorException(e.Message, "HealPotionDrinkSystemRunTask");
                        continue;
                    }
                }

                if (task.IsComplete)
                {
                    _drinkEvents.GetEntity(index).Destroy();
                    continue;
                }

                if (task.Status == EcsTask.States.Processed)
                {
                    continue;
                }

                task.Status = EcsTask.States.Processed;
                DrinkBottle(task, _drinkEvents.Get1(index).ItemId, _drinkEvents.Get2(index).Id).Forget();
            }
        }

        private async UniTaskVoid DrinkBottle(EcsTask task, int itemId, int traumaId)
        {
            _screenLocker.Lock(_cancellationToken);

            await _healTraumaHelper.HealForBottle(traumaId, _cancellationToken);

            EcsEntity itemUsedConfig = _healTraumaHelper.UseItem(itemId);
            itemUsedConfig.Get<TraumaMarker>();

            _screenLocker.Unlock(_cancellationToken);
            task.Status = EcsTask.States.Done;
        }
    }
}