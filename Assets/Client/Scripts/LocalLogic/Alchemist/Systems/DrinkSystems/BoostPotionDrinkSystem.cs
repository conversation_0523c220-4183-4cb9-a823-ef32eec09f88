using System.Threading;
using Client.Alchemist.Components;
using Client.Alchemist.Components.Markers;
using Client.Common.Configs.Components.Items;
using Client.Common.Network.MetaNet;
using Client.Common.NpcTab.Craft;
using Client.Common.Player.Controllers;
using Client.Common.Player.Timers;
using Client.Common.UI.InputLockService;
using Client.Utils.ECS.EcsTaskTools;
using Client.Utils.ECS.LocalWorld;
using Client.Utils.ResultTool.Results;
using Common;
using Cysharp.Threading.Tasks;
using External;
using Leopotam.Ecs;

namespace Client.Alchemist.Systems.DrinkSystems
{
    public class BoostPotionDrinkSystem : IEcsRunSystem
    {
        private readonly LocalEcsWorld _world = default;
        private readonly MetaNet _metaNet = default;
        private readonly FullScreenLocker _screenLocker = default;
        private readonly PlayerManager _playerManager = default;
        private readonly CancellationToken _cancellationToken = default;

        private readonly EcsFilter<DrinkBottleEvent, EcsTaskConfig, BoostMarker> _drinkEvents;
        
        public void Run()
        {
            foreach (int index in _drinkEvents)
            {
                EcsTask task = _drinkEvents.Get2(index).Task;

                if (task.IsComplete)
                {
                    _drinkEvents.GetEntity(index).Destroy();
                    continue;
                }

                if (task.Status == EcsTask.States.Processed)
                {
                    continue;
                }

                task.Status = EcsTask.States.Processed;
                DrinkBottle(task, _drinkEvents.Get1(index).ItemId).Forget();
            }
        }

        private async UniTaskVoid DrinkBottle(EcsTask task, int itemId)
        {
            _screenLocker.Lock(_cancellationToken);

            Result<ExternalDrinkPotionResponse> drinkTask = await _metaNet.DrinkPotion(itemId, _cancellationToken);
            if (drinkTask.IsFailure)
            {
                task.Status = EcsTask.States.Cancelled;
                return;
            }
            
            _playerManager.Session.Timers.ChangeTimer(drinkTask.Value.Timer);

            EcsEntity itemUsedConfig = _world.NewEntity();
            itemUsedConfig.Get<ItemUsedEvent>() = new ItemUsedEvent().Construct(itemId, "Boost");
            itemUsedConfig.Get<ItemTypeData>().ItemType = ItemType.Bottle;

            _screenLocker.Unlock(_cancellationToken);
            task.Status = EcsTask.States.Done;
        }
    }
}