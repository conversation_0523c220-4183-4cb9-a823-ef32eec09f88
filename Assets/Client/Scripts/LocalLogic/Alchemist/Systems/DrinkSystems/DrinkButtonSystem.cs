using System.Collections.Generic;
using System.Threading;
using Client.Alchemist.Controllers;
using Client.Alchemist.Data;
using Client.Common.Configs.Components;
using Client.Common.Configs.Components.Items;
using Client.Common.CSV;
using Client.Common.Network.MetaNet;
using Client.Common.NpcTab.Components;
using Client.Common.NpcTab.Controllers;
using Client.Common.NpcTab.Data.TabTypes.Markers;
using Client.Common.NpcTab.ItemCounts;
using Client.Common.NpcTab.Timer;
using Client.Common.NpcTab.Views;
using Client.Common.Player.Controllers;
using Client.Common.Player.Timers;
using Client.Common.UI.Buttons;
using Client.Common.UI.Labels;
using Client.Common.UI.ScrollController;
using Client.Common.UI.ScrollController.Components;
using Client.Utils.Conditions;
using Client.Utils.ECS.EcsTaskTools;
using Client.Utils.ECS.LocalWorld;
using Client.Utils.Extensions;
using Client.Utils.ViewService;
using Common;
using Cysharp.Threading.Tasks;
using Leopotam.Ecs;
using Leopotam.Localization;
using TimerType = Client.Common.Network.MetaNet.TimerType;

namespace Client.Alchemist.Systems.DrinkSystems
{
    public class DrinkButtonSystem : IEcsInitSystem, IEcsDestroySystem, IEcsRunSystem
    {
        private readonly LocalEcsWorld _world = default;
        private readonly PlayerManager _playerManager = default;
        private readonly CsvLocalization _localization = default;
        private readonly PopupViewService _viewService = default;
        private readonly GoogleDocsData _googleDocs = default;
        
        private readonly EcsFilter<ItemIdData, ItemTypeData, HolderComponent<IPayloadedCondition<int>>, CurrentItem> _currentItem;
        private readonly EcsFilter<ScrollControllerComponent, BottlesTabMarker> _bottlesTab;
        private readonly EcsFilter<DrinkButtonUpdateEvent> _drinkUpdateEvent;
        
        private readonly ActionButton _drinkButton;

        private readonly CancellationTokenSource _cts;

        public DrinkButtonSystem(ActionButton drinkButton)
        {
            _drinkButton = drinkButton;
            _cts = new CancellationTokenSource();
        }
        
        public void Init()
        {
            _drinkButton.Clicked += (() => DrinkBottle().Forget());

            foreach (int index in _bottlesTab)
            {
                IScrollController scrollController = _bottlesTab.Get1(index).ScrollController;
                scrollController.ScrollStarted += HideButtonView;
                scrollController.ScrollEnded += UpdateButtonView;
            }
        }

        public void Run()
        {
            if (_currentItem.IsEmpty() && _drinkButton.gameObject.activeInHierarchy)
            {
                _drinkButton.Hide();
                return;
            }
            
            if (_drinkUpdateEvent.IsEmpty())
            {
                return;
            }

            UpdateButtonView();
        }
        
        public void Destroy()
        {
            foreach (int index in _bottlesTab)
            {
                IScrollController scrollController = _bottlesTab.Get1(index).ScrollController;
                scrollController.ScrollStarted -= HideButtonView;
                scrollController.ScrollEnded -= UpdateButtonView;
            }
            
            _cts?.CancelAndDispose();
        }

        private async UniTask DrinkBottle()
        {
            PlayerTimers.Query boostsQuery = PlayerTimers.Query.Get().WithTimerType(global::Common.TimerType.Boost);
            List<PlayerTimer> boostsTimers = _playerManager.Session.Timers.GetTimers(boostsQuery);
            int activeBoostsCount = boostsTimers.Count;
            int maxConcurrentBoosts = (int)_googleDocs.GetNumber("srv.bottle.maxConcurrentCount");
            
            if (activeBoostsCount < maxConcurrentBoosts)
            {
                PerformDrink().Forget();
                return;
            }
            
            bool confirmPopup = await TryConfirmDrink();
            if (confirmPopup)
            {
                PerformDrink().Forget();
            }
        }

        private async UniTask<bool> TryConfirmDrink()
        {
            YesNoPopupPresenter confirmPopupPresenter = await CreateConfirmPopup();

            bool isConfirmed = await confirmPopupPresenter.Confirm();
            _viewService.CloseWithDestroy(confirmPopupPresenter).Forget();

            return isConfirmed;
        }
        
        private async UniTask<YesNoPopupPresenter> CreateConfirmPopup()
        {
            YesNoPopupModel model = new(
                _localization.Get(LocalIdents.Localization.DRINK_OVER_MAX_BOOST_WARNING),
                _localization.Get(LocalIdents.Localization.DRINK_OVER_MAX_BOOST_OKAY),
                _localization.Get(LocalIdents.Localization.DRINK_OVER_MAX_BOOST_CANCEL));

            return await _viewService.Open<YesNoPopupPresenter, YesNoPopupView, YesNoPopupModel>(model, cancellationToken: _cts.Token);
        }

        private async UniTaskVoid PerformDrink()
        {
            int itemId = _currentItem.Get1(0).Id;
            await DrinkHelper.CreateBoostDrinkEvent(_world, itemId).IsDone();
            PlayerTimer newTimer = _playerManager.Session.Timers.GetTimer(itemId, global::Common.TimerType.Boost);
            
            await EffectRequestHelper.CreateAddEffectRequest<BoostsTabMarker>(_world, newTimer).IsDone();
            UpdateButtonView();
        }

        private void UpdateButtonView(int index)
        {
            UpdateButtonView();
        }
        
        private void HideButtonView(int index)
        {
            _drinkButton.Hide();
        }
        
        private void UpdateButtonView()
        {
            if (_currentItem.IsEmpty())
            {
                _drinkButton.Hide();
                return;
            }
            
            if (_currentItem.Get2(0).ItemType != ItemType.Bottle)
            {
                _drinkButton.Hide();
                return;
            }

            int itemId = _currentItem.Get1(0).Id;
            HolderComponent<IPayloadedCondition<int>> drinkUnavailableConditions = _currentItem.Get3(0);

            foreach (IPayloadedCondition<int> condition in drinkUnavailableConditions.Items)
            {
                if (!condition.IsMet(itemId))
                {
                    _drinkButton.Hide();
                    return;
                }
            } 

            _drinkButton.Show();
        }
    }
}