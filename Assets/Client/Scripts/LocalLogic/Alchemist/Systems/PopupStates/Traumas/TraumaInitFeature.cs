using Client.Alchemist.Components.Markers;
using Client.Alchemist.Data;
using Client.Alchemist.Systems.Generators;
using Client.Common.Network.MetaNet;
using Client.Common.NpcTab.Data.TabTypes.Markers;
using Client.Common.NpcTab.Data.UI;
using Client.Common.NpcTab.Systems.Generators;
using Client.Utils.Extensions.EcsExtensions;
using Leopotam.Ecs;

namespace Client.Alchemist.Systems.PopupStates.Traumas
{
    public class TraumaInitFeature : IEcsFeature
    {
        private readonly TabAnchorsBundle _anchorsBundle;
        
        public TraumaInitFeature(TabAnchorsBundle anchorsBundle)
        {
            _anchorsBundle = anchorsBundle;
        }
        public void Build(EcsSystems systems)
        {
            systems
                .Add(new TabConfigGeneratorSystem<TraumasTabMarker>(_anchorsBundle.Center))
                .Add(new EffectConfigGenerator<TraumaMarker>(global::Common.TimerType.Trauma))
                .Add(
                    new NoEffectIconConfigGenerator<TraumaMarker>(
                        TimerType.Trauma, LocalIdents.Paths.NO_ITEM_ICON, LocalIdents.Localization.NO_ITEMS_NAME_KEY_MASK,
                        LocalIdents.Localization.NO_ITEMS_DESCRIPTION_KEY_MASK))
                .Add(new ExpiredConfigCleanupSystem<TraumasTabMarker>());
        }
    }
}