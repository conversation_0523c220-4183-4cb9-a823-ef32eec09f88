using Client.Alchemist.Controllers;
using Client.Alchemist.Data;
using Client.Common.Configs.Components.Items;
using Client.Common.Items;
using Client.Common.Network.MetaNet;
using Client.Common.NpcTab.Components;
using Client.Common.NpcTab.Data.TabTypes.Markers;
using Client.Common.NpcTab.Views;
using Client.Common.Player.Controllers;
using Client.Common.Player.Timers;
using Client.Common.Traumas.Data;
using Client.Common.Traumas.Views;
using Client.Common.UI;
using Client.Common.UI.Buttons.Pay;
using Client.Utils.Extensions.EcsExtensions;
using Cysharp.Threading.Tasks;
using Leopotam.Ecs;
using UnityEngine;
using TimerType = Common.TimerType;

namespace Client.Alchemist.Systems.PopupStates.Traumas
{
    internal class ActiveTraumaStateSystem<TEffectMarker, TStateMachine> : ActiveEffectStateSystem<TEffectMarker, TStateMachine, TraumasTabMarker>
        where TEffectMarker : struct
        where TStateMachine : IEcsFeature
    {
        private const int _BOTTLE_ID_ = PlayerInventory.Items.BOTTLE_FOR_HEAL;
        private readonly ItemData _itemData = default;
        private readonly TraumasDurationCache _durationCache = default;
        private readonly EcsFilter<TabButtonComponent, BottlesTabMarker> _bottlesTabButton = default;

        protected override ThePayButton CompleteButton { get; }
        protected override SpeedUpButtonView SpeedUpButton { get; }
        
        private int _reduceSeconds;

        public ActiveTraumaStateSystem(TraumasTabBundle traumasTabBundle)
        {
            CompleteButton = traumasTabBundle.CompleteButton;
            SpeedUpButton = traumasTabBundle.SpeedUpButton;
        }

        protected override void OnEnableInternal()
        {
            int currentTraumaId = CurrentConfig.Get<ItemIdData>().Id;
            _reduceSeconds = (int)GoogleDocsData.GetNumber(string.Format(LocalIdents.Keys.EFFECT_CHANGE_BY_PROMO, currentTraumaId));
            base.OnEnableInternal();
        }

        protected override async UniTaskVoid InitExtendButtonsVisual()
        {
            string healTitle = Localization.Get(LocalIdents.Localization.HEAL_TRAUMA_TITLE);
            string healSubtext = Localization.Get(LocalIdents.Localization.HEAL_TRAUMA_SUBTEXT);
            ClientItemData itemData = _itemData.GetItemDataById(_BOTTLE_ID_);

            CompleteButton.Show();
            CompleteButton.SetText(healTitle);
            CompleteButton.SetSubText(string.Format(healSubtext, UiHelpers.FormatTime(Mathf.Abs(itemData.Content[0].Max), Localization)));
            GameObject bottleIconAsset = await ResourceLoadingService.FromResources(string.Format(ItemIdents.Paths.ITEM_PATH, _BOTTLE_ID_)).LoadUniAsync<GameObject>();
            CompleteButton.SetPrice(bottleIconAsset, 1);

            string reduceSubtext = Localization.Get(LocalIdents.Localization.REDUCE_TRAUMA_SUBTEXT);
            SpeedUpButton.Show();
            SpeedUpButton.SetCaption(Localization.Get(LocalIdents.Localization.REDUCE_TRAUMA_TITLE));
            SpeedUpButton.SetSubText(string.Format(reduceSubtext, UiHelpers.FormatTime(Mathf.Abs(_reduceSeconds), Localization)));
        }

        protected override async UniTaskVoid PerformExtendByPromo()
        {
            if (!TryGetCurrentTimer(out PlayerTimer timer))
            {
                return;
            }

            FullScreenLocker.Lock();
            
            await ChangeEffectTime(timer, 1000 * _reduceSeconds);
            TraumaTimerChange(timer.ItemId);
            
            FullScreenLocker.Unlock();
        }

        protected override string GetPromoOperationType() => "reduce_trauma_effect";

        protected override async UniTaskVoid PerformExtendByBottle()
        {
            if (!TryGetCurrentTimer(out PlayerTimer timer))
            {
                return;
            }

            if (!TryExtend(_BOTTLE_ID_))
            {
                _bottlesTabButton.Get1(0).ButtonContext.Controller.SetInactiveButtonHighlight();
                return;
            }

            FullScreenLocker.Lock();
            
            await DrinkHelper.CreateHealDrinkEvent(World, _BOTTLE_ID_, timer.ItemId).IsDone();
            await TraumaTimerChange(timer.ItemId);
            
            FullScreenLocker.Unlock();
        }

        protected override async UniTask OnTimerEnd()
        {
            FullScreenLocker.Lock();
            
            TraumaTimerView view = (TraumaTimerView)CurrentEffect.Get1(0).View;
            
            await view.PlayHeal();
            await base.OnTimerEnd();
            
            FullScreenLocker.Unlock();
        }

        private UniTask TraumaTimerChange(int timerId)
        {
            if (!PlayerManager.Session.Timers.TryGetTimer(timerId, TimerType.Trauma, out PlayerTimer updatedTimer))
            {
                return UniTask.CompletedTask;
            }
            return UpdateEffectView(updatedTimer);
        }

        private UniTask UpdateEffectView(PlayerTimer updatedTimer)
        {
            EffectTimerView effectView = CurrentEffect.Get1(0).View;
            effectView.SetTime((int) _durationCache[updatedTimer.ItemId], (int) updatedTimer.EndTime);
            effectView.UpdateTimer(1.0f*updatedTimer.GetRemainingUtcTimeInSeconds() / _durationCache[updatedTimer.ItemId]);
            TraumaTimerView view = effectView as TraumaTimerView;
            if (view != null)
            {
                return view.PlayTimerReduced();
            }
            return UniTask.CompletedTask;
        }
    }
}