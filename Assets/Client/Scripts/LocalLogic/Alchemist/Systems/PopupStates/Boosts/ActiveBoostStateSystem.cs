using Client.Alchemist.Controllers;
using Client.Alchemist.Data;
using Client.Common.Configs.Components.Items;
using Client.Common.Items;
using Client.Common.Network.MetaNet;
using Client.Common.NpcTab.Data.TabTypes.Markers;
using Client.Common.NpcTab.Views;
using Client.Common.Player.Timers;
using Client.Common.Traumas.Views;
using Client.Common.UI;
using Client.Common.UI.Buttons.Pay;
using Client.Utils.Extensions.EcsExtensions;
using Cysharp.Threading.Tasks;
using Leopotam.Ecs;
using UnityEngine;
using TimerType = Common.TimerType;

namespace Client.Alchemist.Systems.PopupStates.Boosts
{
    internal class ActiveBoostStateSystem<TEffectMarker, TStateMachine> : ActiveEffectStateSystem<TEffectMarker, TStateMachine, BoostsTabMarker>
        where TEffectMarker : struct
        where TStateMachine : IEcsFeature
    {
        protected override ThePayButton CompleteButton { get; }
        protected override SpeedUpButtonView SpeedUpButton { get; }
        
        private int _currentId;
        private int _extendSeconds;

        public ActiveBoostStateSystem(BoostsTabBundle boostsTabBundle)
        {
            CompleteButton = boostsTabBundle.CompleteButton;
            SpeedUpButton = boostsTabBundle.SpeedUpButton;
        }

        protected override void OnEnableInternal()
        {
            _currentId = CurrentConfig.Get<ItemIdData>().Id;
            _extendSeconds = (int) GoogleDocsData.GetNumber(string.Format(LocalIdents.Keys.EFFECT_CHANGE_BY_PROMO, _currentId));

            base.OnEnableInternal();
        }

        protected override async UniTaskVoid InitExtendButtonsVisual()
        {
            int itemId = CurrentConfig.Get<ItemIdData>().Id;
            string extendTitle = Localization.Get(LocalIdents.Localization.EXTEND_EFFECT_TITLE);
            
            CompleteButton.Show();
            CompleteButton.SetText(extendTitle);
            CompleteButton.SetSubText(Localization.Get(LocalIdents.Localization.EXTEND_EFFECT_SUBTEXT));
            GameObject bottleIconAsset = await ResourceLoadingService.FromResources(string.Format(ItemIdents.Paths.ITEM_PATH, itemId)).LoadUniAsync<GameObject>();
            CompleteButton.SetPrice(bottleIconAsset, 1);

            string extendSubtextMask = Localization.Get(LocalIdents.Localization.EXTEND_EFFECT_SUBTEXT_TIME_MASK);
            SpeedUpButton.Show();
            SpeedUpButton.SetCaption(extendTitle);
            SpeedUpButton.SetSubText(string.Format(extendSubtextMask, UiHelpers.FormatTime(_extendSeconds, Localization)));
        }

        protected override async UniTaskVoid PerformExtendByPromo()
        {
            if (!TryGetCurrentTimer(out PlayerTimer timer))
            {
                return;
            }
            
            FullScreenLocker.Lock();
            
            await ChangeEffectTime(timer, 1000 * _extendSeconds);
            
            BoostTimerChange(timer.ItemId);
            
            FullScreenLocker.Unlock();
        }

        protected override string GetPromoOperationType() => "prolong_boost_effect";

        protected override async UniTaskVoid PerformExtendByBottle()
        {
            if (!TryGetCurrentTimer(out PlayerTimer timer))
            {
                return;
            }

            if (!TryExtend(timer.ItemId))
            {
                return;
            }
            
            FullScreenLocker.Lock();
            
            await DrinkHelper.CreateBoostDrinkEvent(World, timer.ItemId).IsDone();
            
            BoostTimerChange(timer.ItemId);
            
            FullScreenLocker.Unlock();
        }

        private void BoostTimerChange(int timerId)
        {
            if (!PlayerManager.Session.Timers.TryGetTimer(timerId, TimerType.Boost, out PlayerTimer updatedTimer))
            {
                return;
            }
            UpdateEffectView(updatedTimer);
        }

        private void UpdateEffectView(PlayerTimer updatedTimer)
        {
            EffectTimerView effectView = CurrentEffect.Get1(0).View;
            effectView.SetTime((int) updatedTimer.Duration, (int) updatedTimer.EndTime);
            effectView.UpdateTimer(updatedTimer.GetProgressRatio());
        }
    }
}