using Client.Alchemist.Components.Markers;
using Client.Alchemist.Data;
using Client.Alchemist.Systems.Generators;
using Client.Common.Items;
using Client.Common.Network.MetaNet;
using Client.Common.NpcTab.Controllers;
using Client.Common.NpcTab.Data.TabTypes.Markers;
using Client.Common.NpcTab.Data.UI;
using Client.Common.NpcTab.Systems.Generators;
using Client.Utils.Extensions.EcsExtensions;
using Leopotam.Ecs;

namespace Client.Alchemist.Systems.PopupStates.Boosts
{
    public class BoostInitFeature : IEcsFeature
    {
        private readonly TabAnchorsBundle _anchorsBundle;

        public BoostInitFeature(TabAnchorsBundle anchorsBundle)
        {
            _anchorsBundle = anchorsBundle;
        }
        
        public void Build(EcsSystems systems)
        {
            systems
                .Add(new TabConfigGeneratorSystem<BoostsTabMarker>(_anchorsBundle.Center))
                .Add(new EffectConfigGenerator<BoostMarker>(global::Common.TimerType.Boost))
                .Add(new NoEffectIconConfigGenerator<BoostMarker>(
                         TimerType.Boost, LocalIdents.Paths.NO_ITEM_ICON, LocalIdents.Localization.NO_ITEMS_NAME_KEY_MASK,
                         LocalIdents.Localization.NO_ITEMS_DESCRIPTION_KEY_MASK))
                .Add(new ExpiredConfigCleanupSystem<BoostsTabMarker>());
        }
    }
}