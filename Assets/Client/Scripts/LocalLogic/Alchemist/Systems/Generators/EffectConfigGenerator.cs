using System.Collections.Generic;
using System.Threading;
using Client.Alchemist.Components.Markers;
using Client.Alchemist.Components.Requests;
using Client.Alchemist.Data;
using Client.Alchemist.Systems.Generators.Scroll.Effects;
using Client.Alchemist.UnityComponents;
using Client.Common.Configs.Components;
using Client.Common.Configs.Components.Items;
using Client.Common.Ftue.Helpers;
using Client.Common.Ftue.Infrastructure;
using Client.Common.NpcTab.Controllers.Generators;
using Client.Common.Player.Controllers;
using Client.Common.Player.Timers;
using Client.Common.TimeGiver.Implementations;
using Client.Common.Traumas.Data;
using Client.Common.Traumas.Views;
using Client.Utils.ECS.ECSAsyncInitSystem;
using Client.Utils.ECS.EcsTaskTools;
using Client.Utils.ECS.LocalWorld;
using Client.Utils.ResourceLoading;
using Common;
using Cysharp.Threading.Tasks;
using Leopotam.Ecs;
using Leopotam.Localization;
using UnityEngine;
using UnityEngine.UI;

namespace Client.Alchemist.Systems.Generators
{
    public class EffectConfigGenerator<TMarker> : IEcsAsyncInitSystem, IEcsRunSystem, IEcsDestroySystem
        where TMarker : struct
    {
        private readonly LocalEcsWorld _world = default;
        private readonly PlayerManager _playerManager = default;
        private readonly ServerTimeService _serverTimeService = default;
        private readonly CsvLocalization _localization = default;
        private readonly ResourceLoadingService _resourceLoadingService = default;
        private readonly ViewComponentGenerator _viewComponentGenerator = default;
        private readonly FtueProgress _ftueProgress = default;
        private readonly TraumasDurationCache _traumasDurationCache = default;
        private readonly EffectScrollItemHolderFactory _effectScrollItemHolderFactory = default;

        private readonly EcsFilter<GenerateEffectRequest, TMarker> _generateRequests = default;

        private readonly TimerType _timerType;
        private readonly CancellationTokenSource _cts;

        public EffectConfigGenerator(TimerType timerType)
        {
            _timerType = timerType;
            _cts = new CancellationTokenSource();
        }
        
        public async UniTask InitAsync(CancellationToken cancellationToken)
        {
            List<PlayerTimer> activeTimers = _playerManager.Session.Timers.GetTimers(PlayerTimers.Query.Get().WithTimerType(_timerType));
            List<UniTask> generateProcesses = new(activeTimers.Count);

            foreach (PlayerTimer timer in activeTimers)
            {
                int duration = (int)timer.Duration;
                if (timer.TimerType == TimerType.Trauma)
                {
                    duration = (int)_traumasDurationCache[timer.ItemId];
                }
                generateProcesses.Add(Generate(timer.ItemId, timer.TimerType, duration, (int) timer.EndTime, _cts.Token));
            }

            await UniTask.WhenAll(generateProcesses);
        }
        
        public void Run()
        {
            ProcessGenerateRequests();
        }

        public void Destroy()
        {
            _cts?.Cancel();
        }

        private void ProcessGenerateRequests()
        {
            foreach (int index in _generateRequests)
            {
                GenerateEffectRequest request = _generateRequests.Get1(index);
                EcsTask task = _generateRequests.Get1(index).Task;

                if (task.IsComplete)
                {
                    _generateRequests.GetEntity(index).Destroy();
                    continue;
                }
                
                if (task.Status == EcsTask.States.Processed)
                {
                    continue;
                }

                task.Status = EcsTask.States.Processed;
                
                int duration = request.Duration;
                if (request.Type == TimerType.Trauma)
                {
                    duration = (int)_traumasDurationCache[request.Id];
                }
                GenerateByRequest(task, request.Id, request.Type, duration, request.EndTime, _cts.Token).Forget();
            }
        }

        private async UniTaskVoid GenerateByRequest(EcsTask task, int effectId, TimerType effectType, int duration, int endTime, CancellationToken token)
        {
            await Generate(effectId, effectType, duration, endTime, token);

            task.Status = EcsTask.States.Done;
        }

        private async UniTask Generate(int effectId, TimerType effectType, int duration, int endTime, CancellationToken token)
        {
            EcsEntity config = GenerateConfig(effectId, effectType);
            GenerateDescription(config, effectId);

            EffectScrollItemHolderView scrollHolder = await _effectScrollItemHolderFactory.Create(config, token);
            await GenerateEffectView(config, effectId, effectType, duration, endTime, scrollHolder.ItemRoot);
        }

        private EcsEntity GenerateConfig(int effectId, TimerType effectType)
        {
            EcsEntity config = _world.NewEntity();
            config.Get<ItemIdData>().Id = effectId;
            config.Get<TimerTypeData>().Type = effectType;
            config.Get<EffectMarker>();
            config.Get<ItemMarker>();
            config.Get<TMarker>();
            
            return config;
        }

        private async UniTask GenerateEffectView(EcsEntity config, int effectId, TimerType effectType, int duration, int endTime, Transform root)
        {
            string viewPath = string.Format(LocalIdents.Paths.EFFECT_VIEW, effectType.ToString());
            EffectTimerView effectView = await _viewComponentGenerator.GenerateView<EffectTimerView>(config, viewPath, root);
            effectView.Init(_serverTimeService, _localization);

            if (effectType == TimerType.Boost || _ftueProgress.Conditions.IsTraumaCalculationEnabled())
            {
                effectView.SetTime(duration, endTime);
            }
            else
            {
                effectView.SetWithoutTimer();
            }
            
            effectView.Hide();

            Graphic icon = await _resourceLoadingService.FromResources(string.Format(LocalIdents.Paths.EFFECT_ICON, effectId)).LoadAsync<Graphic>();
            effectView.SetIcon(icon);
        }

        private void GenerateDescription(EcsEntity config, int effectId)
        {
            string nameKey = string.Format(LocalIdents.Localization.EFFECT_NAME, effectId);
            string descriptionKey = string.Format(LocalIdents.Localization.EFFECT_DESCRIPTION, effectId);
            string name = _localization.Get(nameKey);
            string description = _localization.Get(descriptionKey);;
            
            config.Get<ItemDescriptionData>().Construct(name, description);
        }
    }
}