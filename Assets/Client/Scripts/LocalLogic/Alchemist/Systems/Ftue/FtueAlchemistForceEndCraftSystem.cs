using System.Collections;
using Client.Alchemist.Data;
using Client.Alchemist.UnityComponents;
using Client.Common.Analytics.Helpers;
using Client.Common.Analytics.Trackers.Ftue;
using Client.Common.CodeFlow;
using Client.Common.Configs.Components;
using Client.Common.Configs.Components.Items;
using Client.Common.Ftue.Infrastructure;
using Client.Common.Ftue.Infrastructure.Data;
using Client.Common.Network.MetaNet;
using Client.Common.NpcTab.Controllers;
using Client.Common.NpcTab.Data.TabTypes;
using Client.Common.NpcTab.Data.TabTypes.Markers;
using Client.Common.NpcTab.Systems.Tabs;
using Client.Common.NpcTab.Timer;
using Client.Common.NpcTab.Views;
using Client.Common.Player.Controllers;
using Client.Common.ResourcesTopPanel;
using Client.Common.ResourcesTopPanel.Configs;
using Client.Common.ResourcesTopPanel.Systems;
using Client.Common.UI.Buttons.Abstract;
using Client.Common.UI.Buttons.Pay;
using Client.Common.UI.ScrollController.Components;
using Client.Utils.ECS.LocalWorld;
using Common;
using Leopotam.Ecs;
using UnityEngine.UI;
using TimerType = Client.Common.Network.MetaNet.TimerType;

namespace Client.Alchemist.Systems.Ftue
{
    internal sealed class FtueAlchemistForceEndCraftSystem : FtueSystemBase
    {
        private readonly FtueProgress _ftueProgress = default;
        private readonly IAnalyticsHelper _analyticsHelper = default;
        private readonly PlayerManager _playerManager = default;
        private readonly BottlePriceCalculator _priceCalculator = default;
        private readonly TabSwitcher _tabSwitcher = default;
        private readonly EcsFilter<ItemMarker, ItemIdData, CraftComponent> _itemConfigs = default;
        private readonly EcsFilter<ScrollControllerComponent, BottlesTabMarker> _scrollController = default;
        private readonly EcsFilter<TimerEndEvent> _timerEnds = default;

        private IEnumerator _ftueIt;
        private bool _isCrafting;
        private bool _completeClicked;
        private uint _previousPrice;
        private int _previousPriceBound;

        private readonly IButton _closeButton;
        private readonly ThePayButton _completeButton;
        private readonly SpeedUpButtonView _adsButton;
        private readonly Button _rightArrow; 
        private readonly Button _leftArrow; 

        public FtueAlchemistForceEndCraftSystem(AlchemistTabView view, ThePayButton completeButton, SpeedUpButtonView adsButton)
        {
            _closeButton = view.CloseButton;
            _completeButton = completeButton;
            _adsButton = adsButton;
            _rightArrow = view.RightArrow;
            _leftArrow = view.LeftArrow;
        }

        protected override AccountStep FtueStep => AccountStep.AlchemistCraftTutorialFinished;
        protected override string LogDescription => "alchemist: complete craft";

        protected override bool TutorialStartCondition()
        {
            _isCrafting = _playerManager.Session.Timers.GetTimer(LocalIdents.Keys.FTUE_CRAFT_ID, global::Common.TimerType.Craft) != null;
            if (IsCompleted(FtueProgressKeys.AlchemistCraftHealBottle) && !_isCrafting)
            {
                _ftueProgress.Complete(FtueProgressKeys.AlchemistCompleteCraftHealBottle);
                return false;
            }
            return true;
        }

        protected override void OnInit(CodeFlow<FtueProgress> flow)
        {
            _previousPriceBound = _priceCalculator.PriceBound;
            
            flow.Entry
                .Then(WaitForStartCraftFtue())
                .Then(WaitForCraftConfigAndOverride())
                .Then(InitButtons())
                .Then(WaitForCompleteClick())
                .Then(CompleteStage())
                .Then(_analyticsHelper.FireContextualAnalyticsRoutine(FtueSteps.AlchemistCraftComplete))
                ;
        }

        protected override void OnRun()
        {
            if (_isCrafting)
            {
                CheckForCraftEnd();
            }
        }

        private void OverrideFtuePrice(uint newPrice)
        {
            foreach (int index in _itemConfigs)
            {
                if (_itemConfigs.Get2(index).Id == LocalIdents.Keys.FTUE_CRAFT_ID)
                {
                    ref CraftComponent craftConfig = ref _itemConfigs.Get3(index);
                    _previousPrice = craftConfig.CraftCostToComplete.Price;
                    craftConfig.CraftCostToComplete.Price = newPrice;
                    _itemConfigs.GetEntity(index).Get<DiscountData>().Construct(0, -1);
                }
            }
        }

        private IEnumerator WaitForStartCraftFtue()
        {
            if (IsCompleted(FtueProgressKeys.AlchemistCraftHealBottle))
            {
                yield return null;
            }
            while (!IsCompleted(FtueProgressKeys.AlchemistCraftHealBottle))
            {
                yield return null;
            }
            _isCrafting = true;
        }

        private IEnumerator WaitForCraftConfigAndOverride()
        {
            while (_itemConfigs.IsEmpty())
            {
                yield return null;
            }
            OverrideFtuePrice(0);
        }

        private IEnumerator InitButtons()
        {
            _adsButton.Hide();
            _closeButton.Hide();
            _world.NewEntity().Get<SetResourcesTopPanelConfig>().ResourcesTopPanelConfig = ResourcesTopPanelConfig.NoPurchase;
            
            _rightArrow.gameObject.SetActive(false);
            _leftArrow.gameObject.SetActive(false);
            _scrollController.Get1(0).ScrollController.LockInput(true);
            
            _completeButton.SetAttractionActive(true);
            _completeButton.Clicked += OnCompleteButtonClicked;
            yield break;
        }

        private IEnumerator WaitForCompleteClick()
        {
            while (!_completeClicked && _isCrafting)
            {
                yield return null;
            }
            _completeButton.SetAttractionActive(false);
            _completeButton.Clicked -= OnCompleteButtonClicked;
        }


        private void OnCompleteButtonClicked()
        {
            _completeClicked = true;
        }

        private IEnumerator CompleteStage()
        {
            OverrideFtuePrice(_previousPrice);
            _priceCalculator.PriceBound = _previousPriceBound;
            
            _world.NewEntity().Get<SetResourcesTopPanelConfig>().ResourcesTopPanelConfig = ResourcesTopPanelConfig.WithPurchase;
            _tabSwitcher.OpenTab((int)AlchemistTabType.Bottles);
            _scrollController.Get1(0).ScrollController.LockInput(false);
            _closeButton.Show();
            
            _tabSwitcher.UnlockTab((int)AlchemistTabType.Boosts);
            _tabSwitcher.UnlockTab((int)AlchemistTabType.Traumas);
            
            yield return null;  //wait for force craft request to be sent before sending ftue steps
            _ftueProgress.Complete(FtueProgressKeys.AlchemistCompleteCraftHealBottle);
            ResetDiscount();
        }

        private void ResetDiscount()
        {
            foreach (int index in _itemConfigs)
            {
                if (_itemConfigs.Get2(index).Id == LocalIdents.Keys.FTUE_CRAFT_ID)
                {
                    _itemConfigs.GetEntity(index).Del<DiscountData>();
                }
            }
        }

        private void CheckForCraftEnd()
        {
            foreach (int index in _timerEnds)
            {
                ref TimerEndEvent timerEndEvent = ref _timerEnds.Get1(index);
                if (timerEndEvent.Type != global::Common.TimerType.Craft)
                {
                    continue;
                }

                if (timerEndEvent.ItemId != LocalIdents.Keys.FTUE_CRAFT_ID)
                {
                    continue;
                }

                _isCrafting = false;
                return;
            }
        }
    }
}