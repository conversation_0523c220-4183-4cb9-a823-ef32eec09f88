using System.Threading;
using System.Threading.Tasks;
using Client.Alchemist.Components.Markers;
using Client.Alchemist.Data;
using Client.Alchemist.Quests;
using Client.Alchemist.Systems;
using Client.Alchemist.Systems.Analytics;
using Client.Alchemist.Systems.DrinkSystems;
using Client.Alchemist.Systems.Ftue;
using Client.Alchemist.Systems.Generators;
using Client.Alchemist.Systems.Generators.DrinkConditions;
using Client.Alchemist.Systems.PopupStates.Boosts;
using Client.Alchemist.Systems.PopupStates.Bottles;
using Client.Alchemist.Systems.PopupStates.Traumas;
using Client.Alchemist.Systems.Tabs;
using Client.Alchemist.UnityComponents;
using Client.Common.Analytics.Helpers;
using Client.Common.CraftReadyItems;
using Client.Common.Currency;
using Client.Common.Dialogs.Runtime.Systems;
using Client.Common.ECS.LocalWorld;
using Client.Common.HintSystem;
using Client.Common.Items;
using Client.Common.Network.MetaNet;
using Client.Common.NpcTab.Craft;
using Client.Common.NpcTab.Data.TabTypes;
using Client.Common.NpcTab.Data.TabTypes.Markers;
using Client.Common.NpcTab.Data.UI;
using Client.Common.NpcTab.ItemCounts;
using Client.Common.NpcTab.Pieces;
using Client.Common.NpcTab.Systems.Craft;
using Client.Common.NpcTab.Systems.Tabs;
using Client.Common.Player.Controllers;
using Client.Common.Player.ProgressItems;
using Client.Common.Quests.Goals.Systems;
using Client.Common.Quests.Systems;
using Client.Common.ResourcesTopPanel;
using Client.Common.ResourcesTopPanel.Configs;
using Client.Common.ScreenTransitions;
using Client.Common.TelegramBot.Systems;
using Client.Common.TimeGiver.Implementations;
using Client.Common.Traumas;
using Client.Common.Traumas.Data;
using Client.Common.UI.BackButton;
using Client.Common.UI.InputLockService;
using Client.Common.UI.ScrollController.Controllers;
using Client.Utils.Extensions.EcsExtensions.SystemExtensions;
using Client.Utils.ResourceLoading;
using Cysharp.Threading.Tasks;
using Leopotam.Localization;
using UnityEngine;
using VContainer;
using TabStateMachine = Client.Alchemist.Systems.Tabs.TabStateMachine;

namespace Client.Alchemist
{
    internal struct AlchemistScope
    {
    }

    internal class AlchemistStartup : LocalWorld<AlchemistScope>
    {
        [SerializeField] private AlchemistLifeTimeScope _lifeTimeScope;
        [SerializeField] private AlchemistTabView _view;
        [SerializeField] private TabAnchorsBundle _anchorsBundle;
        [SerializeField] private BottlesTabBundle _bottlesTabBundle;
        [SerializeField] private TraumasTabBundle _traumasTabBundle;
        [SerializeField] private BoostsTabBundle _boostsTabBundle;

        [Inject] private MetaNet _metaNet;
        [Inject] private PlayerManager _playerManager;
        [Inject] private ItemData _itemData;
        [Inject] private ServerTimeService _serverTimeService;
        [Inject] private ResourceLoadingService _resourceLoadingService;
        [Inject] private CsvLocalization _localization;
        [Inject] private ProgressItemsCache _progressItemsCache;
        [Inject] private LocalAnalyticsHelper _analyticsHelper;
        [Inject] private FullScreenLocker _screenLocker;

        protected override async Task OnStart(CancellationToken cancellationToken)
        {
            ScrollGenerator scrollGenerator = await new ScrollGenerator().InitAsync(_resourceLoadingService, cancellationToken);

            Systems
                .Add(new CraftReadyItemsUpdateSystem())
                .AddFeature(new AlchemistQuestFeature())

                .Add(new AlchemistViewInitSystem(_view))

                .AddFeature(new BottleInitFeature(_anchorsBundle, _bottlesTabBundle, _progressItemsCache))
                .AddFeature(new TraumaInitFeature(_anchorsBundle))
                .AddFeature(new BoostInitFeature(_anchorsBundle))
                .AddFeature(new DrinkConditionsGenerator(_itemData))
                
                .Add(new FtueAlchemistStartCraftSystem(_view, _bottlesTabBundle))
                .Add(new FtueAlchemistForceEndCraftSystem(_view, _bottlesTabBundle.CompleteButton, _boostsTabBundle.SpeedUpButton))
                .Add(new FtueFirstTraumaSystem(_view.CloseButton, _traumasTabBundle.CompleteButton, _traumasTabBundle.SpeedUpButton))
                
                .Add(new BackButtonWithGlintSystem(_view.CloseButton))
                .AddFeature(new DialogFeature(World, _analyticsHelper))
                
                .AddFeature(new AlchemistNextRegionItemFeature(_playerManager, _anchorsBundle))
                .AddFeature(new TabStateMachine(World, scrollGenerator, _bottlesTabBundle))
                .AddFeature(new BottlesStateMachine(_view, _bottlesTabBundle, _screenLocker))
                .AddFeature(new TraumasStateMachine(_traumasTabBundle))
                .AddFeature(new BoostsStateMachine(_boostsTabBundle))
                .AddFeature(new PieceUiFeature())
                .AddFeature(new BottleCountUiFeature())
                
                .Add(new DrinkButtonSystem(_bottlesTabBundle.DrinkButton))
                .Add(new BoostPotionDrinkSystem())
                .Add(new HealPotionDrinkSystem())
                
                .Add(new EffectTabButtonHighlightSystem<TraumaMarker, TraumasTabMarker>())
                .Add(new EffectTabButtonHighlightSystem<BoostMarker, BoostsTabMarker>())
                .Add(new BoostTabExpireViewSystem())
                
                .Add(new CraftSystem())
                .Add(new CraftForceCompleteSystem())
                .AddFeature(new ResourcesTopPanelFeature(ResourcesTopPanelConfig.WithPurchase))
                // should be last.
                .Add(new TabOpenSystem<AlchemistTabType>())
                .Add(new DrinkElixirQuestProgressSystem())
                .Add(new AlchemistAnalyticsTrackerSystem())
                .Add(new AudioSystem())
                .Add(new FadeOutSystem())
                .OneFrame<TraumaHealed>()
                .OneFrameEntity<ItemUsedEvent>()
                .OneFrameEntity<DrinkButtonUpdateEvent>()
                .InjectFromContainer(_lifeTimeScope.Container)
                ;
        }
    }
}