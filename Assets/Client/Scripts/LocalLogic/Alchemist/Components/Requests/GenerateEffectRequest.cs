using Client.Common.Player.Timers;
using Client.Utils.ECS.EcsTaskTools;
using Common;

namespace Client.Alchemist.Components.Requests
{
    public struct GenerateEffectRequest
    {
        public EcsTask Task;
        public int Id;
        public TimerType Type;
        public int Duration;
        public int EndTime;

        public EcsTask Construct(int id, TimerType type, int duration, int endTime)
        {
            Id = id;
            Type = type;
            Duration = duration;
            EndTime = endTime;
            Task = new EcsTask();

            return Task;
        }
        
        public EcsTask Construct(PlayerTimer timer)
        {
            Id = timer.ItemId;
            Type = timer.TimerType;
            Duration = (int) timer.Duration;
            EndTime = (int) timer.EndTime;
            Task = new EcsTask();

            return Task;
        }
    }
}