#if DEBUG_PANEL

using System;
using System.Collections.Generic;
using System.Globalization;
using System.Linq;
using System.Threading.Tasks;
using Client.Common.BloodColba;
using Client.Common.Countries;
using Client.Common.CountryChange.Components;
using Client.Common.CSV;
using Client.Common.Items;
using Client.Common.Items.QuantityProviders;
using Client.Common.LanguageChange.Data;
using Client.Common.Network.MetaNet;
using Client.Common.NpcTab.Components;
using Client.Common.Player.Controllers;
using Client.Common.SceneLoading;
using Client.FPV.Battle.Components;
using Client.Utils.ECS.LocalWorld;
using Client.Utils.GameLogger;
using Client.Utils.ResultTool.Results;
using Client.Utils.ServiceTool;
using Common;
using Cysharp.Threading.Tasks;
using External;
using Google.Protobuf.WellKnownTypes;
using Leopotam.Ecs;
using Leopotam.Localization;
using TMPro;
using UnityEngine;
using UnityEngine.UI;
using Enum = System.Enum;
using ItemQuantity = Client.Common.Network.MetaNet.ItemQuantity;
using SkinDurability = Client.Common.Network.MetaNet.SkinDurability;
using StatusResponse = Client.Common.Network.MetaNet.StatusResponse;
using WeaponSkin = External.WeaponSkin;

namespace Client.DebugPanel.DebugPanelComponents
{
    public class DebugPanelItems : MonoBehaviour
    {
        private readonly ushort[] _progressItemIndices =
        {
            //Costumes:
            10002,
            10003,
            10004,
            10005,
            10006,
            //Shurikens:
            11002,
            11003,
            11004,
            //Shuriken skins:
            12001,
            12002,
            12003,
            12004,
            12005,
            12006,
            12007,
            12008,
            12009,
            12010,
            //Amulets:
            17001,
            17002,
            17003
        };

        private const ushort _BLOOD_SACK_ = 14700;

        [SerializeField] private TMP_InputField _itemsAmountInput;
        [SerializeField] private TMP_InputField _itemsItemIdInput;
        [SerializeField] private TMP_Dropdown _typeDropdown;
        [SerializeField] private TMP_Dropdown _itemsDropdown;
        [SerializeField] private Button _itemsAddButton;
        [SerializeField] private Button _itemsRemoveButton;
        [SerializeField] private Button _itemsAddAllButton;
        [SerializeField] private Button _itemsAddLootButton;
        [SerializeField] private TMP_InputField _addBloodInput;
        [SerializeField] private Button _addBloodButton;
        [SerializeField] private Button _collectBloodButton;
        [SerializeField] private Text _progressLabel;
        [SerializeField] private GameObject _itemsWrapper;
        [SerializeField] private TMP_Dropdown _itemsLanguage;
        [SerializeField] private TMP_Dropdown _itemsCountries;
        [SerializeField] private TMP_InputField _itemsAmountDurability;
        [SerializeField] private Button _itemsDurabilityButton;
        [SerializeField] private Button _itemsUnequipWeaponButton;
        [SerializeField] private Button _itemsUnequipAmuletButton;
        [SerializeField] private Button _itemEquipButton;

        private Dictionary<TMP_Dropdown.OptionData, ClientItemData> _optionToItem;
        
        private LocalEcsWorld _world;
        private EcsSystems _systems;
        private GoogleDocsData _googleDocsData;
        private ItemData _itemData;

        private void Start()
        {
            OpenItemsPanel(false);
            _itemData = Service<ItemData>.Get();

            var itemTypes = Enum.GetValues(typeof(ItemType)) as ItemType[];
            if (itemTypes != null)
            {
                _typeDropdown.AddOptions(itemTypes.Select(type => type.ToString()).ToList());
            }
        }

        private void OnEnable()
        {
            _progressLabel.text = String.Empty;

            _typeDropdown.onValueChanged.AddListener(
                index => { PopulateItemOptions((ItemType)index); });

            _itemsDropdown.onValueChanged.AddListener(
                index =>
                {
                    TMP_Dropdown.OptionData option = _itemsDropdown.options[index];
                    ClientItemData item = _optionToItem[option];
                    _itemsItemIdInput.text = item.Id.ToString();
                    _itemsAmountDurability.text = item.MaxDurability.ToString();
                });

            _itemsAddButton.onClick.AddListener(() => ExecuteItemAction(AdminProgressAction.Add));
            _itemsRemoveButton.onClick.AddListener(() => ExecuteItemAction(AdminProgressAction.Remove));
            _itemsAddAllButton.onClick.AddListener(AddAllProgressItems);
            _addBloodButton.onClick.AddListener(AddBlood);
            _collectBloodButton.onClick.AddListener(CollectBlood);
            _itemsDurabilityButton.onClick.AddListener(SetDurability);
            _itemsAddLootButton.onClick.AddListener(SetLootItem);
            _itemsUnequipWeaponButton.onClick.AddListener(UnequipWeapon);
            _itemsUnequipAmuletButton.onClick.AddListener(UnequipAmulet);
            _itemEquipButton.onClick.AddListener(EquipItem);
        }

        private async void EquipItem()
        {
            _itemEquipButton.interactable = false;
            ClientItemData selectItem = _optionToItem[_itemsDropdown.options[_itemsDropdown.value]];
            
            if ((ItemType) selectItem.Type is ItemType.Bomb or ItemType.Hook or ItemType.PowerStone)
            {
                await EquipPocket(selectItem);
            }
            else
            {
                await EquipItem(selectItem);
            }
            _itemEquipButton.interactable = true;
        }

        private async UniTask EquipItem(ClientItemData selectItem)
        {
            MetaNet metaNet = Service<MetaNet>.Get();
            _progressLabel.text = $"Equipping item {selectItem.Id}...";
            var result = await metaNet.EquipItem(selectItem.Id);
            _progressLabel.text += result.IsSuccess ? "Done!" : "Failed";
        }

        private async UniTask EquipPocket(ClientItemData selectItem)
        {
            MetaNet metaNet = Service<MetaNet>.Get();
            if (!UInt32.TryParse(_itemsAmountInput.text, out uint value) || value < 1)
            {
                value = 10;
            }

            _progressLabel.text = $"Equipping pocket items {selectItem.Id} X {value}...";
            var result = await metaNet.EquipPocket(new ItemQuantity(selectItem.Id, value));
            _progressLabel.text += result.IsSuccess ? "Done!" : "Failed";
        }

        private void SetLanguagesList()
        {
            _itemsLanguage.ClearOptions();
            List<TMP_Dropdown.OptionData> options = new List<TMP_Dropdown.OptionData>();
            foreach (KeyValuePair<string, string> lang in LanguagesList.LanguageList)
            {
                TMP_Dropdown.OptionData option = new TMP_Dropdown.OptionData
                {
                    text = lang.Value
                };
                options.Add(option);
            }
            _itemsLanguage.AddOptions(options);
            _itemsLanguage.onValueChanged.AddListener(ChangeLanguage);
        }
        
        private void SetCountriesList()
        {
            _itemsCountries.ClearOptions();
            List<TMP_Dropdown.OptionData> options = new List<TMP_Dropdown.OptionData>();
            CountryData countryData = Service<CountryData>.Get();
            CsvLocalization localization = Service<CsvLocalization>.Get();
            foreach (KeyValuePair<string, string> lang in countryData.GetRawDict())
            {
                TMP_Dropdown.OptionData option = new TMP_Dropdown.OptionData
                {
                    text = localization.Get(lang.Key)
                };
                options.Add(option);
            }
            _itemsCountries.AddOptions(options);
            _itemsCountries.onValueChanged.AddListener(ChangeCountry);
        }

        private async void CollectBlood()
        {
            _progressLabel.text = "Collecting blood...";
            MetaNet metaNet = Service<MetaNet>.Get();
            Result<CollectBloodResponse> result =  await metaNet.CollectBlood(_BLOOD_SACK_);
            if (result.IsFailure)
            {
                _progressLabel.text = $"Error collecting blood: {result.Error}";
                return;
            }
            PlayerManager playerManager = Service<PlayerManager>.Get();
            playerManager.Session.BloodColba.Blood = result.Value.Count;
            _world.NewEntity().Get<BloodColbaDebugAfterShow>();
            _progressLabel.text = $"Collecting blood done! Total: {result.Value.Count}";
        }

        private async void AddBlood()
        {
            if (uint.TryParse(_addBloodInput.text, out uint count))
            {
                await ExecuteItemAction(AdminProgressAction.Add, PlayerInventory.Items.BLOOD_CURRENCY, count);
            }
            MetaNet metaNet = Service<MetaNet>.Get();
            PlayerManager playerManager = Service<PlayerManager>.Get();
            Result<DrainBloodResponse> drainBlood;
            int[] firstUnlock = null;
            do
            {
                drainBlood = await metaNet.DrainBlood();
                if (drainBlood.IsFailure)
                {
                    GameLogger.LogError($"error draining blood when adding blood: {drainBlood.Error}");
                    return;
                }
                _progressLabel.text = $"Blood unlock: {string.Join(',',drainBlood.Value.Unlocks)}";
                firstUnlock ??= drainBlood.Value.Unlocks.ToArray();
            } while (drainBlood.Value.Unlocks.Count > 0);
            _world.NewEntity().Get<BloodDrainEvent>().Construct(playerManager.Session.BloodColba.Blood, firstUnlock);
            _world.NewEntity().Get<BloodColbaHide>();
            _progressLabel.text = $"Added {count} blood... Done!";
            playerManager.Session.BloodColba.Blood = 0;
        }

        private void OnDisable()
        {
            _typeDropdown.onValueChanged.RemoveAllListeners();
            _itemsDropdown.onValueChanged.RemoveAllListeners();

            _itemsAddButton.onClick.RemoveAllListeners();
            _itemsRemoveButton.onClick.RemoveAllListeners();
            _itemsAddAllButton.onClick.RemoveAllListeners();
            
            _itemEquipButton.onClick.RemoveAllListeners();
        }

        private async void AddAllProgressItems()
        {
            foreach (var item in _progressItemIndices)
            {
                await ExecuteItemAction(AdminProgressAction.Add, item, 1);
            }

            _progressLabel.text = "Adding all progress...Done!";
        }

        private void PopulateItemOptions(ItemType type)
        {
            if (_itemData is null)
            {
                return;
            }

            List<ClientItemData> itemData = _itemData.GetItemDataByType(type);
            _optionToItem = new Dictionary<TMP_Dropdown.OptionData, ClientItemData>();
            var quantifiedLocalization = Service<QuantifiedLocalizationService>.Get();
            foreach (ClientItemData item in itemData)
            {
                string itemName = quantifiedLocalization.GetQuantifiedName(item.Id, 1);
                TMP_Dropdown.OptionData optionData = new() { text = itemName };
                _optionToItem.Add(optionData, item);
            }

            List<TMP_Dropdown.OptionData> optionDataContainer = _optionToItem.Keys.OrderBy(data => data.text).ToList();
            _itemsDropdown.ClearOptions();
            _itemsDropdown.AddOptions(optionDataContainer);
            ClientItemData firstItem = _optionToItem[_itemsDropdown.options[0]];
            _itemsItemIdInput.text = firstItem.Id.ToString();
            _itemsAmountInput.text = "1";
            if (type is ItemType.Currency or ItemType.OutfitCloth or ItemType.ShurikenSkinPaint)
            {
                _itemsAmountInput.text = "100000";
            }

            if (type is ItemType.Outfit or ItemType.ShurikenSkin)
            {
                _itemsAmountDurability.text = firstItem.MaxDurability.ToString();
            }
        }

        private async void ExecuteItemAction(AdminProgressAction action)
        {
            uint amount = uint.TryParse(_itemsAmountInput.text, out uint amountValue) ? amountValue : 0;
            int itemId = int.TryParse(_itemsItemIdInput.text, out int itemIdValue) ? itemIdValue : int.MinValue;
            if (amount == 0)
            {
                Debug.LogError("Invalid amount");
                return;
            }

            if (itemId == int.MinValue)
            {
                Debug.LogError("Invalid itemId");
                return;
            }

            await ExecuteItemAction(action, itemId, amount);
        }

        private async Task ExecuteItemAction(AdminProgressAction action, int itemId, uint amount)
        {
            _progressLabel.text = $"{action.ToString()} item {itemId}, count: {amount}...";
            GameLogger.Log($"AdminProgressAction action: {action} itemId: {itemId} amount: {amount}");

            MetaNet metaNet = Service<MetaNet>.Get();

            Result<StatusResponse> actionResponse = await metaNet.AdminProgressAction(action, itemId, amount);
            if (actionResponse.IsFailure)
            {
                _progressLabel.text = $"{action.ToString()} item {itemId}, count: {amount}...Error {actionResponse.Error}";
                return;
            }

            _progressLabel.text = $"{action.ToString()} item {itemId}, count: {amount}...Done!";
        }

        public void SetEcsWorld(LocalEcsWorld localWorld, EcsSystems systems)
        {
            _world = localWorld;
            _systems = systems;
            _googleDocsData = Service<GoogleDocsData>.Get();
        }

        private void ChangeLanguage(int langId)
        {
            _world.NewEntity().Get<ChangeLanguageRequest>().LangId = langId == 0 ? "English" : "Russian";
            SceneLoader sceneLoader = Service<SceneLoader>.Get();
            sceneLoader.ReloadCurrent();
        }
        
        private void ChangeCountry(int countryId)
        {
            CountryData countryData = Service<CountryData>.Get();
            Dictionary<string, string> countries = countryData.GetRawDict();
            string key = countries.Keys.ToArray()[countryId];
            _world.NewEntity().Get<ChangeCountryRequest>().CountryId = key;
        }

        private async void SetDurability()
        {
            ClientItemData selectItem = _optionToItem[_itemsDropdown.options[_itemsDropdown.value]];
            
            MetaNet metaNet = Service<MetaNet>.Get();
            
            uint durability = uint.Parse(_itemsAmountDurability.text, CultureInfo.InvariantCulture);
            _itemsDurabilityButton.interactable = false;  
            
            if ((ItemType) _itemData.GetItemDataById(selectItem.Id).Type == ItemType.Shuriken)
            {
                PlayerWeaponStorage weapons = Service<PlayerManager>.Get().Session.Progress.WeaponStorage;
                if (!weapons.TryGet(selectItem.Id, out var weapon) || weapon.Skin.Id == PlayerInventory.Items.DEFAULT_SHURIKEN_SKIN_ID)
                {
                    _itemsDurabilityButton.interactable = true;
                    _progressLabel.text = $"No skin found on shuriken {selectItem.Id}";
                    return;
                }
                WeaponSkin weaponSkin = new() { WeaponId = selectItem.Id, Durability = durability, SkinId = weapons.GetSkinId(selectItem.Id)};
                Result result = await metaNet.SetWeaponSkinsDurability(weaponSkin);
                if (result.IsSuccess)
                {
                    _progressLabel.text = ($"Skin durability changed for weapon {selectItem.Id} to {_itemsAmountDurability.text}");
                }
            }
            else
            {
                List<SkinDurability> updateSkinDurability = new List<SkinDurability>
                {
                    new SkinDurability
                    {
                        Id = (ushort)selectItem.Id,
                        Durability = durability
                    }
                };
                Result<StatusResponse> durabilityDebug = await metaNet.SetDurabilityDebug(updateSkinDurability);
                if (durabilityDebug.IsSuccess)
                {
                    _progressLabel.text = ($"Set durability for item {selectItem.Id} to {_itemsAmountDurability.text}");
                }
            }

            _itemsDurabilityButton.interactable = true;
        }

        private void SetLootItem()
        {
            PlayerManager playerManager = Service<PlayerManager>.Get();
            ItemQuantity item = new ItemQuantity
            {
                Id = ushort.TryParse(_itemsItemIdInput.text, out ushort itemIdValue) ? itemIdValue : ushort.MinValue,
                Count = uint.TryParse(_itemsAmountInput.text, out uint amountValue) ? amountValue : 0
            };
            playerManager.Session.RegionResultData.RegionStorage.AddAnyReward(item);
        }

        private async void UnequipWeapon()
        {
            MetaNet metaNet = Service<MetaNet>.Get();
            Result<ClientItemEquipResponse> equipItem = await metaNet.EquipItem(PlayerInventory.Items.FIRST_SHURIKEN_ID);
        }
        
        private async void UnequipAmulet()
        {
            PlayerManager playerManager = Service<PlayerManager>.Get();
            await ExecuteItemAction(AdminProgressAction.Remove, (ushort)playerManager.Session.Progress.AmuletId, 1);
        }

        public void OpenItemsPanel(bool state)
        {
            _itemsWrapper.SetActive(state);
            if (state)
            {
                PopulateItemOptions(ItemType.Outfit);
                SetCountriesList();
                SetLanguagesList();
            }
        }
    }
}

#endif