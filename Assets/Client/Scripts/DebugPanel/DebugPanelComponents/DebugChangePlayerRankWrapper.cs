#if DEBUG_PANEL

using Client.Common.Network.MetaNet;
using Client.Common.Player.Controllers;
using Client.Utils.GameLogger;
using Client.Utils.ResultTool.Results;
using Client.Utils.ServiceTool;
using External;
using TMPro;
using UnityEngine;
using UnityEngine.UI;

namespace Client.DebugPanel.DebugPanelComponents
{
    public class DebugChangePlayerRankWrapper : MonoBehaviour
    {
        [SerializeField] private UiChangeIntegerValue _integerValue;
        [SerializeField] private Button _updateRankButton;
        [SerializeField] private TMP_Text _text;

        private PlayerManager _playerManager;
        
        private void Start()
        {
            _playerManager = Service<PlayerManager>.Get();

            Init();
            
            _updateRankButton.onClick.AddListener(UpdatePlayerRank);
        }

        private void OnDestroy()
        {
            _updateRankButton.onClick.RemoveListener(UpdatePlayerRank);
        }
        
        private void Init()
        {
            int currentRank = _playerManager.Session.Progress.Rank;
            
            int[] rankArray = DebugData.BotRanksRange;
            
            _integerValue.Init(true, rankArray);
            _integerValue.SetValue(currentRank);
            UpdateText();
        }

        private void UpdatePlayerRank()
        {
            int playerRank = _playerManager.Session.Progress.Rank;
            int selectedValue = _integerValue.GetValue();

            if (selectedValue == playerRank)
            {
                UpdateText("Stop trying to send same rank...", Color.red);
                GameLogger.LogError("[AdminRank] Trying to change rank to the same value");
                return;
            }
            
            SendRequest(selectedValue);
        }

        private async void SendRequest(int newRank)
        {
            MetaNet metaNet = Service<MetaNet>.Get();
            
            _updateRankButton.interactable = false;
            
            Result<StatusResponse> setRankAdmin = await metaNet.SetRankAdmin((byte)newRank);

            if (setRankAdmin.IsSuccess)
            {
                _playerManager.Session.Progress.SetRank(newRank);
                UpdateText("Rank changed successfully", Color.green);
                GameLogger.Log($"[AdminRank] Rank changed from {_playerManager.Session.Progress.Rank} to {newRank}");
            }
            
            _updateRankButton.interactable = true;
        }

        private void UpdateText(string text = null, Color color = default)
        {
            _text.SetText(text);
            _text.color = color;
        }
    }
}

#endif