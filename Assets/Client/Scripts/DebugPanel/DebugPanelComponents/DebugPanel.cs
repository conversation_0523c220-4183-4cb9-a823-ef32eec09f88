#if DEBUG_PANEL
using System;
using System.Collections.Generic;
using System.Linq;
using System.Reflection;
using System.Threading;
using System.Threading.Tasks;
using Client.Common;
using Client.Common.AI;
using Client.Common.AI.Data;
using Client.Common.AppVersion;
using Client.Common.Battle.History;
using Client.Common.CameraController;
using Client.Common.Components.Unit;
using Client.Common.CSV;
using Client.Common.DailyBonus.Services;
using Client.Common.DebugTools.Events;
using Client.Common.ECS;
using Client.Common.Ftue.Helpers;
using Client.Common.Ftue.Infrastructure;
using Client.Common.Ftue.Infrastructure.Data;
using Client.Common.HumanoidBody.Data;
using Client.Common.Items;
using Client.Common.LocalLogic.FPV;
using Client.Common.LocalLogic.TopDown.Unit;
using Client.Common.Network.Authentication.Credentials;
using Client.Common.Network.Authentication.Credentials.Social;
using Client.Common.Network.Authentication.Credentials.Social.Facebook;
using Client.Common.Network.Authentication.Credentials.Social.Google;
using Client.Common.Network.ExternalEvents.Data;
using Client.Common.Network.MetaNet;
using Client.Common.NpcTab.Timer;
using Client.Common.Player.Controllers;
using Client.Common.Player.Timers;
using Client.Common.PowerStoneFeature;
using Client.Common.Ranks;
using Client.Common.ResourcesTopPanel.Views;
using Client.Common.SceneLoading;
using Client.Common.SceneLoading.SceneData.Implementations;
using Client.Common.SceneLoading.SceneProviders;
using Client.Common.TimeGiver.Abstractions;
using Client.Common.TimeGiver.Implementations;
using Client.DebugPanel.Data;
using Client.DebugPanel.Services;
using Client.DebugPanel.TimeServices;
using Client.FPV.Battle.Camera.UnityComponents;
using Client.FPV.Battle.Components;
using Client.FPV.Battle.Components.Cheats;
using Client.FPV.Battle.DebugTools;
using Client.FPV.Battle.ShieldBehaviour.Defence.Base.Components;
using Client.FPV.Battle.Systems.Wounds;
using Client.FPV.Battle.UnityComponents;
using Client.TopDown.Market.Components;
using Client.TopDown.Region.AI.Controllers;
using Client.TopDown.Region.Components;
using Client.TopDown.Region.Corpses.Systems.Creators;
using Client.TopDown.Region.UnityComponents;
using Client.TopDown.Region.VisibilityBehaviour.Overlap.Services;
using Client.TopDown.SpawnSystem.Components;
using Client.TopDown.SpawnSystem.DataProviders.Items;
using Client.TopDown.SpawnSystem.DataProviders.Skins;
using Client.Utils.ECS.EcsTaskTools;
using Client.Utils.ECS.LocalWorld;
using Client.Utils.Extensions;
using Client.Utils.GameLogger;
using Client.Utils.ResourceLoading;
using Client.Utils.ServiceTool;
using Common;
using Cysharp.Threading.Tasks;
using Leopotam.Ecs;
using TMPro;
using UnityEngine;
using UnityEngine.EventSystems;
using UnityEngine.SceneManagement;
using UnityEngine.UI;
using BattleResult = Client.Common.Network.MetaNet.BattleResult;
using BodyPart = Client.Common.Network.MetaNet.BodyPart;
using Touch = UnityEngine.Touch;
using Client.FPV.Battle.DebugTools.Quests;
using Client.TopDown.Region;
using Client.TopDown.Region.Helpers;
using Client.TopDown.SpawnSystem.DataProviders;
using Client.TopDown.SpawnSystem.DataProviders.Traumas;
using Client.Utils.ResultTool.Results;
using External;
using VContainer;
using VContainer.Unity;
using SkinDurability = Client.Common.Network.MetaNet.SkinDurability;
using TimerType = Client.Common.Network.MetaNet.TimerType;

#if UNITY_EDITOR
using UnityEditor;
#endif

namespace Client.DebugPanel.DebugPanelComponents
{
    public class DebugPanel : MonoBehaviour
    {
        private const float _PANEL_CLOSED_POSITION_X_ = 750f;
        private const float _PANEL_OPEN_POSITION_X_ = 0f;

        [SerializeField] TMP_Text _versionLbl;
        public RectTransform Panel;

        [SerializeField] private Button _openCloseButton;

        [Header("GENERAL")]
        [SerializeField] private GameObject _generalPage;

        [SerializeField] private Button _fpsEnableButton;
        [SerializeField] private FpsCounter _fpsCounter;
        
        [SerializeField] private Button _signOutButton;

        [SerializeField]
        private Button _skipAllBoosts;

        private TextMeshProUGUI _fpsEnableLabel;
        private const string ShowFpsText = "SHOW\nFPS";
        private const string HideFpsText = "HIDE\nFPS";

        [SerializeField]
        private Button _shieldTouchButton;

        private TextMeshProUGUI _shieldTouchLabel;
        private TouchSettings _shieldTouchSettings = default;
        private const string ShieldSingleText = "SET\nONE\nTOUCH\nSHIELD";
        private const string ShieldDoubleText = "SET\nDOUBLE\nTOUCH\nSHIELD";

        [SerializeField] private Button _itemsOpenPageButton;

        [SerializeField] private Button _resetGameSavesBtn;

        [SerializeField] private Button _crashBtn;

        [SerializeField] private GameObject _itemsPage;

        [Header("TUTORIAL")]
        public GameObject TutorialPage;

        public Button SkipTutorial;
        public Dropdown SkipTutorialSelector;

        [Header("MARKET")]
        [SerializeField]
        private GameObject _marketPage;

        [SerializeField]
        private Button _enterWheel;

        [SerializeField]
        private Button _unbindFacebookButton;
        
        [SerializeField]
        private Button _unbindGoogleButton;

        [SerializeField]
        private Button _toBattleBtn;

        [SerializeField]
        private Button _toLeagueBtn;

        private const string _MARKET_SCENE_ = "Market";

        [Header("BATTLE")]
        public GameObject BattlePage;

        public Button WinBattleBtn;
        public Button LoseBattleBtn;
        public Button EnableInvincibilityBattle;

        public Button DealDamageButton;
        public TMP_Dropdown DamageTypeDropDown;
        TextMeshProUGUI EnableInvincibilityLabelBattle;
        private const string _BATTLE_SCENE_ = "Battle";

        [Header("HEADBOB")]
        [SerializeField]
        private Toggle _cameraSettingsToggle;

        [SerializeField]
        private GameObject _cameraSettingsContainer;

        public UiChangeFloatValue WalkStepAmplitude;
        public UiChangeFloatValue WalkStepFrequency;
        public UiChangeFloatValue WalkStepSpeed;
        public UiChangeFloatValue IdleStepAmplitude;
        public UiChangeFloatValue IdleStepFrequency;
        public UiChangeFloatValue IdleStepSpeed;
        public UiChangeFloatValue CameraVerticalOffset;
        private UnitCameraData _originCameraData;

        [Header("BOT AI")]
        [SerializeField] UiBotAiSettings BotAiSettings;

        [Header("REGION")]
        public GameObject RegionPage;

        private const string _REGION_SCENE_ = "Region";

        [SerializeField]
        private ResourcesTopPanelView _resourcePanel;

        public Button EnableInvincibilityRegion;
        private bool _isPlayerInvincible;
        private TextMeshProUGUI _enableInvincibilityLabelRegion;
        private const string _ENABLE_INVINCIBILITY_TEXT_ = "Enable Invincibility";
        private const string _DISABLE_INVINCIBILITY_TEXT_ = "Disable Invincibility";

        [Header("PROGRESS")]
        public Button AddOneKill;

        public Button AddTenKills;
        public Button AddFiftyKills;
        public GameObject AddingKillsProgress;
        TextMeshProUGUI AddingKillsProgressLabel;

        [Header("BOT SPAWNER")]
        public UiChangeIntegerValue BotRank;

        public UiChangeIntegerValue BotShuriken;
        public UiChangeIntegerValue BotShurikenSkin;
        public UiChangeIntegerValue BotOutfit;
        public UiChangeIntegerValue BotAmulet;
        public UiChangeIntegerValue BotStone;
        public UiChangeIntegerValue BotHook;
        public UiChangeIntegerValue BotBomb;

        [SerializeField]
        private TMP_Dropdown _regionBotTypeDropdown;

        [SerializeField]
        private TMP_Dropdown _battleBotTypeDropdown;

        [SerializeField]
        private Toggle _regionBotTypeToggle;

        [SerializeField]
        private Toggle _battleBotTypeToggle;

        bool areBotSettingsInit;
        private EcsEntity _debugDuelBeginEntity;

        public Button ResetBotSettings;
        public Button RandomBotSettings;
        public Button SpawnBot;
        public Button SpawnCorpse;

        [Header("QUESTS")]
        public DebugPanelPrizes PrizesPage;

        [Header("TRAUMA")]
        public GameObject TraumaPage;

        public Dropdown TraumaSelector;
        public Button AddTraumaButton;

        [Header("USER PROGRESS")]
        public GameObject UserProgressPage;

        [SerializeField] UiUserProgressView ProgressView;

        [Header("ITEMS PANEL")]
        [SerializeField] private DebugPanelItems _debugPanelItems;

        [Header("VISIBILITY")]
        [SerializeField] private Button _visibilityButton;

        [SerializeField] private TMP_Text _visibilityLabel;
        private VisibilityCheckerByCamera _visibilityChecker;
        private Camera _visibilityCamera;

        [Header("Speed Modifier")]
        [SerializeField] private Slider _speedModifierSlider;

        [SerializeField] private TMP_Text _speedModifierValue;
        [SerializeField] private RegionVisualData _visualData;

        [Header("Tabs")]
        [SerializeField] private DebugStaminaInfiniteWrapper _staminaInfiniteWrapper;

        [SerializeField] private DebugEnemyShieldToggle _debugEnemyShieldToggle;
        [SerializeField] private DebugPanelBotTypeLabels _debugPanelBotTypeLabels;
        [SerializeField] private DebugSkipIntroPage _debugSkipIntroPage;

        [Header("Bots settings")]
        [SerializeField] private DebugPanelBotSettingsPage _debugPanelBotSettingsPage;
        
        [Header("Time")]
        [SerializeField] private Button _timeOpenPageButton;
        [SerializeField] private GameObject _timePage;
        [SerializeField] private TimeSetterBlockView _timeSetterBlockView;
        private CustomTimeActualizer _customTimeActualizer;
        
        // debug panel state
        private LocalEcsWorld _world;
        private EcsWorld _globalWorld;
        private EcsSystems _systems;
        private Canvas _canvas;
        private readonly TouchManager _touchManager = new TouchManager();
        private Vector2 _startPanelAnchoredPosition = new Vector2(_PANEL_CLOSED_POSITION_X_, 0f);
        private string _currentScene = "";

        private PlayerManager _playerManager;
        private static DebugPanel _Instance;

        private static MetaNet _MetaNet;

        private static void SetWorld(LocalEcsWorld world, EcsSystems systems)
        {
            //todo: remove after Debug directive removal in scope of a fix task.
            if (_Instance == null)
            {
                return;
            }

            _Instance._world = world;
            _Instance._systems = systems;
            _MetaNet = Service<MetaNet>.Get();

            _Instance.SetEcsWorld(world, systems);

            _Instance._debugPanelItems.SetEcsWorld(world, systems);
            _Instance._staminaInfiniteWrapper.SetEcsWorld(_Instance._globalWorld, world);
            _Instance._debugEnemyShieldToggle.SetEcsWorld(world);
            _Instance._debugPanelBotTypeLabels.SetEcsWorld(world);
            _Instance._debugSkipIntroPage.SetEcsWorld(world);
            _Instance.PrizesPage.SetEcsWorld(world);
        }

        async void SetEcsWorld(LocalEcsWorld world, EcsSystems systems)
        {
            PlayerSession session = Service<PlayerManager>.Get().Session;
            bool isTutorial = session.RegionId == 0;

            if (_currentScene == _REGION_SCENE_ && !isTutorial)
            {
                // init / reset / restore state

                if (!areBotSettingsInit)
                {
                    ResetBotSettingsMethod(session.Progress);
                    areBotSettingsInit = true;
                }

                // restore cheats
                SetLabel(_enableInvincibilityLabelRegion, _isPlayerInvincible, _ENABLE_INVINCIBILITY_TEXT_, _DISABLE_INVINCIBILITY_TEXT_);
                SetPlayerRegionInvincibility(_isPlayerInvincible);

                AddingKillsProgress.SetActive(false);
            }

            if (_currentScene == _BATTLE_SCENE_)
            {
                // restore cheats
                SetLabel(EnableInvincibilityLabelBattle, _isPlayerInvincible, _ENABLE_INVINCIBILITY_TEXT_, _DISABLE_INVINCIBILITY_TEXT_);
                if (_isPlayerInvincible)
                {
                    EnablePlayerInvincibilityMethodBattle();
                }

                // connect AI
                var (tai, mai) = GetOpponentBattleAI();
                BotAiSettings.Init(tai, mai);
            }

            if (_currentScene == _REGION_SCENE_ || _currentScene == _MARKET_SCENE_)
            {
                ProgressView.Init(_MetaNet);
                CancellationTokenSource cts = new CancellationTokenSource();
                await ProgressView.Set(session, cts.Token);
            }
        }

        async void Start()
        {
            _Instance = this;
            DontDestroyOnLoad(gameObject);

            SceneLoader sceneLoader = Service<SceneLoader>.Get();
            GlobalWorld globalWorld = Service<GlobalWorld>.Get();

            _globalWorld = globalWorld.World;
            globalWorld.LocalWorldStarted += SetWorld;

            _canvas = GetComponent<Canvas>();
            _playerManager = Service<PlayerManager>.Get();
            Panel.anchoredPosition = new Vector2(_PANEL_CLOSED_POSITION_X_, 0f);
            FindObjectOfType<EventSystem>();
            _generalPage.SetActive(true);

            _openCloseButton.onClick.AddListener(SwitchVisibility);

            // build version.
            AppVersion clientVersion = await AppVersionProvider.GetClientVersion();
            _versionLbl.text = $"build {clientVersion.Hash}:{clientVersion.Date}:{clientVersion.Branch}";

            SceneManager.sceneLoaded += (scene, mode) => { SceneLoaded(scene); };

            // get labels from buttons
            _fpsEnableLabel = _fpsEnableButton.GetComponentInChildren<TextMeshProUGUI>();
            _shieldTouchLabel = _shieldTouchButton.GetComponentInChildren<TextMeshProUGUI>();
            EnableInvincibilityLabelBattle = EnableInvincibilityBattle.GetComponentInChildren<TextMeshProUGUI>();
            _enableInvincibilityLabelRegion = EnableInvincibilityRegion.GetComponentInChildren<TextMeshProUGUI>();
            AddingKillsProgressLabel = AddingKillsProgress.GetComponentInChildren<TextMeshProUGUI>();

            // General settings
            _fpsEnableButton.onClick.AddListener(
                () =>
                {
                    var isEnabled = !_fpsCounter.enabled;
                    _fpsCounter.enabled = isEnabled;
                    _fpsEnableLabel.text = isEnabled ? HideFpsText : ShowFpsText;
                });

            InitializeTutorialPanel(sceneLoader);

            _shieldTouchSettings = Resources.Load<TouchSettings>("DebugPanel/ShieldTouchSettings");
            _shieldTouchLabel.text = _shieldTouchSettings.TouchNumber <= 1 ? ShieldDoubleText : ShieldSingleText;
            _shieldTouchButton.onClick.AddListener(
                () =>
                {
                    _shieldTouchSettings.TouchNumber = _shieldTouchSettings.TouchNumber <= 1 ? 2 : 1;
                    _shieldTouchLabel.text = _shieldTouchSettings.TouchNumber <= 1 ? ShieldDoubleText : ShieldSingleText;
                });

            _resetGameSavesBtn.onClick.AddListener(
                () =>
                {
                    PlayerPrefs.DeleteAll();
#if UNITY_EDITOR
                    EditorApplication.isPlaying = false;
#else
                    Application.Quit();
#endif
                });

            _itemsOpenPageButton.onClick.AddListener(
                () =>
                {
                    var isItemsOpen = _itemsPage.activeSelf;
                    _generalPage.SetActive(isItemsOpen);
                    _debugPanelItems.OpenItemsPanel(!isItemsOpen);
                });
            
            _timeOpenPageButton.onClick.AddListener(
                () =>
                {
                    bool isOpen = _timePage.activeSelf;
                    _generalPage.SetActive(isOpen);
                    _timePage.SetActive(!isOpen);
                });

            _customTimeActualizer = new CustomTimeActualizer(Service<TimeService>.Get(), _timeSetterBlockView);
            _customTimeActualizer.ActualizeCustomTimeFromSave();
            InvokeRepeating(nameof(_customTimeActualizer.SaveTime), 0, 30); // save this value every 30 sec
            
            _crashBtn.onClick.AddListener(() => throw new Exception("Test Exception"));

            // visibility
            _visibilityButton.onClick.AddListener(CheckPlayerVisibility);

            // Market
            _enterWheel.onClick.AddListener(() => { sceneLoader.Load(new WheelSceneLoadData(), SceneHistoryMode.SaveInHistory); });
            _skipAllBoosts.onClick.AddListener(SkipAllBoosts);
            _unbindFacebookButton.onClick.AddListener(UnbindFacebook);
            _unbindGoogleButton.onClick.AddListener(UnbindGoogle);
            _toBattleBtn.onClick.AddListener(EnterTestBattle);
            _toLeagueBtn.onClick.AddListener(EnterLeague);

            // Battle
            WinBattleBtn.onClick.AddListener(
                () =>
                {
                    int dropDownValue = DamageTypeDropDown.value;
                    var damagePart = (FullBodyPart)Enum.Parse(typeof(FullBodyPart), DamageTypeDropDown.options[dropDownValue].text);

                    EcsEntity botEntity = _world.GetFilter<EcsFilter<FPVUnit>.Exclude<PlayerFlag>>().GetEntity(0);
                    botEntity.Get<UnitDead>();

                    _world.NewEntity().Get<BodyHitWinGoalResultFromDebug>().DamagedPart = damagePart;
                });

            LoseBattleBtn.onClick.AddListener(
                () =>
                {
                    EcsEntity playerEntity = _world.GetFilter<EcsFilter<FPVUnit, PlayerFlag>>().GetEntity(0);
                    playerEntity.Get<UnitDead>();
                });

            EnableInvincibilityBattle.onClick.AddListener(
                () =>
                {
                    if (_isPlayerInvincible)
                    {
                        DisablePlayerInvincibilityMethodBattle();
                    }
                    else
                    {
                        EnablePlayerInvincibilityMethodBattle();
                    }

                    SetLabel(EnableInvincibilityLabelBattle, _isPlayerInvincible, _ENABLE_INVINCIBILITY_TEXT_, _DISABLE_INVINCIBILITY_TEXT_);
                });


            ConfigureDamagingButtons();


            _cameraSettingsContainer.SetActive(false);
            _cameraSettingsToggle.onValueChanged.AddListener(ConfigureCameraHeadbob);


            // Region
            AddOneKill.onClick.AddListener(() => { MakeKills(1, Service<PlayerManager>.Get().Session, _MetaNet, Service<ItemData>.Get()); });
            AddTenKills.onClick.AddListener(() => { MakeKills(10, Service<PlayerManager>.Get().Session, _MetaNet, Service<ItemData>.Get()); });
            AddFiftyKills.onClick.AddListener(() => { MakeKills(50, Service<PlayerManager>.Get().Session, _MetaNet, Service<ItemData>.Get()); });

            // bot spawner
            BotRank.Init(true, DebugData.BotRanksRange);
            BotOutfit.Init(true, DebugData.BotOutfitsRange);
            BotShuriken.Init(true, DebugData.BotShurikensRange);
            BotShurikenSkin.Init(false, DebugData.BotShurikenSkins);
            BotAmulet.Init(false, DebugData.BotAmulets);
            BotStone.Init(false, DebugData.BotStones);
            BotBomb.Init(true, DebugData.BotBombs);
            BotHook.Init(true, DebugData.BotHooks);

            EnableInvincibilityRegion.onClick.AddListener(
                () =>
                {
                    SetPlayerRegionInvincibility(!_isPlayerInvincible);
                    SetLabel(_enableInvincibilityLabelRegion, _isPlayerInvincible, _ENABLE_INVINCIBILITY_TEXT_, _DISABLE_INVINCIBILITY_TEXT_);
                });

            ResetBotSettings.onClick.AddListener(
                () =>
                {
                    var session = Service<PlayerManager>.Get().Session;
                    ResetBotSettingsMethod(session.Progress);
                });

            RandomBotSettings.onClick.AddListener(()=>RandomBotSettingsMethod());
            
            SpawnCorpse.onClick.AddListener(SpawnCorpseSettingsMethod);

            ConfigureBotSpawnButtons();

            TraumaSelector.options = Enum.GetNames(typeof(BodyPart))
                                         .Where(trauma => BodyPart.Unknown.ToString() != trauma)
                                         .Select(bodyPart => new Dropdown.OptionData(bodyPart))
                                         .ToList();

            AddTraumaButton.onClick.AddListener(
                async () =>
                {
                    if (Enum.TryParse<BodyPart>(TraumaSelector.options[TraumaSelector.value].text, out var result))
                    {
                        switch (_currentScene)
                        {
                            case Idents.Scenes.Alchemist:
                            case _REGION_SCENE_:
                                Result<ClientTraumaAddResponse> response = await _MetaNet.AddTrauma(result);
                                _playerManager.Session.Timers.ChangeTimer(response.Value.Timer);
                                if (_currentScene == Idents.Scenes.Alchemist)
                                {
                                    sceneLoader.ReloadCurrent();
                                }

                                break;
                            case _BATTLE_SCENE_:
                                var playerFilter = (EcsFilter<FPVUnit, PlayerFlag>)_world.GetFilter(typeof(EcsFilter<FPVUnit, PlayerFlag>));
                                var enemyFilter = (EcsFilter<FPVUnit>.Exclude<PlayerFlag>)_world.GetFilter(typeof(EcsFilter<FPVUnit>.Exclude<PlayerFlag>));
                                EcsEntity playerUnit = playerFilter.GetEntity(0);
                                EcsEntity enemyUnit = enemyFilter.GetEntity(0);
                                // If you need add trauma to enemy swap the comments
                                int traumaId = TimersIdents.EffectIds[(int)result - 1];
                                _world.NewEntity().Get<AddTraumaRequest>().Construct(traumaId, playerUnit);
                                //_world.NewEntity().Get<AddTraumaRequest>().Construct(traumaId, enemyUnit);
                                break;
                        }
                    }
                });

            _speedModifierSlider.onValueChanged.AddListener(SetSpeedModifier);

            // RankMinusButton.onClick.AddListener (() => {
            //     var playerManager = Service<PlayerManager>.Get ();
            //     if (playerManager.Session.Progress.Rank > 0) {
            //         playerManager.Session.Progress.Rank--;
            //         RankNumberLabel.text = playerManager.Session.Progress.Rank.ToString ();
            //     }
            // });
            // RankPlusButton.onClick.AddListener (() => {
            //     var session = Service<PlayerManager>.Get ().Session;
            //     if (session.Progress.Rank < 13) {
            //         session.Progress.Rank++;
            //         RankNumberLabel.text = session.Progress.Rank.ToString ();
            //     }
            // });
            // RegionMinusButton.onClick.AddListener (() => {
            //     var session = Service<PlayerManager>.Get ().Session;
            //     session.RegionId--;
            //     session.RegionId = Math.Max (session.RegionId, 0);
            //     ToBattleButton.gameObject.SetActive (Array.IndexOf (AllowedBattleRegion, session.RegionId) > -1);
            // });
            // RegionPlusButton.onClick.AddListener (() => {
            //     var session = Service<PlayerManager>.Get ().Session;
            //     session.RegionId++;
            //     session.RegionId = Math.Min (session.RegionId, 3);
            //     ToBattleButton.gameObject.SetActive (Array.IndexOf (AllowedBattleRegion, session.RegionId) > -1);
            // });
            // ToRegionButton.onClick.AddListener (() => {
            //     var metaNetwork = Service<MetaNetwork>.Get ();
            //     if (metaNetwork) {
            //         var session = Service<PlayerManager>.Get ().Session;
            //         metaNetwork.EnterRegion (session.RegionId);
            //     }
            //     Service<SoundManager>.Get ().ProcessMasterVolume (1f, 0f, 0.5f);
            //     Service<LoaderFade>.Get ().Process (Color.clear, Color.black, 0.5f, (c) => {
            //         Service<SceneLoader>.Get ().ClearHistory ();
            //         Service<SceneLoader>.Get ().Load (Idents.Scenes.Region);
            //     });
            // });
            // BattleMinusButton.onClick.AddListener (() => {
            //     var session = Service<PlayerManager>.Get ().Session;
            //     if (session.BattleId > 0) {
            //         session.BattleId--;
            //         BattleNumberLabel.text = session.BattleId.ToString ();
            //     }
            // });
            // BattlePlusButton.onClick.AddListener (() => {
            //     var session = Service<PlayerManager>.Get ().Session;
            //     if (session.BattleId < 8) {
            //         session.BattleId++;
            //         BattleNumberLabel.text = session.BattleId.ToString ();
            //     }
            // });
            // ToBattleButton.onClick.AddListener (() => {
            //     var metaNetwork = Service<MetaNetwork>.Get ();
            //     if (metaNetwork) {
            //         var session = Service<PlayerManager>.Get ().Session;
            //         metaNetwork.RequestDuel (0, session.RegionId, session.BattleId);
            //     }
            //     /*
            //     var session = Service<PlayerManager>.Get ().Session;
            //     var correct = Array.IndexOf (AllowedBattleRegion, session.RegionId) > -1;
            //     if (correct) {
            //         Service<SoundManager>.Get ().ProcessMasterVolume (1f, 0f, 0.5f);
            //         Service<LoaderFade>.Get ().Process (Color.clear, Color.black, 0.5f, (c) => {
            //             Service<SceneLoader>.Get ().ClearHistory ();
            //             Service<SceneLoader>.Get ().Load (Idents.Scenes.Battle);
            //         });
            //     }
            //     */
            // });
            // ToMarketButton.onClick.AddListener (() => {
            //     Service<SoundManager>.Get ().ProcessMasterVolume (1f, 0f, 0.5f);
            //     Service<LoaderFade>.Get ().Process (Color.clear, Color.black, 0.5f, (c) => {
            //         Service<SceneLoader>.Get ().ClearHistory ();
            //         Service<SceneLoader>.Get ().Load (Idents.Scenes.Market);
            //     });
            // });
            // FpsLimitButton.onClick.AddListener (() => {
            //     FpsLimitLabel.text = "FPS:\n" + (Application.targetFrameRate = Application.targetFrameRate == 30 ? 60 : 30).ToString ();
            // });
            // PingEnableButton.onClick.AddListener (() => {
            //     if (PingLabel.rectTransform.anchoredPosition.x == 500.0f) {
            //         PingLabel.rectTransform.anchoredPosition = new Vector2 (-33.0f, PingLabel.rectTransform.anchoredPosition.y);
            //         PingEnableLabel.text = "HIDE\nPING";
            //     } else {
            //         PingLabel.rectTransform.anchoredPosition = new Vector2 (500.0f, PingLabel.rectTransform.anchoredPosition.y);
            //         PingEnableLabel.text = "SHOW\nPING";
            //     }
            // });

            // NetworkButton.onClick.AddListener (() => {
            //     var pm = Service<PlayerManager>.Get ();
            //     pm.Session.NoNetwork = !pm.Session.NoNetwork;
            //     NetworkButton.GetComponentInChildren<TMP_Text> ().text = pm.Session.NoNetwork ? "Network disabled" : "Network enabled";
            // });
            // NetworkButton.GetComponentInChildren<TMP_Text> ().text = Service<PlayerManager>.Get ().Session.NoNetwork ? "Network disabled" : "Network enabled";

            // Battle specific settings
            // AIMovingToggle.onClick.AddListener (() => {
            //     _battleAIMoving = !_battleAIMoving;
            //     if (_battleAIMoving) {
            //         AIMovingToggle.image.color = new Color (0.5f, 1.0f, 0.5f, 1.0f);
            //     } else {
            //         AIMovingToggle.image.color = new Color (1.0f, 0.5f, 0.5f, 1.0f);
            //     }
            //     if (_ecsWorld != null) {
            //         var units = (EcsFilter<Unit>) _ecsWorld.GetFilter (typeof (EcsFilter<Unit>));
            //         foreach (var idx in units) {
            //             ref var unit = ref units.Get1 (idx);
            //             if (_battleAIMoving) {
            //                 unit.Weak = true;
            //             } else {
            //                 unit.Weak = false;
            //             }
            //         }
            //     }
            // });
            // AIThrowToggle.onClick.AddListener (() => {
            //     _battleAIThrowing = !_battleAIThrowing;
            //     if (_battleAIThrowing) {
            //         AIThrowToggle.image.color = new Color (0.5f, 1.0f, 0.5f, 1.0f);
            //     } else {
            //         AIThrowToggle.image.color = new Color (1.0f, 0.5f, 0.5f, 1.0f);
            //     }
            //     if (_ecsWorld != null) {
            //         var units = (EcsFilter<Unit>) _ecsWorld.GetFilter (typeof (EcsFilter<Unit>));
            //         foreach (var idx in units) {
            //             if (!units.GetEntity (idx).Has<UnitPlayerFlag> ()) {
            //                 ref var unit = ref units.Get1 (idx);
            //                 if (_battleAIThrowing) {
            //                     unit.Shuriken.StaminaRegeneration *= 1000.0f;
            //                     unit.Stamina = 1.0f;
            //                 } else {
            //                     unit.Shuriken.StaminaRegeneration *= 0.001f;
            //                     unit.Stamina = 0.0f;
            //                 }
            //             }
            //         }
            //     }
            // });
            // EnemyShurikenMinusButton.onClick.AddListener (() => {
            //     if (DebugData.EnemyWeaponId > 11001) {
            //         DebugData.EnemyWeaponId--;
            //         EnemyShurikenNumberLabel.text = DebugData.EnemyWeaponId.ToString ();
            //     }
            // });
            // EnemyShurikenPlusButton.onClick.AddListener (() => {
            //     if (DebugData.EnemyWeaponId < 11010) {
            //         DebugData.EnemyWeaponId++;
            //         EnemyShurikenNumberLabel.text = DebugData.EnemyWeaponId.ToString ();
            //     }
            // });
            // EnemyAmuletMinusButton.onClick.AddListener (() => {
            //     if (DebugData.EnemyAmuletId > 17001) {
            //         DebugData.EnemyAmuletId--;
            //         EnemyAmuletNumberLabel.text = DebugData.EnemyAmuletId.ToString ();
            //     }
            // });
            // EnemyAmuletPlusButton.onClick.AddListener (() => {
            //     if (DebugData.EnemyAmuletId < 17004) {
            //         DebugData.EnemyAmuletId++;
            //         EnemyAmuletNumberLabel.text = DebugData.EnemyAmuletId.ToString ();
            //     }
            // });

            // Market specific settings
            // ClothMinusButton.onClick.AddListener (() => {
            //     var session = Service<PlayerManager>.Get ().Session;
            //     if (session.Progress.OutfitId > 10001) {
            //         session.Progress.OutfitId--;
            //         ClothLabel.text = session.Progress.OutfitId.ToString ();
            //     }
            // });
            // ClothPlusButton.onClick.AddListener (() => {
            //     var session = Service<PlayerManager>.Get ().Session;
            //     if (session.Progress.OutfitId < 10016) {
            //         session.Progress.OutfitId++;
            //         ClothLabel.text = session.Progress.OutfitId.ToString ();
            //     }
            // });
            // GetBloodButton.onClick.AddListener (() => {
            //     var session = Service<PlayerManager>.Get ().Session;
            //     var unit = _ecsWorld.GetFilter<EcsFilter<LocalLogic.Battle2.Unit, LocalLogic.Battle2.UnitPlayerFlag>> ();
            //     if (unit.IsEmpty ()) {
            //         var newUnitEntity = _ecsWorld.CreateEntityWith<LocalLogic.Battle2.Unit> (out var newUnit);
            //         _ecsWorld.AddComponent<LocalLogic.Battle2.UnitPlayerFlag> (newUnitEntity);
            //         newUnit.Health = 1f;
            //     }
            //     _ecsWorld.CreateEntityWith<BattleEnd> (out _);
            //     BloodLabel.text = "Blood: " + (session.Profile.Blood + 1);
            // });

            // ToWheelButton.onClick.AddListener (() => {
            //     var session = Service<PlayerManager>.Get ().Session;
            //     Service<SoundManager>.Get ().ProcessMasterVolume (1f, 0f, 0.5f);
            //     Service<LoaderFade>.Get ().Process (Color.clear, Color.black, 0.5f, (c) => {
            //         Service<SceneLoader>.Get ().ClearHistory ();
            //         Service<SceneLoader>.Get ().Load (Idents.Scenes.BattleResult);
            //     });
            // });
        }

        private void EnterLeague()
        {
            Service<SceneLoader>.Get().Load(new LeagueSceneLoadData(Idents.Scenes.Leagues));
        }

        private void UnbindFacebook()
        {
            FacebookAuthService facebookAuthService = Service<FacebookAuthService>.Get();
            Unbind(facebookAuthService.Token).Forget();

            async UniTaskVoid Unbind(string facebookId)
            {
                if (string.IsNullOrEmpty(facebookId))
                {
                    GameLogger.LogWarning("No facebook profile is linked to this account");

                    return;
                }
                
                await facebookAuthService.Unbind(facebookId);
                _unbindFacebookButton.GetComponent<Image>().color = ColorX.HexToColor("74EC6E");
            }
        }
        
        private void UnbindGoogle()
        {
            GoogleAuthService googleAuthService = Service<GoogleAuthService>.Get();
            Unbind(googleAuthService.Token).Forget();

            async UniTaskVoid Unbind(string googleId)
            {
                if (string.IsNullOrEmpty(googleId))
                {
                    GameLogger.LogWarning("No google profile is linked to this account");
                    return;
                }
                
                await googleAuthService.Unbind(googleId);
                _unbindGoogleButton.GetComponent<Image>().color = ColorX.HexToColor("74EC6E");
            }
        }

        private void EnterTestBattle()
        {
            BattleFeatureConfig battleConfig = BattleFeatureConfig.Default();
            battleConfig.Offline = true;
            _world.NewEntity().Get<DuelBeginRequest>().Construct(
                new Rival()
                {
                    RivalName = FtueConditions.BOT_NAME_FOR_TUTORIAL_BATTLES,
                    RivalCountry = FtueConditions.BOT_COUNTRY_FOR_TUTORIAL_BATTLES,
                    RivalAmulet = 17001,
                    RivalShuriken = 11001,
                    RivalPowerStone = new PowerStone(new ItemQuantity(70002, 10)),
                    RivalBattleRank = 1,
                    RivalVisualRank = 1,
                    RivalSkin = 10001,
                    RivalShurikenSkin = 12005,
                    RivalTraumas = new List<int>()
                }, battleConfig);
        }

        private async void CheckPlayerVisibility()
        {
            if (_visibilityCamera == null || _visibilityCamera != Camera.main)
            {
                _visibilityCamera = Camera.main;
                _visibilityChecker = await VisibilityCheckerByCamera.Construct(_visibilityCamera, Service<ResourceLoadingService>.Get());
            }

            var playerFilter = (EcsFilter<TopDownUnit, PlayerFlag>)_world.GetFilter(typeof(EcsFilter<TopDownUnit, PlayerFlag>));
            TopDownUnit player = playerFilter.Get1(0);
            SkinnedMeshRenderer playerRenderer = player.Visual.GetFirstSkin();
            float visibility = await _visibilityChecker.GetVisibility(playerRenderer);
            _visibilityLabel.text = $"{visibility * 100:0.00}%";
        }

        private void ConfigureBotSpawnButtons()
        {
            string[] regionAiTypeNamesArray = Enum.GetNames(typeof(RegionAIType));

            List<TMP_Dropdown.OptionData> regionOptionsList = regionAiTypeNamesArray
                                                              .Where(aiType => RegionAIType.None.ToString() != aiType)
                                                              .Select(aiType => new TMP_Dropdown.OptionData(aiType))
                                                              .ToList();

            _regionBotTypeDropdown.ClearOptions();
            _regionBotTypeDropdown.AddOptions(regionOptionsList);

            int regionTypeIndex = regionOptionsList.FindIndex(x => x.text == RegionAIType.AFK.ToString());
            _regionBotTypeDropdown.SetValueWithoutNotify(regionTypeIndex);

            string[] battleAiTypeNamesArray = Enum.GetNames(typeof(BattleAIType));

            List<TMP_Dropdown.OptionData> battleOptionsList = battleAiTypeNamesArray
                                                              .Select(aiType => new TMP_Dropdown.OptionData(aiType))
                                                              .ToList();

            _battleBotTypeDropdown.ClearOptions();
            _battleBotTypeDropdown.AddOptions(battleOptionsList);

            int battleTypeIndex = battleOptionsList.FindIndex(x => x.text == BattleAIType.Standard.ToString());
            _battleBotTypeDropdown.SetValueWithoutNotify(battleTypeIndex);

            SpawnBot.onClick.AddListener(SpawnBotWithParams);

            _regionBotTypeToggle.SetIsOnWithoutNotify(false);
            _battleBotTypeToggle.SetIsOnWithoutNotify(false);
        }

        private void SpawnBotWithParams()
        {
            EcsFilter<TopDownUnit, PlayerFlag> users = (EcsFilter<TopDownUnit, PlayerFlag>)_world.GetFilter(typeof(EcsFilter<TopDownUnit, PlayerFlag>));
            
            RanksInfoProvider ranksInfoProvider = Service<RanksInfoProvider>.Get();
            GoogleDocsData googleDocsData = Service<GoogleDocsData>.Get();
            WinRateHelper winRateHelper = new(_playerManager.Session.Progress, googleDocsData);
            int winRate = winRateHelper.GetWinRateType();
            
            StandardBotRankProvider standardBotRankProvider = new (ranksInfoProvider, _playerManager.Session.Progress, googleDocsData, winRate);
            ExcludingTraumaGenerator excludingTraumaGenerator = new (googleDocsData, ranksInfoProvider, winRate);

            Vector3 userPoint = default;
            Vector3 userDirection = default;

            foreach (int idx in users)
            {
                userPoint = users.Get1(idx).Position;
                userDirection = users.Get1(idx).Direction;
            }

            userPoint.y = 0f;

            EcsEntity botEntity = _world.NewEntity();
            ref UnitSpawn spawn = ref botEntity.Get<UnitSpawn>();

            spawn.Rank = !_debugPanelBotSettingsPage.IsOn ? standardBotRankProvider.GetRankId() : BotRank.GetValue();
            spawn.TraumasId = !_debugPanelBotSettingsPage.IsOn ? excludingTraumaGenerator.GetTraumas(spawn.Rank) : new List<int>() ;

            if (!_debugPanelBotSettingsPage.IsOn)
            {
                RandomBotSettingsMethod(spawn.Rank);
            }

            spawn.OutfitId = BotOutfit.GetValue();
            spawn.ShurikenId = BotShuriken.GetValue();
            spawn.ShurikenSkinId = BotShurikenSkin.GetValue();
            spawn.AmuletId = BotAmulet.GetValue();
            spawn.SpawnPosition = userPoint + userDirection * 2;
            spawn.SpawnDirection = userDirection;

            PowerStone botPowerStone = BotStone.GetValue() == 0 ? PowerStone.Absent : new PowerStone(BotStone.GetValue(), 3);
            spawn.AddPocketItem(PocketType.PowerStone, botPowerStone.Item);
            ItemQuantity botBomb = new(BotBomb.GetValue(), 10);
            spawn.AddPocketItem(PocketType.Bomb, botBomb);
            ItemQuantity botHook = new(BotHook.GetValue(), 10);
            spawn.AddPocketItem(PocketType.Hook, botHook);

            ref AiSpawnData aiSpawnData = ref botEntity.Get<AiSpawnData>();
            aiSpawnData.AiType = (RegionAIType)Enum.Parse(typeof(RegionAIType), _regionBotTypeDropdown.options[_regionBotTypeDropdown.value].text);
            aiSpawnData.DisableAi = _regionBotTypeToggle.isOn;
                    
            GameLogger.Log(
                $"[DebugPanel] spawn bot: rank: {spawn.Rank}, outfit: {spawn.OutfitId}, shuriken: {spawn.ShurikenId}, " +
                $"shurikenSkin: {spawn.ShurikenSkinId}, amulet: {spawn.AmuletId}, type: {aiSpawnData.AiType}");


            if (_battleBotTypeToggle.isOn)
            {
                if (!_debugDuelBeginEntity.IsAlive())
                {
                    _debugDuelBeginEntity = _globalWorld.NewEntity();
                }

                BattleAIType type = (BattleAIType) Enum.Parse(typeof(BattleAIType), _battleBotTypeDropdown.options[_battleBotTypeDropdown.value].text);

                _debugDuelBeginEntity.Get<DebugDuelBattleAiType>().Type = type;
            }
        }

        private void ConfigureDamagingButtons()
        {
            DamageTypeDropDown.ClearOptions();

            var dropOptions = Enum.GetNames(typeof(FullBodyPart))
                                  .Where(trauma => FullBodyPart.UNDEFINED.ToString() != trauma)
                                  .Select(bodyPart => new TMP_Dropdown.OptionData(bodyPart))
                                  .ToList();

            DamageTypeDropDown.options = dropOptions;

            void DealDamageSettings()
            {
                ref Damaging damaging = ref _world.NewEntity().Get<Damaging>();

                var playerFilter = (EcsFilter<FPVUnit, PlayerFlag>)_world.GetFilter(typeof(EcsFilter<FPVUnit, PlayerFlag>));
                var enemyFilter = (EcsFilter<FPVUnit>.Exclude<PlayerFlag>)_world.GetFilter(typeof(EcsFilter<FPVUnit>.Exclude<PlayerFlag>));

                FPVUnit playerunit = playerFilter.Get1(0);
                FPVUnit enemyunit = enemyFilter.Get1(0);

                damaging.Calculated = true;

                damaging.Construct(50);

                damaging.Type = Damaging.DamageType.BASE;

                damaging.HitPoint = Vector3.zero;

                damaging.Invoker = playerunit.Entity;
                damaging.Receiver = enemyunit.Entity;

                int dropDownValue = DamageTypeDropDown.value;
                damaging.Part = (FullBodyPart)Enum.Parse(typeof(FullBodyPart), DamageTypeDropDown.options[dropDownValue].text);

                ref ShurikenSpawnEvent shurikenSpawnEvent = ref _world.NewEntity().Get<ShurikenSpawnEvent>();

                shurikenSpawnEvent.IsLaunch = true;
                shurikenSpawnEvent.IsPlayer = true;
                shurikenSpawnEvent.Start = Vector3.zero;
                shurikenSpawnEvent.End = Vector3.zero;
                shurikenSpawnEvent.BaseScale = 1;
                shurikenSpawnEvent.SideOffset = 0;
                shurikenSpawnEvent.ThrowSpeed = 0;
                shurikenSpawnEvent.BotTargetPart = damaging.Part;
            }

            DealDamageButton.onClick.AddListener(DealDamageSettings);
        }

        private void ConfigureCameraHeadbob(bool isOverriden)
        {
            _cameraSettingsContainer.SetActive(isOverriden);
            UnitCameraComponent unitCamera = GetUnitCamera();

            if (!isOverriden)
            {
                unitCamera.Data.Set(_originCameraData);
                return;
            }

            _originCameraData = unitCamera.Data.Clone();

            WalkStepAmplitude.Init(10f, 0f, 15f).AddListener(value => unitCamera.Data.WalkHeadbobData.Amplitude = value / WalkStepAmplitude.Precision);
            WalkStepFrequency.Init(6f, 0f, 15f).AddListener(value => unitCamera.Data.WalkHeadbobData.Frequency = value / WalkStepFrequency.Precision);
            WalkStepSpeed.Init(3f, 1f, 100f).AddListener(value => unitCamera.Data.WalkHeadbobData.Speed = value / WalkStepSpeed.Precision);
            IdleStepAmplitude.Init(3.2f, 0f, 10f).AddListener(value => unitCamera.Data.IdleHeadbobData.Amplitude = value / IdleStepAmplitude.Precision);
            IdleStepFrequency.Init(1.4f, 0f, 10f).AddListener(value => unitCamera.Data.IdleHeadbobData.Frequency = value / IdleStepFrequency.Precision);
            IdleStepSpeed.Init(2f, 1f, 100f).AddListener(value => unitCamera.Data.IdleHeadbobData.Speed = value / IdleStepSpeed.Precision);
            CameraVerticalOffset.Init(16f, -180f, 180f).AddListener(value => unitCamera.Data.Configuration.VerticalAngle = value / CameraVerticalOffset.Precision);
        }

        public static UnitCameraComponent GetUnitCamera() => FindObjectOfType<UnitCameraComponent>();

        private void InitializeTutorialPanel(SceneLoader sceneLoader)
        {
            List<string> steps = FtueProgressConverter.GetAllSteps();

            SkipTutorialSelector.options = steps.Select(x => new Dropdown.OptionData(x)).ToList();
            SkipTutorialSelector.value = 15;

            SkipTutorial.onClick.AddListener(
                () => OverrideFtue(sceneLoader, steps).Forget() );
        }

        private async UniTask OverrideFtue(SceneLoader sceneLoader, List<string> steps)
        {
            List<string> newSteps = new List<string>();
            List<AccountStep> majorSteps = new List<AccountStep>();
            foreach (string step in steps.Take(SkipTutorialSelector.value + 1))
            {
                if (IsMajor(step))
                {
                    majorSteps.Add(Enum.Parse<AccountStep>(TrimmedStep(step)));
                }
                else
                {
                    newSteps.Add(step);
                }

                if (step == nameof(FtueProgressKeys.DailyBonusClaimed))
                {
                    Service<DailyBonusService>.Get().NextBonusTime = Service<ITimeGiver>.Get().UtcNow;
                }
            }

            FtueProgress ftueProgress = GetScopeService<FtueProgress>();
            await ftueProgress.OverrideProgress(newSteps, majorSteps);

            var sceneData = new ConnectionNextSceneProvider(_playerManager.Session, ftueProgress).GetNextSceneData();
            sceneLoader.Load(sceneData, SceneHistoryMode.ClearHistory);

            bool IsMajor(string step)
            {
                return step.Contains("<");
            }

            string TrimmedStep(string step)
            {
                if (!step.Contains("<"))
                {
                    return step;
                }

                int length = step.Length - "<color=#FF0000>".Length - "</color>".Length;
                return step.Substring("<color=#FF0000>".Length, length);
            }
        }

        private void EnablePlayerInvincibilityMethodBattle()
        {
            var entity = _world.NewEntity();
            entity.Get<CheatUnitInvincibility>().Construct(InvincibilityMode.RegenerateOnZero, true);
            _isPlayerInvincible = true;
        }

        private void DisablePlayerInvincibilityMethodBattle()
        {
            var invincibilityCheats = (EcsFilter<CheatUnitInvincibility>)_world.GetFilter(typeof(EcsFilter<CheatUnitInvincibility>));
            foreach (var idx in invincibilityCheats)
            {
                if (invincibilityCheats.Get1(idx).IsPlayer)
                {
                    invincibilityCheats.GetEntity(idx).Destroy();
                }
            }

            _isPlayerInvincible = false;
        }

        private void SetPlayerRegionInvincibility(bool isInvincible)
        {
            var playerFilter = _world.GetFilter<EcsFilter<TopDownUnit, PlayerFlag>>();

            foreach (var index in playerFilter)
            {
                if (isInvincible)
                {
                    playerFilter.GetEntity(index).Get<HookInvincibility>();
                }
                else
                {
                    playerFilter.GetEntity(index).Del<HookInvincibility>();
                }
            }

            _isPlayerInvincible = isInvincible;
        }

        (AiThrowData throwingAi, AiMovementData movementAi) GetOpponentBattleAI()
        {
            AiThrowData tai = default;
            AiMovementData mai = default;
            var opponents = (EcsFilter<BattleAi, Shield>.Exclude<PlayerFlag>)_world.GetFilter(typeof(EcsFilter<BattleAi, Shield>.Exclude<PlayerFlag>));
            foreach (var idx in opponents)
            {
                ref BattleAi unit = ref opponents.Get1(idx);
                tai = unit.ThrowData;
                mai = unit.MovementData;
                break;
            }

            return (tai, mai);
        }

        static void SetLabel(TextMeshProUGUI label, bool isEnabled, string enableText, string disableText)
        {
            label.text = isEnabled ? disableText : enableText;
        }

        void ResetBotSettingsMethod(PlayerProgress playerProgress)
        {
            BotRank.SetValue(playerProgress.Rank);
            BotOutfit.SetValue(playerProgress.OutfitId);
            BotShuriken.SetValue(playerProgress.WeaponId);
            BotShurikenSkin.SetValue(playerProgress.WeaponSkinId);
            BotAmulet.SetValue(playerProgress.AmuletId);
            BotStone.SetValue(playerProgress.Pockets.PowerStone.Id == PlayerInventory.Items.POWER_STONE_ABSENT ? 0 : playerProgress.Pockets.PowerStone.Id);
            BotHook.SetValue(playerProgress.Pockets.Hook.Id);
            BotBomb.SetValue(playerProgress.Pockets.Bomb.Id);

            string[] regionAiTypeNamesArray = Enum.GetNames(typeof(RegionAIType));

            List<TMP_Dropdown.OptionData> regionOptionsList = regionAiTypeNamesArray
                                                              .Where(aiType => RegionAIType.None.ToString() != aiType)
                                                              .Select(aiType => new TMP_Dropdown.OptionData(aiType))
                                                              .ToList();
            int regionTypeIndex = regionOptionsList.FindIndex(x => x.text == RegionAIType.AFK.ToString());

            _regionBotTypeDropdown.SetValueWithoutNotify(regionTypeIndex);

            areBotSettingsInit = true;
        }

        void RandomBotSettingsMethod(int botSelectRank = -1)
        {
            ItemData itemData = Service<ItemData>.Get();
            PlayerSession playerSession = Service<PlayerManager>.Get().Session;
            GoogleDocsData googleDocsData = Service<GoogleDocsData>.Get();
            LifetimeScope scope = LifetimeScope.Find<RegionLifeTimeScope>();
            BotItemsData botItemsData = scope.Container.Resolve<BotItemsData>();
            IBotDifficultyProvider botDifficultyData = scope.Container.Resolve<IBotDifficultyProvider>();

            if (botItemsData == default)
            {
                return;
            }

            ItemProviderWeighted shurikenGenerator = new(ItemType.Shuriken, botDifficultyData, itemData);
            AmuletProviderWeighted amuletGenerator = new(ItemType.Amulet, botDifficultyData, itemData);
            SkinRandomGenerator skinsGenerator = new(playerSession.ProgressItems);
            
            FtueConditions ftueConditions = GetScopeService<FtueProgress>().Conditions;
            
            PowerStoneProvider powerStoneProvider = new(botDifficultyData, itemData,googleDocsData, ftueConditions);
            BombPocketProvider bombPocketProvider = new(botDifficultyData, itemData,googleDocsData, ftueConditions);
            HookPocketProvider hookPocketProvider = new(botDifficultyData, itemData,googleDocsData, ftueConditions);
            
            UnitSpawn spawn = botItemsData.GetStandardBotSpawn(
                playerSession.Progress.Rank,
                shurikenGenerator,
                amuletGenerator,
                skinsGenerator,
                powerStoneProvider,
                bombPocketProvider,
                hookPocketProvider,
                Vector3.zero,
                Quaternion.identity,
                botSelectRank,
                false);

            BotRank.SetValue(spawn.Rank);
            BotOutfit.SetValue(spawn.OutfitId);
            BotShuriken.SetValue(spawn.ShurikenId);
            BotShurikenSkin.SetValue(spawn.ShurikenSkinId);
            BotAmulet.SetValue(spawn.AmuletId);

            ItemQuantity botPowerStoneId = spawn.GetPocketItem(PocketType.PowerStone);
            BotStone.SetValue(botPowerStoneId.Id == PowerStone.Absent.Id ? 0 : botPowerStoneId.Id);
            BotBomb.SetValue(spawn.GetPocketItem(PocketType.Bomb).Id);
            BotHook.SetValue(spawn.GetPocketItem(PocketType.Hook).Id);

            areBotSettingsInit = true;
        }

        private void SpawnCorpseSettingsMethod()
        {
            GoogleDocsData googleDocsData = GetScopeService<GoogleDocsData>();
            ITimeGiver time = GetScopeService<ServerTimeService>();
            CorpseCreatorProvider creator = new(_world, googleDocsData, time);
            var playerFilter = _world.GetFilter<EcsFilter<TopDownUnit, PlayerFlag>>();
            creator.GetReal().Create(playerFilter.Get1(0).Position, Quaternion.identity);
        }

        async void MakeKills(int killsAmount, PlayerSession session, MetaNet metaNet, ItemData itemData)
        {
            if (killsAmount < 1)
            {
                return;
            }

            // hide buttons, show progress
            AddOneKill.gameObject.SetActive(false);
            AddTenKills.gameObject.SetActive(false);
            AddFiftyKills.gameObject.SetActive(false);
            AddingKillsProgress.SetActive(true);

            // for safety
            if (!_isPlayerInvincible)
            {
                SetPlayerRegionInvincibility(true);
                SetLabel(_enableInvincibilityLabelRegion, _isPlayerInvincible, _ENABLE_INVINCIBILITY_TEXT_, _DISABLE_INVINCIBILITY_TEXT_);
            }
            
            AddingKillsProgressLabel.text = $"adding kills... 0/{killsAmount.ToString()}";
            for (var i = 0; i < killsAmount; ++i)
            {
                string battleResultId = await EnterBattle(metaNet);
                if (battleResultId == null) return;

                const string country = "bs";
                HitBodyPart hits = new(){ Arm = 1, Body = 1, Head = 1, Hips = 1, Leg = 1 };
                int botRank = BotRank.GetValue();
                await EndBattle(
                    session, metaNet,
                    battleResultId, session.LocationId, session.RegionId,
                    botRank, BotOutfit.GetValue(), BotShuriken.GetValue(), country, hits, 100, 100, 0);
                session.RegionResultData.AddBattleHistory(new BattleHistoryData
                {
                    BattleWin = true,
                });
                AddingKillsProgressLabel.text = $"adding kills... {(i + 1).ToString()}/{killsAmount.ToString()}";

                if (await TryAddLoot(session, metaNet, botRank) == false) return;
            }

            session.RegionResultData.BattleResult = BattleResult.Win;
            AddingKillsProgressLabel.text = $"kills were added successfully";

            // show buttons, hide progress
            AddOneKill.gameObject.SetActive(true);
            AddTenKills.gameObject.SetActive(true);
            AddFiftyKills.gameObject.SetActive(true);
            AddingKillsProgress.SetActive(false);
        }

        private async Task<bool> TryAddLoot(PlayerSession session, MetaNet metaNet, int botRank)
        {
            Result<ExternalLootCorpseResponse> lootCorpse = await metaNet.GetLootCorpse(botRank);

            if (lootCorpse.IsFailure)
            {
                return false;
            }
            
            ItemComponentsToItemQuantitiesConverter itemComponentsToItemQuantitiesConverter = new();

            session.RegionResultData.RegionStorage.AddAnyReward(itemComponentsToItemQuantitiesConverter.Convert(lootCorpse.Value.Items.ToList()));

            return true;
        }

        static async Task<string> EnterBattle(MetaNet metaNet)
        {
            Rival enemy = Rival.Default();
            BattleOpponent battleOpponent = new()
            {
                Rank = (ushort)enemy.RivalBattleRank, Outfit = (ushort)enemy.RivalSkin,
                Country = enemy.RivalCountry, Shuriken = (ushort)enemy.RivalShuriken,
                Type = BattleOpponentType.Standard,
                Points = (ushort)0,
                Injuries = new BattleOpponentInjuries(),
                Damage = new BattleOpponentDamage()
            };
            Result<ClientBattleEnterResponse> enterBattle = await metaNet.EnterBattle(battleOpponent);

            if (enterBattle.IsSuccess)
            {
                return enterBattle.Value.Battle.Id;
            }

            return null;
        }

        async Task EndBattle(
            PlayerSession session, MetaNet metaNet, string battleResultId, int location, int region,
            int enemyRank, int enemyOutfit, int enemyShuriken, string enemyCountry, HitBodyPart inflictedHits, int playerHp, int playerDamageApply, int playerDamageReceive)
        {
            Result<ClientBattleResultResponse> battleResult = await metaNet.BattleResult(
                battleResultId,
                BattleResult.Win,
                new BattleLocation { Point = (byte)location, Region = (byte)region },
                new BattleOpponent
                {
                    Rank = (ushort)enemyRank, Outfit = (ushort)enemyOutfit,
                    Country = enemyCountry, Shuriken = (ushort)enemyShuriken,
                    Injuries = new BattleOpponentInjuries { Inflicted = inflictedHits, Received = default },
                    Damage = new BattleOpponentDamage
                    {
                        Inflicted = playerDamageReceive,
                        Received = playerDamageApply,
                    }
                },
                Array.Empty<SkinDurability>().ToList(), playerHp);

            if (battleResult.IsFailure)
            {
                return;
            }

            Result<BattleProgressResponse> battleProgress = await metaNet.GetBattleProgress();

            if (battleProgress.IsFailure)
            {
                return;
            }

            session.Progress.UpdateBattleProgress(battleResult.Value.Rank, battleProgress.Value.Points, battleProgress.Value.Win, battleProgress.Value.Lose);
        }

        private void SkipAllBoosts()
        {
            PerformSkipAllBoosts().Forget();
        }

        private async UniTaskVoid PerformSkipAllBoosts()
        {
            foreach (PlayerTimer timer in _playerManager.Session.Timers.GetTimers(PlayerTimers.Query.Get().WithTimerType(global::Common.TimerType.Boost)))
            {
                Result<ChangeTimerResponse> response = await _MetaNet.ChangeTimer(TimerType.Boost, timer.ItemId, (int)(-1000 * timer.GetRemainingUtcTimeInSeconds()));
                _playerManager.Session.Timers.ChangeTimer(response.Value.Timer);
            }
        }

        void SceneLoaded(Scene scene)
        {
            var session = Service<PlayerManager>.Get().Session;
            var isTutorial = session.RegionId == 0;

            BattlePage.SetActive(scene.name == _BATTLE_SCENE_);
            RegionPage.SetActive(scene.name == _REGION_SCENE_ && !isTutorial);
            TutorialPage.SetActive(scene.name == _REGION_SCENE_ && isTutorial);
            _marketPage.SetActive(scene.name == _MARKET_SCENE_);
            PrizesPage.OpenWrapper(scene.name == Idents.Scenes.Prizes);
            TraumaPage.SetActive(scene.name == Idents.Scenes.Alchemist || scene.name == _REGION_SCENE_ && !isTutorial);
            UserProgressPage.SetActive(scene.name == _REGION_SCENE_ || scene.name == _MARKET_SCENE_);

            _currentScene = scene.name;
            _canvas.gameObject.SetActive(true);
            _canvas.enabled = true;

            if (scene.name == _REGION_SCENE_)
            {
                UpdateResourcePanel();
            }
        }

        private void UpdateResourcePanel()
        {
            if (!_resourcePanel)
            {
                return;
            }

            _resourcePanel.Set(_playerManager.Session.Inventory.Soft.Amount, _playerManager.Session.Inventory.Hard.Amount, _playerManager.Session.Inventory.Rp.Amount);
        }

        private void Update()
        {
            Touch[] touches = _touchManager.GetTouches().Where(touch => touch.phase == TouchPhase.Began).ToArray();
            if (touches.Length >= 4 || Input.GetKeyUp("]"))
            {
                _openCloseButton.gameObject.SetActive(!_openCloseButton.gameObject.activeSelf);
            }
        }

        private void SwitchVisibility()
        {
            if (_startPanelAnchoredPosition.x == _PANEL_CLOSED_POSITION_X_)
            {
                _startPanelAnchoredPosition = new Vector2(_PANEL_OPEN_POSITION_X_, 0f);
            }
            else if (_startPanelAnchoredPosition.x == _PANEL_OPEN_POSITION_X_)
            {
                _startPanelAnchoredPosition = new Vector2(_PANEL_CLOSED_POSITION_X_, 0f);
            }

            Panel.anchoredPosition = _startPanelAnchoredPosition;
        }

        private void SetSpeedModifier(float value)
        {
            var playerFilter = (EcsFilter<TopDownUnit, PlayerFlag>)_world.GetFilter(typeof(EcsFilter<TopDownUnit, PlayerFlag>));
            ref TopDownUnit player = ref playerFilter.Get1(0);
            player.MovementSpeedModifier.Apply(value);
            _speedModifierValue.text = value.ToString("F1");
        }

        static T GetScopeService<T>()
        {
            return FindObjectOfType<LifetimeScope>().Container.Resolve<T>();
        }

        const BindingFlags Flags = BindingFlags.Instance | BindingFlags.GetProperty | BindingFlags.SetProperty | BindingFlags.GetField | BindingFlags.SetField |
                                   BindingFlags.NonPublic;

        static FieldInfo[] GetListOfFields(object obj, BindingFlags flags = BindingFlags.Default)
        {
            var type = obj.GetType();
            var fields = type.GetFields(Flags | flags);
            return fields;
        }
    }
}
#endif